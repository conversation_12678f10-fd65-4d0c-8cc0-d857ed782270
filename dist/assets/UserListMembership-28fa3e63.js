import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{M as se}from"./MembershipCard-f676b019.js";import{r as a,f as re,k as te,b as V}from"./vendor-851db8c1.js";import{H as ae,J as ie,G as oe,A as le,b,t as L,E as ne,P as k,d as ce,M as de}from"./index-13fd629e.js";import{B as me}from"./BackButton-11ba52b2.js";import{b as xe,c as he}from"./index.esm-c561e951.js";import{S as pe}from"./index.esm-92169588.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const ge=f=>{const x={size:24,className:"text-white"},h={Visa:{icon:e.jsx(xe,{...x}),bgColor:"bg-[#1A1F71]"},Mastercard:{icon:e.jsx(he,{...x}),bgColor:"bg-[#EB001B]"},"American Express":{icon:e.jsx(pe,{...x}),bgColor:"bg-[#006FCF]"},Discover:{text:"DISC",bgColor:"bg-[#FF6000]"},"Diners Club":{icon:e.jsx(ae,{...x}),bgColor:"bg-[#0069AA]"},JCB:{icon:e.jsx(ie,{...x}),bgColor:"bg-[#0B4EA2]"},UnionPay:{text:"UP",bgColor:"bg-[#00447C]"}}[f];return h?e.jsx("div",{className:`flex h-8 w-12 items-center justify-center rounded ${h.bgColor} text-white`,children:h.icon||e.jsx("span",{className:"text-sm font-bold",children:h.text})}):null};let m=new de;function es(){var U,Y;const[f,x]=a.useState([]),[_,h]=a.useState(!0),[z,B]=a.useState(!1),[R,ue]=a.useState(null),[r,W]=a.useState(null),[A,D]=a.useState(1),[o,H]=a.useState(null),[O,E]=a.useState(!1),y=re(),[F]=te(),{dispatch:c}=V.useContext(oe),{state:N,dispatch:w}=V.useContext(le),S=localStorage.getItem("user"),[l,q]=a.useState(null),[p,K]=a.useState(null),n=F.get("familyMemberId"),j=F.get("familyMemberName"),i=N.isFamilyMemberSwitch,g=N.familyMemberDetails,C=a.useMemo(()=>i&&N.user?N.user:n||S,[i,N.user,n,S]),I=a.useCallback(()=>i&&g?g.first_name:j||"Your",[i,g,j]),v=a.useCallback(()=>i&&g?`${g.first_name} ${g.last_name}`:j||"Your",[i,g,j]),$=a.useCallback(async()=>{var s,t,M;h(!0);try{m.setTable("user");const u=await m.callRestAPI({id:S},"GET");m.setTable("clubs");const d=await m.callRestAPI({id:(s=u==null?void 0:u.model)==null?void 0:s.club_id},"GET");q(d.model),console.log("view model response",(t=d==null?void 0:d.model)==null?void 0:t.membership_settings),x(JSON.parse((M=d==null?void 0:d.model)==null?void 0:M.membership_settings)||[])}catch(u){console.log(u)}finally{h(!1)}},[S]),P=a.useCallback(async()=>{var s;try{B(!0);const{data:t,error:M,message:u}=await m.getCustomerStripeCards();if(console.log(t),M&&b(c,u,5e3),!t)return;const d=(s=t==null?void 0:t.data)==null?void 0:s.find(ee=>{var G,J;return ee.id===((J=(G=t==null?void 0:t.data[0])==null?void 0:G.customer)==null?void 0:J.default_source)});H(d)}catch(t){console.error("ERROR",t),b(c,t.message,5e3),L(w,t.code)}finally{B(!1)}},[c,w]);a.useEffect(()=>{$(),P()},[$,P]),a.useEffect(()=>{if(l){const s=v(),t=i||n;ne({title:t?`Membership for ${s}`:"Membership",path:"/user/membership",clubName:l==null?void 0:l.name,favicon:l==null?void 0:l.club_logo,description:t?`Membership for ${s}`:"Membership"})}},[i,n,v,l==null?void 0:l.name,l==null?void 0:l.club_logo]);const Q=s=>{W(s),D(2)},X=async()=>{E(!0);try{if(p!=null&&p.subId){const s=await m.changeStripeSubscription({userId:C,activeSubscriptionId:p.subId,newPlanId:r.plan_id});s.error?(console.error(s.message),b(c,s.message,7500,"error")):(b(c,"Subscription updated successfully",3e3),y("/user/profile?tab=membership"))}else{const s=await m.createStripeSubscription({planId:r.plan_id,userId:C});s.error?(console.error(s.message),b(c,s.message,7500,"error")):(b(c,"Subscription created successfully",3e3),y("/user/profile?tab=membership"))}}catch(s){console.error("Error",s),b(c,s.message,7500,"error"),L(c,s.code)}finally{E(!1)}},T=a.useCallback(async()=>{try{const s=await m.getCustomerStripeSubscription(C);K(s.customer)}catch(s){console.error(s),L(w,s.code)}},[C,w]);a.useEffect(()=>{T()},[T]);const Z=()=>e.jsxs("div",{className:"h-fit animate-pulse rounded-xl border border-gray-200 bg-white p-5 shadow-sm",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between border-b border-gray-100 pb-4",children:[e.jsx("div",{className:"h-6 w-32 rounded bg-gray-200"}),e.jsx("div",{className:"h-6 w-16 rounded bg-gray-200"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 h-8 w-24 rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-16 rounded bg-gray-200"})]}),e.jsxs("div",{className:"mb-4 rounded-lg bg-gray-50 p-3",children:[e.jsx("div",{className:"mb-2 h-5 w-40 rounded bg-gray-200"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:[1,2,3,4].map(s=>e.jsx("div",{className:"h-4 rounded bg-gray-200"},s))})]}),e.jsx("div",{className:"mb-4 space-y-3",children:[1,2,3].map(s=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-5 w-5 rounded bg-gray-200"}),e.jsx("div",{className:"h-4 flex-1 rounded bg-gray-200"})]},s))}),e.jsx("div",{className:"h-10 w-full rounded-lg bg-gray-200"})]});return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100",children:e.jsxs("div",{className:"mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8",children:[e.jsx("div",{className:"mb-6",children:e.jsx(me,{onBack:()=>{A===1?y(-1):D(1)}})}),A===1&&e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 sm:text-4xl lg:text-5xl",children:i||n?`Choose Membership for ${v()}`:"Choose Your Membership"}),e.jsx("p",{className:"mx-auto mt-4 max-w-2xl text-lg text-gray-600",children:i||n?`Select the perfect plan for ${I()} and start enjoying all the benefits`:"Select the perfect plan that fits your needs and start enjoying all the benefits"})]}),e.jsx("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:gap-8",children:_?Array.from({length:3}).map((s,t)=>e.jsx(Z,{},t)):(f==null?void 0:f.length)>0?f.map(s=>e.jsx("div",{className:"transform transition-all duration-200 hover:scale-105",children:e.jsx(se,{...s,isCurrentPlan:R===s.id,onSelect:()=>Q(s),isActive:(p==null?void 0:p.planId)===s.plan_id})},s.plan_name)):e.jsxs("div",{className:"col-span-full py-12 text-center",children:[e.jsx("div",{className:"mx-auto h-24 w-24 text-gray-400",children:e.jsx("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),e.jsx("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:"No membership plans available"}),e.jsx("p",{className:"mt-2 text-gray-500",children:"Please contact support for assistance."})]})})]}),A===2&&e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 sm:text-4xl",children:(r==null?void 0:r.price)===0?i||n?`Confirm Selection for ${v()}`:"Confirm Your Selection":i||n?`Complete Purchase for ${v()}`:"Complete Your Purchase"}),e.jsx("p",{className:"mt-2 text-lg text-gray-600",children:(r==null?void 0:r.price)===0?i||n?`Review the free plan selection for ${I()} and activate the membership`:"Review your free plan selection and activate your membership":i||n?`Review the selection and payment details for ${I()}`:"Review your selection and payment details"})]}),e.jsx("div",{className:"mx-auto max-w-6xl",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-2",children:[e.jsx("div",{className:"order-2 lg:order-1",children:e.jsxs("div",{className:"overflow-hidden rounded-2xl border border-gray-100 bg-white shadow-xl",children:[e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4",children:e.jsx("h2",{className:"text-lg font-semibold text-white",children:"Membership Details"})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"mb-2 text-2xl font-bold text-gray-900",children:r==null?void 0:r.plan_name}),e.jsxs("div",{className:"text-3xl font-bold text-blue-600",children:[k(r==null?void 0:r.price),e.jsx("span",{className:"ml-1 text-lg font-normal text-gray-500",children:"/month"})]})]}),e.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[e.jsx("h4",{className:"mb-4 font-semibold text-gray-900",children:"Features included:"}),e.jsx("div",{className:"space-y-3",children:(U=r==null?void 0:r.features)==null?void 0:U.map((s,t)=>e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-green-100",children:e.jsx("svg",{className:"h-4 w-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),e.jsx("span",{className:"text-gray-700",children:s.text})]},t))})]})]})]})}),e.jsx("div",{className:"order-1 lg:order-2",children:e.jsxs("div",{className:"overflow-hidden rounded-2xl border border-gray-100 bg-white shadow-xl",children:[e.jsx("div",{className:`px-6 py-4 ${(r==null?void 0:r.price)===0,"bg-gradient-to-r from-green-600 to-emerald-600"}`,children:e.jsx("h2",{className:"text-lg font-semibold text-white",children:(r==null?void 0:r.price)===0?"Subscription Details":"Payment Details"})}),e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex justify-between text-sm uppercase tracking-wide text-gray-600",children:e.jsx("span",{children:"Billing Summary"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-700",children:"Plan price"}),e.jsx("span",{className:"font-medium",children:k(r==null?void 0:r.price)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-700",children:"Taxes & fees"}),e.jsx("span",{className:"font-medium",children:k(0)})]})]}),e.jsx("div",{className:"border-t border-gray-200 pt-4",children:e.jsxs("div",{className:"flex justify-between text-lg font-bold",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"text-blue-600",children:k(r==null?void 0:r.price)})]})})]})}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"mb-4 font-semibold text-gray-900",children:"Payment Method"}),z?e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"h-16 rounded-lg bg-gray-200"})}):o?e.jsxs("div",{className:"flex items-center justify-between rounded-xl border-2 border-gray-200 bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[ge(o==null?void 0:o.brand),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium text-gray-900",children:[(Y=o==null?void 0:o.brand)==null?void 0:Y.toUpperCase()," ••••"," ",o==null?void 0:o.last4]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Default payment method"})]})]}),e.jsx("button",{onClick:()=>y("/user/profile?tab=payment-methods"),className:"text-sm font-medium text-blue-600 transition-colors hover:text-blue-700",children:"Change"})]}):e.jsx("div",{className:"rounded-xl border-2 border-red-200 bg-red-50 p-4",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-2 text-sm font-medium text-red-800",children:"Payment method required"}),e.jsx("p",{className:"mb-3 text-sm text-red-700",children:"Please add a payment method to continue with your subscription."}),e.jsx("button",{onClick:()=>y("/user/profile?tab=payment-methods"),className:"text-sm font-medium text-red-600 underline transition-colors hover:text-red-700",children:"Add Payment Method →"})]})]})})]}),e.jsx(ce,{onClick:X,loading:O,disabled:!o,className:`w-full rounded-xl py-4 text-lg font-semibold transition-all duration-200 ${o?"transform bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg hover:scale-[1.02] hover:from-blue-700 hover:to-indigo-700 hover:shadow-xl":"cursor-not-allowed bg-gray-300 text-gray-500"}`,children:(r==null?void 0:r.price)===0?"Activate Free Plan":o?"Complete Purchase":"Add payment method to continue"}),e.jsx("p",{className:"mt-6 text-center text-xs leading-relaxed text-gray-500",children:"By completing this purchase, you agree to our Terms of Service and Privacy Policy. Your subscription will automatically renew monthly unless cancelled."})]})]})})]})})]})]})})}export{es as default};
