import{j as r}from"./@nivo/heatmap-ba1ecfff.js";import{r as n}from"./vendor-851db8c1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";const x=({customMinWidthInTw:s="min-w-[30%]",isModalActive:t=!1,closeModalFn:a=()=>{},children:i})=>{const e=n.useRef();return n.useEffect(()=>{const o=d=>{e.current&&!e.current.contains(d.target)&&a()};return document.addEventListener("mousedown",o),()=>{document.removeEventListener("mousedown",o)}},[]),n.useEffect(()=>{t&&e&&e.current.focus()},[t]),r.jsx("div",{id:"modal","aria-hidden":"false",autoFocus:!0,className:`transition-all ${t?"-translate-x-0 flex":"translate-x-full hidden"} z-50 bg-[#292828d2] overflow-x-hidden overflow-y-auto fixed h-screen w-screen md:h-full left-0 right-0 md:inset-0 justify-center items-center`,children:r.jsx("div",{className:"relative overflow-hidden !max-w-[250px] min-h-[100vh] px-4 h-full",children:r.jsx("div",{ref:e,autoFocus:!0,className:`${s} fixed right-0 z-[9999] bg-white py-1 rounded-md border min-h-[100vh]  overflow-y-auto block items-center text-center shadow-xl transition-all ${t?"-translate-x-0":"translate-x-full hidden"}`,children:i})})})};export{x as default};
