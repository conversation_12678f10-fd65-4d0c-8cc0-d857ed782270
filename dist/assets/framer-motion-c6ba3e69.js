import{r as y,b as hs}from"./vendor-851db8c1.js";const fs=y.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),Qt=y.createContext({}),Jt=y.createContext(null),te=typeof document<"u",Fe=te?y.useLayoutEffect:y.useEffect,ds=y.createContext({strict:!1}),Be=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),ki="framerAppearId",ms="data-"+Be(ki);function ji(t,e,n,s){const{visualElement:i}=y.useContext(Qt),o=y.useContext(ds),r=y.useContext(Jt),a=y.useContext(fs).reducedMotion,l=y.useRef();s=s||o.renderer,!l.current&&s&&(l.current=s(t,{visualState:e,parent:i,props:n,presenceContext:r,blockInitialAnimation:r?r.initial===!1:!1,reducedMotionConfig:a}));const c=l.current;y.useInsertionEffect(()=>{c&&c.update(n,r)});const u=y.useRef(!!(n[ms]&&!window.HandoffComplete));return Fe(()=>{c&&(c.render(),u.current&&c.animationState&&c.animationState.animateChanges())}),y.useEffect(()=>{c&&(c.updateFeatures(),!u.current&&c.animationState&&c.animationState.animateChanges(),u.current&&(u.current=!1,window.HandoffComplete=!0))}),c}function mt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function Oi(t,e,n){return y.useCallback(s=>{s&&t.mount&&t.mount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):mt(n)&&(n.current=s))},[e])}function Rt(t){return typeof t=="string"||Array.isArray(t)}function ee(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const ke=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],je=["initial",...ke];function ne(t){return ee(t.animate)||je.some(e=>Rt(t[e]))}function ps(t){return!!(ne(t)||t.variants)}function Ii(t,e){if(ne(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Rt(n)?n:void 0,animate:Rt(s)?s:void 0}}return t.inherit!==!1?e:{}}function Ui(t){const{initial:e,animate:n}=Ii(t,y.useContext(Qt));return y.useMemo(()=>({initial:e,animate:n}),[an(e),an(n)])}function an(t){return Array.isArray(t)?t.join(" "):t}const ln={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Lt={};for(const t in ln)Lt[t]={isEnabled:e=>ln[t].some(n=>!!e[n])};function Ni(t){for(const e in t)Lt[e]={...Lt[e],...t[e]}}const Oe=y.createContext({}),gs=y.createContext({}),Wi=Symbol.for("motionComponentSymbol");function Gi({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){t&&Ni(t);function o(a,l){let c;const u={...y.useContext(fs),...a,layoutId:Hi(a)},{isStatic:h}=u,f=Ui(a),d=s(a,h);if(!h&&te){f.visualElement=ji(i,d,u,e);const m=y.useContext(gs),p=y.useContext(ds).strict;f.visualElement&&(c=f.visualElement.loadFeatures(u,p,t,m))}return y.createElement(Qt.Provider,{value:f},c&&f.visualElement?y.createElement(c,{visualElement:f.visualElement,...u}):null,n(i,a,Oi(d,f.visualElement,l),d,h,f.visualElement))}const r=y.forwardRef(o);return r[Wi]=i,r}function Hi({layoutId:t}){const e=y.useContext(Oe).id;return e&&t!==void 0?e+"-"+t:t}function $i(t){function e(s,i={}){return Gi(t(s,i))}if(typeof Proxy>"u")return e;const n=new Map;return new Proxy(e,{get:(s,i)=>(n.has(i)||n.set(i,e(i)),n.get(i))})}const zi=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ie(t){return typeof t!="string"||t.includes("-")?!1:!!(zi.indexOf(t)>-1||/[A-Z]/.test(t))}const $t={};function Ki(t){Object.assign($t,t)}const Ft=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ct=new Set(Ft);function ys(t,{layout:e,layoutId:n}){return ct.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!$t[t]||t==="opacity")}const O=t=>!!(t&&t.getVelocity),_i={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Xi=Ft.length;function Yi(t,{enableHardwareAcceleration:e=!0,allowTransformNone:n=!0},s,i){let o="";for(let r=0;r<Xi;r++){const a=Ft[r];if(t[a]!==void 0){const l=_i[a]||a;o+=`${l}(${t[a]}) `}}return e&&!t.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(t,s?"":o):n&&s&&(o="none"),o}const vs=t=>e=>typeof e=="string"&&e.startsWith(t),xs=vs("--"),Pe=vs("var(--"),qi=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,Zi=(t,e)=>e&&typeof t=="number"?e.transform(t):t,tt=(t,e,n)=>Math.min(Math.max(n,t),e),ut={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},At={...ut,transform:t=>tt(0,1,t)},It={...ut,default:1},wt=t=>Math.round(t*1e5)/1e5,se=/(-)?([\d]*\.?[\d])+/g,Ps=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Qi=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Bt(t){return typeof t=="string"}const kt=t=>({test:e=>Bt(e)&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),q=kt("deg"),H=kt("%"),b=kt("px"),Ji=kt("vh"),tr=kt("vw"),cn={...H,parse:t=>H.parse(t)/100,transform:t=>H.transform(t*100)},un={...ut,transform:Math.round},Ts={borderWidth:b,borderTopWidth:b,borderRightWidth:b,borderBottomWidth:b,borderLeftWidth:b,borderRadius:b,radius:b,borderTopLeftRadius:b,borderTopRightRadius:b,borderBottomRightRadius:b,borderBottomLeftRadius:b,width:b,maxWidth:b,height:b,maxHeight:b,size:b,top:b,right:b,bottom:b,left:b,padding:b,paddingTop:b,paddingRight:b,paddingBottom:b,paddingLeft:b,margin:b,marginTop:b,marginRight:b,marginBottom:b,marginLeft:b,rotate:q,rotateX:q,rotateY:q,rotateZ:q,scale:It,scaleX:It,scaleY:It,scaleZ:It,skew:q,skewX:q,skewY:q,distance:b,translateX:b,translateY:b,translateZ:b,x:b,y:b,z:b,perspective:b,transformPerspective:b,opacity:At,originX:cn,originY:cn,originZ:b,zIndex:un,fillOpacity:At,strokeOpacity:At,numOctaves:un};function Ue(t,e,n,s){const{style:i,vars:o,transform:r,transformOrigin:a}=t;let l=!1,c=!1,u=!0;for(const h in e){const f=e[h];if(xs(h)){o[h]=f;continue}const d=Ts[h],m=Zi(f,d);if(ct.has(h)){if(l=!0,r[h]=m,!u)continue;f!==(d.default||0)&&(u=!1)}else h.startsWith("origin")?(c=!0,a[h]=m):i[h]=m}if(e.transform||(l||s?i.transform=Yi(t.transform,n,u,s):i.transform&&(i.transform="none")),c){const{originX:h="50%",originY:f="50%",originZ:d=0}=a;i.transformOrigin=`${h} ${f} ${d}`}}const Ne=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function bs(t,e,n){for(const s in e)!O(e[s])&&!ys(s,n)&&(t[s]=e[s])}function er({transformTemplate:t},e,n){return y.useMemo(()=>{const s=Ne();return Ue(s,e,{enableHardwareAcceleration:!n},t),Object.assign({},s.vars,s.style)},[e])}function nr(t,e,n){const s=t.style||{},i={};return bs(i,s,t),Object.assign(i,er(t,e,n)),t.transformValues?t.transformValues(i):i}function sr(t,e,n){const s={},i=nr(t,e,n);return t.drag&&t.dragListener!==!1&&(s.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(s.tabIndex=0),s.style=i,s}const ir=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function zt(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ir.has(t)}let Vs=t=>!zt(t);function rr(t){t&&(Vs=e=>e.startsWith("on")?!zt(e):t(e))}try{rr(require("@emotion/is-prop-valid").default)}catch{}function or(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(Vs(i)||n===!0&&zt(i)||!e&&!zt(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function hn(t,e,n){return typeof t=="string"?t:b.transform(e+n*t)}function ar(t,e,n){const s=hn(e,t.x,t.width),i=hn(n,t.y,t.height);return`${s} ${i}`}const lr={offset:"stroke-dashoffset",array:"stroke-dasharray"},cr={offset:"strokeDashoffset",array:"strokeDasharray"};function ur(t,e,n=1,s=0,i=!0){t.pathLength=1;const o=i?lr:cr;t[o.offset]=b.transform(-s);const r=b.transform(e),a=b.transform(n);t[o.array]=`${r} ${a}`}function We(t,{attrX:e,attrY:n,attrScale:s,originX:i,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:l=0,...c},u,h,f){if(Ue(t,c,u,f),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:d,style:m,dimensions:p}=t;d.transform&&(p&&(m.transform=d.transform),delete d.transform),p&&(i!==void 0||o!==void 0||m.transform)&&(m.transformOrigin=ar(p,i!==void 0?i:.5,o!==void 0?o:.5)),e!==void 0&&(d.x=e),n!==void 0&&(d.y=n),s!==void 0&&(d.scale=s),r!==void 0&&ur(d,r,a,l,!1)}const Ss=()=>({...Ne(),attrs:{}}),Ge=t=>typeof t=="string"&&t.toLowerCase()==="svg";function hr(t,e,n,s){const i=y.useMemo(()=>{const o=Ss();return We(o,e,{enableHardwareAcceleration:!1},Ge(s),t.transformTemplate),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};bs(o,t.style,t),i.style={...o,...i.style}}return i}function fr(t=!1){return(n,s,i,{latestValues:o},r)=>{const l=(Ie(n)?hr:sr)(s,o,r,n),u={...or(s,typeof n=="string",t),...l,ref:i},{children:h}=s,f=y.useMemo(()=>O(h)?h.get():h,[h]);return y.createElement(n,{...u,children:f})}}function Cs(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const o in n)t.style.setProperty(o,n[o])}const As=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ws(t,e,n,s){Cs(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(As.has(i)?i:Be(i),e.attrs[i])}function He(t,e){const{style:n}=t,s={};for(const i in n)(O(n[i])||e.style&&O(e.style[i])||ys(i,t))&&(s[i]=n[i]);return s}function Ds(t,e){const n=He(t,e);for(const s in t)if(O(t[s])||O(e[s])){const i=Ft.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;n[i]=t[s]}return n}function $e(t,e,n,s={},i={}){return typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),e}function Ms(t){const e=y.useRef(null);return e.current===null&&(e.current=t()),e.current}const Kt=t=>Array.isArray(t),dr=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),mr=t=>Kt(t)?t[t.length-1]||0:t;function Gt(t){const e=O(t)?t.get():t;return dr(e)?e.toValue():e}function pr({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},s,i,o){const r={latestValues:gr(s,i,o,t),renderState:e()};return n&&(r.mount=a=>n(s,a,r)),r}const Rs=t=>(e,n)=>{const s=y.useContext(Qt),i=y.useContext(Jt),o=()=>pr(t,e,s,i);return n?o():Ms(o)};function gr(t,e,n,s){const i={},o=s(t,{});for(const f in o)i[f]=Gt(o[f]);let{initial:r,animate:a}=t;const l=ne(t),c=ps(t);e&&c&&!l&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||r===!1;const h=u?a:r;return h&&typeof h!="boolean"&&!ee(h)&&(Array.isArray(h)?h:[h]).forEach(d=>{const m=$e(t,d);if(!m)return;const{transitionEnd:p,transition:x,...T}=m;for(const v in T){let g=T[v];if(Array.isArray(g)){const P=u?g.length-1:0;g=g[P]}g!==null&&(i[v]=g)}for(const v in p)i[v]=p[v]}),i}const L=t=>t;class fn{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){const n=this.order.indexOf(e);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}function yr(t){let e=new fn,n=new fn,s=0,i=!1,o=!1;const r=new WeakSet,a={schedule:(l,c=!1,u=!1)=>{const h=u&&i,f=h?e:n;return c&&r.add(l),f.add(l)&&h&&i&&(s=e.order.length),l},cancel:l=>{n.remove(l),r.delete(l)},process:l=>{if(i){o=!0;return}if(i=!0,[e,n]=[n,e],n.clear(),s=e.order.length,s)for(let c=0;c<s;c++){const u=e.order[c];u(l),r.has(u)&&(a.schedule(u),t())}i=!1,o&&(o=!1,a.process(l))}};return a}const Ut=["prepare","read","update","preRender","render","postRender"],vr=40;function xr(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=Ut.reduce((h,f)=>(h[f]=yr(()=>n=!0),h),{}),r=h=>o[h].process(i),a=()=>{const h=performance.now();n=!1,i.delta=s?1e3/60:Math.max(Math.min(h-i.timestamp,vr),1),i.timestamp=h,i.isProcessing=!0,Ut.forEach(r),i.isProcessing=!1,n&&e&&(s=!1,t(a))},l=()=>{n=!0,s=!0,i.isProcessing||t(a)};return{schedule:Ut.reduce((h,f)=>{const d=o[f];return h[f]=(m,p=!1,x=!1)=>(n||l(),d.schedule(m,p,x)),h},{}),cancel:h=>Ut.forEach(f=>o[f].cancel(h)),state:i,steps:o}}const{schedule:A,cancel:X,state:B,steps:ae}=xr(typeof requestAnimationFrame<"u"?requestAnimationFrame:L,!0),Pr={useVisualState:Rs({scrapeMotionValuesFromProps:Ds,createRenderState:Ss,onMount:(t,e,{renderState:n,latestValues:s})=>{A.read(()=>{try{n.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),A.render(()=>{We(n,s,{enableHardwareAcceleration:!1},Ge(e.tagName),t.transformTemplate),ws(e,n)})}})},Tr={useVisualState:Rs({scrapeMotionValuesFromProps:He,createRenderState:Ne})};function br(t,{forwardMotionProps:e=!1},n,s){return{...Ie(t)?Pr:Tr,preloadedFeatures:n,useRender:fr(e),createVisualElement:s,Component:t}}function z(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}const Ls=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function ie(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}const Vr=t=>e=>Ls(e)&&t(e,ie(e));function K(t,e,n,s){return z(t,e,Vr(n),s)}const Sr=(t,e)=>n=>e(t(n)),Q=(...t)=>t.reduce(Sr);function Es(t){let e=null;return()=>{const n=()=>{e=null};return e===null?(e=t,n):!1}}const dn=Es("dragHorizontal"),mn=Es("dragVertical");function Fs(t){let e=!1;if(t==="y")e=mn();else if(t==="x")e=dn();else{const n=dn(),s=mn();n&&s?e=()=>{n(),s()}:(n&&n(),s&&s())}return e}function Bs(){const t=Fs(!0);return t?(t(),!1):!0}class nt{constructor(e){this.isMounted=!1,this.node=e}update(){}}function pn(t,e){const n="pointer"+(e?"enter":"leave"),s="onHover"+(e?"Start":"End"),i=(o,r)=>{if(o.pointerType==="touch"||Bs())return;const a=t.getProps();t.animationState&&a.whileHover&&t.animationState.setActive("whileHover",e),a[s]&&A.update(()=>a[s](o,r))};return K(t.current,n,i,{passive:!t.getProps()[s]})}class Cr extends nt{mount(){this.unmount=Q(pn(this.node,!0),pn(this.node,!1))}unmount(){}}class Ar extends nt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Q(z(this.node.current,"focus",()=>this.onFocus()),z(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const ks=(t,e)=>e?t===e?!0:ks(t,e.parentElement):!1;function le(t,e){if(!e)return;const n=new PointerEvent("pointer"+t);e(n,ie(n))}class wr extends nt{constructor(){super(...arguments),this.removeStartListeners=L,this.removeEndListeners=L,this.removeAccessibleListeners=L,this.startPointerPress=(e,n)=>{if(this.isPressing)return;this.removeEndListeners();const s=this.node.getProps(),o=K(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:c,onTapCancel:u,globalTapTarget:h}=this.node.getProps();A.update(()=>{!h&&!ks(this.node.current,a.target)?u&&u(a,l):c&&c(a,l)})},{passive:!(s.onTap||s.onPointerUp)}),r=K(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(s.onTapCancel||s.onPointerCancel)});this.removeEndListeners=Q(o,r),this.startPress(e,n)},this.startAccessiblePress=()=>{const e=o=>{if(o.key!=="Enter"||this.isPressing)return;const r=a=>{a.key!=="Enter"||!this.checkPressEnd()||le("up",(l,c)=>{const{onTap:u}=this.node.getProps();u&&A.update(()=>u(l,c))})};this.removeEndListeners(),this.removeEndListeners=z(this.node.current,"keyup",r),le("down",(a,l)=>{this.startPress(a,l)})},n=z(this.node.current,"keydown",e),s=()=>{this.isPressing&&le("cancel",(o,r)=>this.cancelPress(o,r))},i=z(this.node.current,"blur",s);this.removeAccessibleListeners=Q(n,i)}}startPress(e,n){this.isPressing=!0;const{onTapStart:s,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),s&&A.update(()=>s(e,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Bs()}cancelPress(e,n){if(!this.checkPressEnd())return;const{onTapCancel:s}=this.node.getProps();s&&A.update(()=>s(e,n))}mount(){const e=this.node.getProps(),n=K(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),s=z(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Q(n,s)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Te=new WeakMap,ce=new WeakMap,Dr=t=>{const e=Te.get(t.target);e&&e(t)},Mr=t=>{t.forEach(Dr)};function Rr({root:t,...e}){const n=t||document;ce.has(n)||ce.set(n,{});const s=ce.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Mr,{root:t,...e})),s[i]}function Lr(t,e,n){const s=Rr(e);return Te.set(t,n),s.observe(t),()=>{Te.delete(t),s.unobserve(t)}}const Er={some:0,all:1};class Fr extends nt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:Er[i]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=c?u:h;f&&f(l)};return Lr(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Br(e,n))&&this.startObserver()}unmount(){}}function Br({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const kr={inView:{Feature:Fr},tap:{Feature:wr},focus:{Feature:Ar},hover:{Feature:Cr}};function js(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}function jr(t){const e={};return t.values.forEach((n,s)=>e[s]=n.get()),e}function Or(t){const e={};return t.values.forEach((n,s)=>e[s]=n.getVelocity()),e}function re(t,e,n){const s=t.getProps();return $e(s,e,n!==void 0?n:s.custom,jr(t),Or(t))}let Ir=L,ze=L;const J=t=>t*1e3,_=t=>t/1e3,Ur={current:!1},Os=t=>Array.isArray(t)&&typeof t[0]=="number";function Is(t){return!!(!t||typeof t=="string"&&Us[t]||Os(t)||Array.isArray(t)&&t.every(Is))}const Ct=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Us={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ct([0,.65,.55,1]),circOut:Ct([.55,0,1,.45]),backIn:Ct([.31,.01,.66,-.59]),backOut:Ct([.33,1.53,.69,.99])};function Ns(t){if(t)return Os(t)?Ct(t):Array.isArray(t)?t.map(Ns):Us[t]}function Nr(t,e,n,{delay:s=0,duration:i,repeat:o=0,repeatType:r="loop",ease:a,times:l}={}){const c={[e]:n};l&&(c.offset=l);const u=Ns(a);return Array.isArray(u)&&(c.easing=u),t.animate(c,{delay:s,duration:i,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"})}function Wr(t,{repeat:e,repeatType:n="loop"}){const s=e&&n!=="loop"&&e%2===1?0:t.length-1;return t[s]}const Ws=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,Gr=1e-7,Hr=12;function $r(t,e,n,s,i){let o,r,a=0;do r=e+(n-e)/2,o=Ws(r,s,i)-t,o>0?n=r:e=r;while(Math.abs(o)>Gr&&++a<Hr);return r}function jt(t,e,n,s){if(t===e&&n===s)return L;const i=o=>$r(o,0,1,t,n);return o=>o===0||o===1?o:Ws(i(o),e,s)}const zr=jt(.42,0,1,1),Kr=jt(0,0,.58,1),Gs=jt(.42,0,.58,1),_r=t=>Array.isArray(t)&&typeof t[0]!="number",Hs=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,$s=t=>e=>1-t(1-e),Ke=t=>1-Math.sin(Math.acos(t)),zs=$s(Ke),Xr=Hs(Ke),Ks=jt(.33,1.53,.69,.99),_e=$s(Ks),Yr=Hs(_e),qr=t=>(t*=2)<1?.5*_e(t):.5*(2-Math.pow(2,-10*(t-1))),Zr={linear:L,easeIn:zr,easeInOut:Gs,easeOut:Kr,circIn:Ke,circInOut:Xr,circOut:zs,backIn:_e,backInOut:Yr,backOut:Ks,anticipate:qr},gn=t=>{if(Array.isArray(t)){ze(t.length===4);const[e,n,s,i]=t;return jt(e,n,s,i)}else if(typeof t=="string")return Zr[t];return t},Xe=(t,e)=>n=>!!(Bt(n)&&Qi.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),_s=(t,e,n)=>s=>{if(!Bt(s))return s;const[i,o,r,a]=s.match(se);return{[t]:parseFloat(i),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},Qr=t=>tt(0,255,t),ue={...ut,transform:t=>Math.round(Qr(t))},lt={test:Xe("rgb","red"),parse:_s("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+ue.transform(t)+", "+ue.transform(e)+", "+ue.transform(n)+", "+wt(At.transform(s))+")"};function Jr(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const be={test:Xe("#"),parse:Jr,transform:lt.transform},pt={test:Xe("hsl","hue"),parse:_s("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+H.transform(wt(e))+", "+H.transform(wt(n))+", "+wt(At.transform(s))+")"},j={test:t=>lt.test(t)||be.test(t)||pt.test(t),parse:t=>lt.test(t)?lt.parse(t):pt.test(t)?pt.parse(t):be.parse(t),transform:t=>Bt(t)?t:t.hasOwnProperty("red")?lt.transform(t):pt.transform(t)},R=(t,e,n)=>-n*t+n*e+t;function he(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function to({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,o=0,r=0;if(!e)i=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=he(l,a,t+1/3),o=he(l,a,t),r=he(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:s}}const fe=(t,e,n)=>{const s=t*t;return Math.sqrt(Math.max(0,n*(e*e-s)+s))},eo=[be,lt,pt],no=t=>eo.find(e=>e.test(t));function yn(t){const e=no(t);let n=e.parse(t);return e===pt&&(n=to(n)),n}const Xs=(t,e)=>{const n=yn(t),s=yn(e),i={...n};return o=>(i.red=fe(n.red,s.red,o),i.green=fe(n.green,s.green,o),i.blue=fe(n.blue,s.blue,o),i.alpha=R(n.alpha,s.alpha,o),lt.transform(i))};function so(t){var e,n;return isNaN(t)&&Bt(t)&&(((e=t.match(se))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(Ps))===null||n===void 0?void 0:n.length)||0)>0}const Ys={regex:qi,countKey:"Vars",token:"${v}",parse:L},qs={regex:Ps,countKey:"Colors",token:"${c}",parse:j.parse},Zs={regex:se,countKey:"Numbers",token:"${n}",parse:ut.parse};function de(t,{regex:e,countKey:n,token:s,parse:i}){const o=t.tokenised.match(e);o&&(t["num"+n]=o.length,t.tokenised=t.tokenised.replace(e,s),t.values.push(...o.map(i)))}function _t(t){const e=t.toString(),n={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&de(n,Ys),de(n,qs),de(n,Zs),n}function Qs(t){return _t(t).values}function Js(t){const{values:e,numColors:n,numVars:s,tokenised:i}=_t(t),o=e.length;return r=>{let a=i;for(let l=0;l<o;l++)l<s?a=a.replace(Ys.token,r[l]):l<s+n?a=a.replace(qs.token,j.transform(r[l])):a=a.replace(Zs.token,wt(r[l]));return a}}const io=t=>typeof t=="number"?0:t;function ro(t){const e=Qs(t);return Js(t)(e.map(io))}const et={test:so,parse:Qs,createTransformer:Js,getAnimatableNone:ro},ti=(t,e)=>n=>`${n>0?e:t}`;function ei(t,e){return typeof t=="number"?n=>R(t,e,n):j.test(t)?Xs(t,e):t.startsWith("var(")?ti(t,e):si(t,e)}const ni=(t,e)=>{const n=[...t],s=n.length,i=t.map((o,r)=>ei(o,e[r]));return o=>{for(let r=0;r<s;r++)n[r]=i[r](o);return n}},oo=(t,e)=>{const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=ei(t[i],e[i]));return i=>{for(const o in s)n[o]=s[o](i);return n}},si=(t,e)=>{const n=et.createTransformer(e),s=_t(t),i=_t(e);return s.numVars===i.numVars&&s.numColors===i.numColors&&s.numNumbers>=i.numNumbers?Q(ni(s.values,i.values),n):ti(t,e)},Et=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s},vn=(t,e)=>n=>R(t,e,n);function ao(t){return typeof t=="number"?vn:typeof t=="string"?j.test(t)?Xs:si:Array.isArray(t)?ni:typeof t=="object"?oo:vn}function lo(t,e,n){const s=[],i=n||ao(t[0]),o=t.length-1;for(let r=0;r<o;r++){let a=i(t[r],t[r+1]);if(e){const l=Array.isArray(e)?e[r]||L:e;a=Q(l,a)}s.push(a)}return s}function ii(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const o=t.length;if(ze(o===e.length),o===1)return()=>e[0];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const r=lo(e,s,i),a=r.length,l=c=>{let u=0;if(a>1)for(;u<t.length-2&&!(c<t[u+1]);u++);const h=Et(t[u],t[u+1],c);return r[u](h)};return n?c=>l(tt(t[0],t[o-1],c)):l}function co(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=Et(0,e,s);t.push(R(n,1,i))}}function uo(t){const e=[0];return co(e,t.length-1),e}function ho(t,e){return t.map(n=>n*e)}function fo(t,e){return t.map(()=>e||Gs).splice(0,t.length-1)}function Xt({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=_r(s)?s.map(gn):gn(s),o={done:!1,value:e[0]},r=ho(n&&n.length===e.length?n:uo(e),t),a=ii(r,e,{ease:Array.isArray(i)?i:fo(e,i)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}function ri(t,e){return e?t*(1e3/e):0}const mo=5;function oi(t,e,n){const s=Math.max(e-mo,0);return ri(n-t(s),e-s)}const me=.001,po=.01,xn=10,go=.05,yo=1;function vo({duration:t=800,bounce:e=.25,velocity:n=0,mass:s=1}){let i,o;Ir(t<=J(xn));let r=1-e;r=tt(go,yo,r),t=tt(po,xn,_(t)),r<1?(i=c=>{const u=c*r,h=u*t,f=u-n,d=Ve(c,r),m=Math.exp(-h);return me-f/d*m},o=c=>{const h=c*r*t,f=h*n+n,d=Math.pow(r,2)*Math.pow(c,2)*t,m=Math.exp(-h),p=Ve(Math.pow(c,2),r);return(-i(c)+me>0?-1:1)*((f-d)*m)/p}):(i=c=>{const u=Math.exp(-c*t),h=(c-n)*t+1;return-me+u*h},o=c=>{const u=Math.exp(-c*t),h=(n-c)*(t*t);return u*h});const a=5/t,l=Po(i,o,a);if(t=J(t),isNaN(l))return{stiffness:100,damping:10,duration:t};{const c=Math.pow(l,2)*s;return{stiffness:c,damping:r*2*Math.sqrt(s*c),duration:t}}}const xo=12;function Po(t,e,n){let s=n;for(let i=1;i<xo;i++)s=s-t(s)/e(s);return s}function Ve(t,e){return t*Math.sqrt(1-e*e)}const To=["duration","bounce"],bo=["stiffness","damping","mass"];function Pn(t,e){return e.some(n=>t[n]!==void 0)}function Vo(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!Pn(t,bo)&&Pn(t,To)){const n=vo(t);e={...e,...n,mass:1},e.isResolvedFromDuration=!0}return e}function ai({keyframes:t,restDelta:e,restSpeed:n,...s}){const i=t[0],o=t[t.length-1],r={done:!1,value:i},{stiffness:a,damping:l,mass:c,duration:u,velocity:h,isResolvedFromDuration:f}=Vo({...s,velocity:-_(s.velocity||0)}),d=h||0,m=l/(2*Math.sqrt(a*c)),p=o-i,x=_(Math.sqrt(a/c)),T=Math.abs(p)<5;n||(n=T?.01:2),e||(e=T?.005:.5);let v;if(m<1){const g=Ve(x,m);v=P=>{const V=Math.exp(-m*x*P);return o-V*((d+m*x*p)/g*Math.sin(g*P)+p*Math.cos(g*P))}}else if(m===1)v=g=>o-Math.exp(-x*g)*(p+(d+x*p)*g);else{const g=x*Math.sqrt(m*m-1);v=P=>{const V=Math.exp(-m*x*P),w=Math.min(g*P,300);return o-V*((d+m*x*p)*Math.sinh(w)+g*p*Math.cosh(w))/g}}return{calculatedDuration:f&&u||null,next:g=>{const P=v(g);if(f)r.done=g>=u;else{let V=d;g!==0&&(m<1?V=oi(v,g,P):V=0);const w=Math.abs(V)<=n,D=Math.abs(o-P)<=e;r.done=w&&D}return r.value=r.done?o:P,r}}}function Tn({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],f={done:!1,value:h},d=S=>a!==void 0&&S<a||l!==void 0&&S>l,m=S=>a===void 0?l:l===void 0||Math.abs(a-S)<Math.abs(l-S)?a:l;let p=n*e;const x=h+p,T=r===void 0?x:r(x);T!==x&&(p=T-h);const v=S=>-p*Math.exp(-S/s),g=S=>T+v(S),P=S=>{const C=v(S),U=g(S);f.done=Math.abs(C)<=c,f.value=f.done?T:U};let V,w;const D=S=>{d(f.value)&&(V=S,w=ai({keyframes:[f.value,m(f.value)],velocity:oi(g,S,f.value),damping:i,stiffness:o,restDelta:c,restSpeed:u}))};return D(0),{calculatedDuration:null,next:S=>{let C=!1;return!w&&V===void 0&&(C=!0,P(S),D(S)),V!==void 0&&S>V?w.next(S-V):(!C&&P(S),f)}}}const So=t=>{const e=({timestamp:n})=>t(n);return{start:()=>A.update(e,!0),stop:()=>X(e),now:()=>B.isProcessing?B.timestamp:performance.now()}},bn=2e4;function Vn(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<bn;)e+=n,s=t.next(e);return e>=bn?1/0:e}const Co={decay:Tn,inertia:Tn,tween:Xt,keyframes:Xt,spring:ai};function Yt({autoplay:t=!0,delay:e=0,driver:n=So,keyframes:s,type:i="keyframes",repeat:o=0,repeatDelay:r=0,repeatType:a="loop",onPlay:l,onStop:c,onComplete:u,onUpdate:h,...f}){let d=1,m=!1,p,x;const T=()=>{x=new Promise(M=>{p=M})};T();let v;const g=Co[i]||Xt;let P;g!==Xt&&typeof s[0]!="number"&&(P=ii([0,100],s,{clamp:!1}),s=[0,100]);const V=g({...f,keyframes:s});let w;a==="mirror"&&(w=g({...f,keyframes:[...s].reverse(),velocity:-(f.velocity||0)}));let D="idle",S=null,C=null,U=null;V.calculatedDuration===null&&o&&(V.calculatedDuration=Vn(V));const{calculatedDuration:ht}=V;let G=1/0,$=1/0;ht!==null&&(G=ht+r,$=G*(o+1)-r);let k=0;const ft=M=>{if(C===null)return;d>0&&(C=Math.min(C,M)),d<0&&(C=Math.min(M-$/d,C)),S!==null?k=S:k=Math.round(M-C)*d;const Tt=k-e*(d>=0?1:-1),nn=d>=0?Tt<0:Tt>$;k=Math.max(Tt,0),D==="finished"&&S===null&&(k=$);let sn=k,rn=V;if(o){const oe=Math.min(k,$)/G;let Ot=Math.floor(oe),st=oe%1;!st&&oe>=1&&(st=1),st===1&&Ot--,Ot=Math.min(Ot,o+1),!!(Ot%2)&&(a==="reverse"?(st=1-st,r&&(st-=r/G)):a==="mirror"&&(rn=w)),sn=tt(0,1,st)*G}const bt=nn?{done:!1,value:s[0]}:rn.next(sn);P&&(bt.value=P(bt.value));let{done:on}=bt;!nn&&ht!==null&&(on=d>=0?k>=$:k<=0);const Bi=S===null&&(D==="finished"||D==="running"&&on);return h&&h(bt.value),Bi&&Pt(),bt},F=()=>{v&&v.stop(),v=void 0},Y=()=>{D="idle",F(),p(),T(),C=U=null},Pt=()=>{D="finished",u&&u(),F(),p()},dt=()=>{if(m)return;v||(v=n(ft));const M=v.now();l&&l(),S!==null?C=M-S:(!C||D==="finished")&&(C=M),D==="finished"&&T(),U=C,S=null,D="running",v.start()};t&&dt();const en={then(M,Tt){return x.then(M,Tt)},get time(){return _(k)},set time(M){M=J(M),k=M,S!==null||!v||d===0?S=M:C=v.now()-M/d},get duration(){const M=V.calculatedDuration===null?Vn(V):V.calculatedDuration;return _(M)},get speed(){return d},set speed(M){M===d||!v||(d=M,en.time=_(k))},get state(){return D},play:dt,pause:()=>{D="paused",S=k},stop:()=>{m=!0,D!=="idle"&&(D="idle",c&&c(),Y())},cancel:()=>{U!==null&&ft(U),Y()},complete:()=>{D="finished"},sample:M=>(C=0,ft(M))};return en}function Ao(t){let e;return()=>(e===void 0&&(e=t()),e)}const wo=Ao(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Do=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Nt=10,Mo=2e4,Ro=(t,e)=>e.type==="spring"||t==="backgroundColor"||!Is(e.ease);function Lo(t,e,{onUpdate:n,onComplete:s,...i}){if(!(wo()&&Do.has(e)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let r=!1,a,l,c=!1;const u=()=>{l=new Promise(g=>{a=g})};u();let{keyframes:h,duration:f=300,ease:d,times:m}=i;if(Ro(e,i)){const g=Yt({...i,repeat:0,delay:0});let P={done:!1,value:h[0]};const V=[];let w=0;for(;!P.done&&w<Mo;)P=g.sample(w),V.push(P.value),w+=Nt;m=void 0,h=V,f=w-Nt,d="linear"}const p=Nr(t.owner.current,e,h,{...i,duration:f,ease:d,times:m}),x=()=>{c=!1,p.cancel()},T=()=>{c=!0,A.update(x),a(),u()};return p.onfinish=()=>{c||(t.set(Wr(h,i)),s&&s(),T())},{then(g,P){return l.then(g,P)},attachTimeline(g){return p.timeline=g,p.onfinish=null,L},get time(){return _(p.currentTime||0)},set time(g){p.currentTime=J(g)},get speed(){return p.playbackRate},set speed(g){p.playbackRate=g},get duration(){return _(f)},play:()=>{r||(p.play(),X(x))},pause:()=>p.pause(),stop:()=>{if(r=!0,p.playState==="idle")return;const{currentTime:g}=p;if(g){const P=Yt({...i,autoplay:!1});t.setWithVelocity(P.sample(g-Nt).value,P.sample(g).value,Nt)}T()},complete:()=>{c||p.finish()},cancel:T}}function Eo({keyframes:t,delay:e,onUpdate:n,onComplete:s}){const i=()=>(n&&n(t[t.length-1]),s&&s(),{time:0,speed:1,duration:0,play:L,pause:L,stop:L,then:o=>(o(),Promise.resolve()),cancel:L,complete:L});return e?Yt({keyframes:[0,1],duration:0,delay:e,onComplete:i}):i()}const Fo={type:"spring",stiffness:500,damping:25,restSpeed:10},Bo=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),ko={type:"keyframes",duration:.8},jo={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Oo=(t,{keyframes:e})=>e.length>2?ko:ct.has(t)?t.startsWith("scale")?Bo(e[1]):Fo:jo,Se=(t,e)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(et.test(e)||e==="0")&&!e.startsWith("url(")),Io=new Set(["brightness","contrast","saturate","opacity"]);function Uo(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(se)||[];if(!s)return t;const i=n.replace(s,"");let o=Io.has(e)?1:0;return s!==n&&(o*=100),e+"("+o+i+")"}const No=/([a-z-]*)\(.*?\)/g,Ce={...et,getAnimatableNone:t=>{const e=t.match(No);return e?e.map(Uo).join(" "):t}},Wo={...Ts,color:j,backgroundColor:j,outlineColor:j,fill:j,stroke:j,borderColor:j,borderTopColor:j,borderRightColor:j,borderBottomColor:j,borderLeftColor:j,filter:Ce,WebkitFilter:Ce},Ye=t=>Wo[t];function li(t,e){let n=Ye(t);return n!==Ce&&(n=et),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const ci=t=>/^0[^.\s]+$/.test(t);function Go(t){if(typeof t=="number")return t===0;if(t!==null)return t==="none"||t==="0"||ci(t)}function Ho(t,e,n,s){const i=Se(e,n);let o;Array.isArray(n)?o=[...n]:o=[null,n];const r=s.from!==void 0?s.from:t.get();let a;const l=[];for(let c=0;c<o.length;c++)o[c]===null&&(o[c]=c===0?r:o[c-1]),Go(o[c])&&l.push(c),typeof o[c]=="string"&&o[c]!=="none"&&o[c]!=="0"&&(a=o[c]);if(i&&l.length&&a)for(let c=0;c<l.length;c++){const u=l[c];o[u]=li(e,a)}return o}function $o({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}function qe(t,e){return t[e]||t.default||t}const zo={skipAnimations:!1},Ze=(t,e,n,s={})=>i=>{const o=qe(s,t)||{},r=o.delay||s.delay||0;let{elapsed:a=0}=s;a=a-J(r);const l=Ho(e,t,n,o),c=l[0],u=l[l.length-1],h=Se(t,c),f=Se(t,u);let d={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...o,delay:-a,onUpdate:m=>{e.set(m),o.onUpdate&&o.onUpdate(m)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if($o(o)||(d={...d,...Oo(t,d)}),d.duration&&(d.duration=J(d.duration)),d.repeatDelay&&(d.repeatDelay=J(d.repeatDelay)),!h||!f||Ur.current||o.type===!1||zo.skipAnimations)return Eo(d);if(!s.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){const m=Lo(e,t,d);if(m)return m}return Yt(d)};function qt(t){return!!(O(t)&&t.add)}const ui=t=>/^\-?\d*\.?\d+$/.test(t);function Qe(t,e){t.indexOf(e)===-1&&t.push(e)}function Je(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class tn{constructor(){this.subscriptions=[]}add(e){return Qe(this.subscriptions,e),()=>Je(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let o=0;o<i;o++){const r=this.subscriptions[o];r&&r(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Ko=t=>!isNaN(parseFloat(t));class _o{constructor(e,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(s,i=!0)=>{this.prev=this.current,this.current=s;const{delta:o,timestamp:r}=B;this.lastUpdated!==r&&(this.timeDelta=o,this.lastUpdated=r,A.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>A.postRender(this.velocityCheck),this.velocityCheck=({timestamp:s})=>{s!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=Ko(this.current),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new tn);const s=this.events[e].add(n);return e==="change"?()=>{s(),A.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=e,this.timeDelta=s}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?ri(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function vt(t,e){return new _o(t,e)}const hi=t=>e=>e.test(t),Xo={test:t=>t==="auto",parse:t=>t},fi=[ut,b,H,q,tr,Ji,Xo],Vt=t=>fi.find(hi(t)),Yo=[...fi,j,et],qo=t=>Yo.find(hi(t));function Zo(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,vt(n))}function Qo(t,e){const n=re(t,e);let{transitionEnd:s={},transition:i={},...o}=n?t.makeTargetAnimatable(n,!1):{};o={...o,...s};for(const r in o){const a=mr(o[r]);Zo(t,r,a)}}function Jo(t,e,n){var s,i;const o=Object.keys(e).filter(a=>!t.hasValue(a)),r=o.length;if(r)for(let a=0;a<r;a++){const l=o[a],c=e[l];let u=null;Array.isArray(c)&&(u=c[0]),u===null&&(u=(i=(s=n[l])!==null&&s!==void 0?s:t.readValue(l))!==null&&i!==void 0?i:e[l]),u!=null&&(typeof u=="string"&&(ui(u)||ci(u))?u=parseFloat(u):!qo(u)&&et.test(c)&&(u=li(l,c)),t.addValue(l,vt(u,{owner:t})),n[l]===void 0&&(n[l]=u),u!==null&&t.setBaseTarget(l,u))}}function ta(t,e){return e?(e[t]||e.default||e).from:void 0}function ea(t,e,n){const s={};for(const i in t){const o=ta(i,e);if(o!==void 0)s[i]=o;else{const r=n.getValue(i);r&&(s[i]=r.get())}}return s}function na({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function sa(t,e){const n=t.get();if(Array.isArray(e)){for(let s=0;s<e.length;s++)if(e[s]!==n)return!0}else return n!==e}function di(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=t.makeTargetAnimatable(e);const l=t.getValue("willChange");s&&(o=s);const c=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const h in a){const f=t.getValue(h),d=a[h];if(!f||d===void 0||u&&na(u,h))continue;const m={delay:n,elapsed:0,...qe(o||{},h)};if(window.HandoffAppearAnimations){const T=t.getProps()[ms];if(T){const v=window.HandoffAppearAnimations(T,h,f,A);v!==null&&(m.elapsed=v,m.isHandoff=!0)}}let p=!m.isHandoff&&!sa(f,d);if(m.type==="spring"&&(f.getVelocity()||m.velocity)&&(p=!1),f.animation&&(p=!1),p)continue;f.start(Ze(h,f,d,t.shouldReduceMotion&&ct.has(h)?{type:!1}:m));const x=f.animation;qt(l)&&(l.add(h),x.then(()=>l.remove(h))),c.push(x)}return r&&Promise.all(c).then(()=>{r&&Qo(t,r)}),c}function Ae(t,e,n={}){const s=re(t,e,n.custom);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const o=s?()=>Promise.all(di(t,s,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(l=0)=>{const{delayChildren:c=0,staggerChildren:u,staggerDirection:h}=i;return ia(t,e,c+l,u,h,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[l,c]=a==="beforeChildren"?[o,r]:[r,o];return l().then(()=>c())}else return Promise.all([o(),r(n.delay)])}function ia(t,e,n=0,s=0,i=1,o){const r=[],a=(t.variantChildren.size-1)*s,l=i===1?(c=0)=>c*s:(c=0)=>a-c*s;return Array.from(t.variantChildren).sort(ra).forEach((c,u)=>{c.notify("AnimationStart",e),r.push(Ae(c,e,{...o,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(r)}function ra(t,e){return t.sortNodePosition(e)}function oa(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(o=>Ae(t,o,n));s=Promise.all(i)}else if(typeof e=="string")s=Ae(t,e,n);else{const i=typeof e=="function"?re(t,e,n.custom):e;s=Promise.all(di(t,i,n))}return s.then(()=>t.notify("AnimationComplete",e))}const aa=[...ke].reverse(),la=ke.length;function ca(t){return e=>Promise.all(e.map(({animation:n,options:s})=>oa(t,n,s)))}function ua(t){let e=ca(t);const n=fa();let s=!0;const i=(l,c)=>{const u=re(t,c);if(u){const{transition:h,transitionEnd:f,...d}=u;l={...l,...d,...f}}return l};function o(l){e=l(t)}function r(l,c){const u=t.getProps(),h=t.getVariantContext(!0)||{},f=[],d=new Set;let m={},p=1/0;for(let T=0;T<la;T++){const v=aa[T],g=n[v],P=u[v]!==void 0?u[v]:h[v],V=Rt(P),w=v===c?g.isActive:null;w===!1&&(p=T);let D=P===h[v]&&P!==u[v]&&V;if(D&&s&&t.manuallyAnimateOnMount&&(D=!1),g.protectedKeys={...m},!g.isActive&&w===null||!P&&!g.prevProp||ee(P)||typeof P=="boolean")continue;let C=ha(g.prevProp,P)||v===c&&g.isActive&&!D&&V||T>p&&V,U=!1;const ht=Array.isArray(P)?P:[P];let G=ht.reduce(i,{});w===!1&&(G={});const{prevResolvedValues:$={}}=g,k={...$,...G},ft=F=>{C=!0,d.has(F)&&(U=!0,d.delete(F)),g.needsAnimating[F]=!0};for(const F in k){const Y=G[F],Pt=$[F];if(m.hasOwnProperty(F))continue;let dt=!1;Kt(Y)&&Kt(Pt)?dt=!js(Y,Pt):dt=Y!==Pt,dt?Y!==void 0?ft(F):d.add(F):Y!==void 0&&d.has(F)?ft(F):g.protectedKeys[F]=!0}g.prevProp=P,g.prevResolvedValues=G,g.isActive&&(m={...m,...G}),s&&t.blockInitialAnimation&&(C=!1),C&&(!D||U)&&f.push(...ht.map(F=>({animation:F,options:{type:v,...l}})))}if(d.size){const T={};d.forEach(v=>{const g=t.getBaseTarget(v);g!==void 0&&(T[v]=g)}),f.push({animation:T})}let x=!!f.length;return s&&(u.initial===!1||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(x=!1),s=!1,x?e(f):Promise.resolve()}function a(l,c,u){var h;if(n[l].isActive===c)return Promise.resolve();(h=t.variantChildren)===null||h===void 0||h.forEach(d=>{var m;return(m=d.animationState)===null||m===void 0?void 0:m.setActive(l,c)}),n[l].isActive=c;const f=r(u,l);for(const d in n)n[d].protectedKeys={};return f}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n}}function ha(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!js(e,t):!1}function it(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function fa(){return{animate:it(!0),whileInView:it(),whileHover:it(),whileTap:it(),whileDrag:it(),whileFocus:it(),exit:it()}}class da extends nt{constructor(e){super(e),e.animationState||(e.animationState=ua(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();this.unmount(),ee(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let ma=0;class pa extends nt{constructor(){super(...arguments),this.id=ma++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n,custom:s}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;const o=this.node.animationState.setActive("exit",!e,{custom:s??this.node.getProps().custom});n&&!e&&o.then(()=>n(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const ga={animation:{Feature:da},exit:{Feature:pa}},Sn=(t,e)=>Math.abs(t-e);function ya(t,e){const n=Sn(t.x,e.x),s=Sn(t.y,e.y);return Math.sqrt(n**2+s**2)}class mi{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=ge(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=ya(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:m}=h,{timestamp:p}=B;this.history.push({...m,timestamp:p});const{onStart:x,onMove:T}=this.handlers;f||(x&&x(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),T&&T(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=pe(f,this.transformPagePoint),A.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:m,resumeAnimation:p}=this.handlers;if(this.dragSnapToOrigin&&p&&p(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=ge(h.type==="pointercancel"?this.lastMoveEventInfo:pe(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,x),m&&m(h,x)},!Ls(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const r=ie(e),a=pe(r,this.transformPagePoint),{point:l}=a,{timestamp:c}=B;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=n;u&&u(e,ge(a,this.history)),this.removeListeners=Q(K(this.contextWindow,"pointermove",this.handlePointerMove),K(this.contextWindow,"pointerup",this.handlePointerUp),K(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),X(this.updatePoint)}}function pe(t,e){return e?{point:e(t.point)}:t}function Cn(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ge({point:t},e){return{point:t,delta:Cn(t,pi(e)),offset:Cn(t,va(e)),velocity:xa(e,.1)}}function va(t){return t[0]}function pi(t){return t[t.length-1]}function xa(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=pi(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>J(e)));)n--;if(!s)return{x:0,y:0};const o=_(i.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const r={x:(i.x-s.x)/o,y:(i.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function I(t){return t.max-t.min}function we(t,e=0,n=.01){return Math.abs(t-e)<=n}function An(t,e,n,s=.5){t.origin=s,t.originPoint=R(e.min,e.max,t.origin),t.scale=I(n)/I(e),(we(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=R(n.min,n.max,t.origin)-t.originPoint,(we(t.translate)||isNaN(t.translate))&&(t.translate=0)}function Dt(t,e,n,s){An(t.x,e.x,n.x,s?s.originX:void 0),An(t.y,e.y,n.y,s?s.originY:void 0)}function wn(t,e,n){t.min=n.min+e.min,t.max=t.min+I(e)}function Pa(t,e,n){wn(t.x,e.x,n.x),wn(t.y,e.y,n.y)}function Dn(t,e,n){t.min=e.min-n.min,t.max=t.min+I(e)}function Mt(t,e,n){Dn(t.x,e.x,n.x),Dn(t.y,e.y,n.y)}function Ta(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?R(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?R(n,t,s.max):Math.min(t,n)),t}function Mn(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function ba(t,{top:e,left:n,bottom:s,right:i}){return{x:Mn(t.x,n,i),y:Mn(t.y,e,s)}}function Rn(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function Va(t,e){return{x:Rn(t.x,e.x),y:Rn(t.y,e.y)}}function Sa(t,e){let n=.5;const s=I(t),i=I(e);return i>s?n=Et(e.min,e.max-s,t.min):s>i&&(n=Et(t.min,t.max-i,e.min)),tt(0,1,n)}function Ca(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const De=.35;function Aa(t=De){return t===!1?t=0:t===!0&&(t=De),{x:Ln(t,"left","right"),y:Ln(t,"top","bottom")}}function Ln(t,e,n){return{min:En(t,e),max:En(t,n)}}function En(t,e){return typeof t=="number"?t:t[e]||0}const Fn=()=>({translate:0,scale:1,origin:0,originPoint:0}),gt=()=>({x:Fn(),y:Fn()}),Bn=()=>({min:0,max:0}),E=()=>({x:Bn(),y:Bn()});function W(t){return[t("x"),t("y")]}function gi({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function wa({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Da(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function ye(t){return t===void 0||t===1}function Me({scale:t,scaleX:e,scaleY:n}){return!ye(t)||!ye(e)||!ye(n)}function rt(t){return Me(t)||yi(t)||t.z||t.rotate||t.rotateX||t.rotateY}function yi(t){return kn(t.x)||kn(t.y)}function kn(t){return t&&t!=="0%"}function Zt(t,e,n){const s=t-n,i=e*s;return n+i}function jn(t,e,n,s,i){return i!==void 0&&(t=Zt(t,i,s)),Zt(t,n,s)+e}function Re(t,e=0,n=1,s,i){t.min=jn(t.min,e,n,s,i),t.max=jn(t.max,e,n,s,i)}function vi(t,{x:e,y:n}){Re(t.x,e.translate,e.scale,e.originPoint),Re(t.y,n.translate,n.scale,n.originPoint)}function Ma(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let o,r;for(let a=0;a<i;a++){o=n[a],r=o.projectionDelta;const l=o.instance;l&&l.style&&l.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&yt(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,vi(t,r)),s&&rt(o.latestValues)&&yt(t,o.latestValues))}e.x=On(e.x),e.y=On(e.y)}function On(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function Z(t,e){t.min=t.min+e,t.max=t.max+e}function In(t,e,[n,s,i]){const o=e[i]!==void 0?e[i]:.5,r=R(t.min,t.max,o);Re(t,e[n],e[s],r,e.scale)}const Ra=["x","scaleX","originX"],La=["y","scaleY","originY"];function yt(t,e){In(t.x,e,Ra),In(t.y,e,La)}function xi(t,e){return gi(Da(t.getBoundingClientRect(),e))}function Ea(t,e,n){const s=xi(t,n),{scroll:i}=e;return i&&(Z(s.x,i.offset.x),Z(s.y,i.offset.y)),s}const Pi=({current:t})=>t?t.ownerDocument.defaultView:null,Fa=new WeakMap;class Ba{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=E(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const i=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(ie(u,"page").point)},o=(u,h)=>{const{drag:f,dragPropagation:d,onDragStart:m}=this.getProps();if(f&&!d&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Fs(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),W(x=>{let T=this.getAxisMotionValue(x).get()||0;if(H.test(T)){const{projection:v}=this.visualElement;if(v&&v.layout){const g=v.layout.layoutBox[x];g&&(T=I(g)*(parseFloat(T)/100))}}this.originPoint[x]=T}),m&&A.update(()=>m(u,h),!1,!0);const{animationState:p}=this.visualElement;p&&p.setActive("whileDrag",!0)},r=(u,h)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:m,onDrag:p}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:x}=h;if(d&&this.currentDirection===null){this.currentDirection=ka(x),this.currentDirection!==null&&m&&m(this.currentDirection);return}this.updateAxis("x",h.point,x),this.updateAxis("y",h.point,x),this.visualElement.render(),p&&p(u,h)},a=(u,h)=>this.stop(u,h),l=()=>W(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new mi(e,{onSessionStart:i,onStart:o,onMove:r,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:Pi(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&A.update(()=>o(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!Wt(e,i,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(r=Ta(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var e;const{dragConstraints:n,dragElastic:s}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,o=this.constraints;n&&mt(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=ba(i.layoutBox,n):this.constraints=!1,this.elastic=Aa(s),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&W(r=>{this.getAxisMotionValue(r)&&(this.constraints[r]=Ca(i.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!mt(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=Ea(s,i.root,this.visualElement.getTransformPagePoint());let r=Va(i.layout.layoutBox,o);if(n){const a=n(wa(r));this.hasMutatedConstraints=!!a,a&&(r=gi(a))}return r}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=W(u=>{if(!Wt(u,n,this.currentDirection))return;let h=l&&l[u]||{};r&&(h={min:0,max:0});const f=i?200:1e6,d=i?40:1e7,m={type:"inertia",velocity:s?e[u]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(u,m)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return s.start(Ze(e,s,0,n))}stopAnimation(){W(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){W(e=>{var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(e){const n="_drag"+e.toUpperCase(),s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){W(n=>{const{drag:s}=this.getProps();if(!Wt(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:r,max:a}=i.layout.layoutBox[n];o.set(e[n]-R(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!mt(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};W(r=>{const a=this.getAxisMotionValue(r);if(a){const l=a.get();i[r]=Sa({min:l,max:l},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),W(r=>{if(!Wt(r,e,null))return;const a=this.getAxisMotionValue(r),{min:l,max:c}=this.constraints[r];a.set(R(l,c,i[r]))})}addListeners(){if(!this.visualElement.current)return;Fa.set(this.visualElement,this);const e=this.visualElement.current,n=K(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();mt(l)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),s();const r=z(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(W(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:r=De,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function Wt(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function ka(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class ja extends nt{constructor(e){super(e),this.removeGroupControls=L,this.removeListeners=L,this.controls=new Ba(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||L}unmount(){this.removeGroupControls(),this.removeListeners()}}const Un=t=>(e,n)=>{t&&A.update(()=>t(e,n))};class Oa extends nt{constructor(){super(...arguments),this.removePointerDownListener=L}onPointerDown(e){this.session=new mi(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Pi(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:Un(e),onStart:Un(n),onMove:s,onEnd:(o,r)=>{delete this.session,i&&A.update(()=>i(o,r))}}}mount(){this.removePointerDownListener=K(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function Ia(){const t=y.useContext(Jt);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:n,register:s}=t,i=y.useId();return y.useEffect(()=>s(i),[]),!e&&n?[!1,()=>n&&n(i)]:[!0]}const Ht={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Nn(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const St={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(b.test(t))t=parseFloat(t);else return t;const n=Nn(t,e.target.x),s=Nn(t,e.target.y);return`${n}% ${s}%`}},Ua={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=et.parse(t);if(i.length>5)return s;const o=et.createTransformer(t),r=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+r]/=a,i[1+r]/=l;const c=R(a,l,.5);return typeof i[2+r]=="number"&&(i[2+r]/=c),typeof i[3+r]=="number"&&(i[3+r]/=c),o(i)}};class Na extends hs.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:o}=e;Ki(Wa),o&&(n.group&&n.group.add(o),s&&s.register&&i&&s.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Ht.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:o}=this.props,r=s.projection;return r&&(r.isPresent=o,i||e.layoutDependency!==n||n===void 0?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||A.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Ti(t){const[e,n]=Ia(),s=y.useContext(Oe);return hs.createElement(Na,{...t,layoutGroup:s,switchLayoutGroup:y.useContext(gs),isPresent:e,safeToRemove:n})}const Wa={borderRadius:{...St,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:St,borderTopRightRadius:St,borderBottomLeftRadius:St,borderBottomRightRadius:St,boxShadow:Ua},bi=["TopLeft","TopRight","BottomLeft","BottomRight"],Ga=bi.length,Wn=t=>typeof t=="string"?parseFloat(t):t,Gn=t=>typeof t=="number"||b.test(t);function Ha(t,e,n,s,i,o){i?(t.opacity=R(0,n.opacity!==void 0?n.opacity:1,$a(s)),t.opacityExit=R(e.opacity!==void 0?e.opacity:1,0,za(s))):o&&(t.opacity=R(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,s));for(let r=0;r<Ga;r++){const a=`border${bi[r]}Radius`;let l=Hn(e,a),c=Hn(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||Gn(l)===Gn(c)?(t[a]=Math.max(R(Wn(l),Wn(c),s),0),(H.test(c)||H.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=R(e.rotate||0,n.rotate||0,s))}function Hn(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const $a=Vi(0,.5,zs),za=Vi(.5,.95,L);function Vi(t,e,n){return s=>s<t?0:s>e?1:n(Et(t,e,s))}function $n(t,e){t.min=e.min,t.max=e.max}function N(t,e){$n(t.x,e.x),$n(t.y,e.y)}function zn(t,e,n,s,i){return t-=e,t=Zt(t,1/n,s),i!==void 0&&(t=Zt(t,1/i,s)),t}function Ka(t,e=0,n=1,s=.5,i,o=t,r=t){if(H.test(e)&&(e=parseFloat(e),e=R(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=R(o.min,o.max,s);t===o&&(a-=e),t.min=zn(t.min,e,n,a,i),t.max=zn(t.max,e,n,a,i)}function Kn(t,e,[n,s,i],o,r){Ka(t,e[n],e[s],e[i],e.scale,o,r)}const _a=["x","scaleX","originX"],Xa=["y","scaleY","originY"];function _n(t,e,n,s){Kn(t.x,e,_a,n?n.x:void 0,s?s.x:void 0),Kn(t.y,e,Xa,n?n.y:void 0,s?s.y:void 0)}function Xn(t){return t.translate===0&&t.scale===1}function Si(t){return Xn(t.x)&&Xn(t.y)}function Ya(t,e){return t.x.min===e.x.min&&t.x.max===e.x.max&&t.y.min===e.y.min&&t.y.max===e.y.max}function Ci(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function Yn(t){return I(t.x)/I(t.y)}class qa{constructor(){this.members=[]}add(e){Qe(this.members,e),e.scheduleRender()}remove(e){if(Je(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function qn(t,e,n){let s="";const i=t.x.translate/e.x,o=t.y.translate/e.y;if((i||o)&&(s=`translate3d(${i}px, ${o}px, 0) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{rotate:l,rotateX:c,rotateY:u}=n;l&&(s+=`rotate(${l}deg) `),c&&(s+=`rotateX(${c}deg) `),u&&(s+=`rotateY(${u}deg) `)}const r=t.x.scale*e.x,a=t.y.scale*e.y;return(r!==1||a!==1)&&(s+=`scale(${r}, ${a})`),s||"none"}const Za=(t,e)=>t.depth-e.depth;class Qa{constructor(){this.children=[],this.isDirty=!1}add(e){Qe(this.children,e),this.isDirty=!0}remove(e){Je(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Za),this.isDirty=!1,this.children.forEach(e)}}function Ja(t,e){const n=performance.now(),s=({timestamp:i})=>{const o=i-n;o>=e&&(X(s),t(o-e))};return A.read(s,!0),()=>X(s)}function tl(t){window.MotionDebug&&window.MotionDebug.record(t)}function el(t){return t instanceof SVGElement&&t.tagName!=="svg"}function nl(t,e,n){const s=O(t)?t:vt(t);return s.start(Ze("",s,e,n)),s.animation}const Zn=["","X","Y","Z"],sl={visibility:"hidden"},Qn=1e3;let il=0;const ot={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Ai({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(r={},a=e==null?void 0:e()){this.id=il++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ot.totalNodes=ot.resolvedTargetDeltas=ot.recalculatedProjection=0,this.nodes.forEach(al),this.nodes.forEach(fl),this.nodes.forEach(dl),this.nodes.forEach(ll),tl(ot)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Qa)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new tn),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const l=this.eventHandlers.get(r);l&&l.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=el(r),this.instance=r;const{layoutId:l,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||l)&&(this.isLayoutDirty=!0),t){let h;const f=()=>this.root.updateBlockedByResize=!1;t(r,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=Ja(f,250),Ht.hasAnimatedSinceResize&&(Ht.hasAnimatedSinceResize=!1,this.nodes.forEach(ts))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&u&&(l||c)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:d,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const p=this.options.transition||u.getDefaultTransition()||vl,{onLayoutAnimationStart:x,onLayoutAnimationComplete:T}=u.getProps(),v=!this.targetLayout||!Ci(this.targetLayout,m)||d,g=!f&&d;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||f&&(v||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,g);const P={...qe(p,"layout"),onPlay:x,onComplete:T};(u.shouldReduceMotion||this.options.layoutRoot)&&(P.delay=0,P.type=!1),this.startAnimation(P)}else f||ts(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,X(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ml),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Jn);return}this.isUpdating||this.nodes.forEach(ul),this.isUpdating=!1,this.nodes.forEach(hl),this.nodes.forEach(rl),this.nodes.forEach(ol),this.clearAllSnapshots();const a=performance.now();B.delta=tt(0,1e3/60,a-B.timestamp),B.timestamp=a,B.isProcessing=!0,ae.update.process(B),ae.preRender.process(B),ae.render.process(B),B.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(cl),this.sharedNodes.forEach(pl)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,A.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){A.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=E(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:r,isRoot:s(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const r=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!Si(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;r&&(a||rt(this.latestValues)||u)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return r&&(l=this.removeTransform(l)),xl(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:r}=this.options;if(!r)return E();const a=r.measureViewportBox(),{scroll:l}=this.root;return l&&(Z(a.x,l.offset.x),Z(a.y,l.offset.y)),a}removeElementScroll(r){const a=E();N(a,r);for(let l=0;l<this.path.length;l++){const c=this.path[l],{scroll:u,options:h}=c;if(c!==this.root&&u&&h.layoutScroll){if(u.isRoot){N(a,r);const{scroll:f}=this.root;f&&(Z(a.x,-f.offset.x),Z(a.y,-f.offset.y))}Z(a.x,u.offset.x),Z(a.y,u.offset.y)}}return a}applyTransform(r,a=!1){const l=E();N(l,r);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&yt(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),rt(u.latestValues)&&yt(l,u.latestValues)}return rt(this.latestValues)&&yt(l,this.latestValues),l}removeTransform(r){const a=E();N(a,r);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!rt(c.latestValues))continue;Me(c.latestValues)&&c.updateSnapshot();const u=E(),h=c.measurePageBox();N(u,h),_n(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return rt(this.latestValues)&&_n(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==B.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==l;if(!(r||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=B.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=E(),this.relativeTargetOrigin=E(),Mt(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),N(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=E(),this.targetWithTransforms=E()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Pa(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):N(this.target,this.layout.layoutBox),vi(this.target,this.targetDelta)):N(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=E(),this.relativeTargetOrigin=E(),Mt(this.relativeTargetOrigin,this.target,d.target),N(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ot.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Me(this.parent.latestValues)||yi(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var r;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((r=this.parent)===null||r===void 0)&&r.isProjectionDirty)&&(c=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===B.timestamp&&(c=!1),c)return;const{layout:u,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||h))return;N(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,d=this.treeScale.y;Ma(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:m}=a;if(!m){this.projectionTransform&&(this.projectionDelta=gt(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=gt(),this.projectionDeltaWithTransform=gt());const p=this.projectionTransform;Dt(this.projectionDelta,this.layoutCorrected,m,this.latestValues),this.projectionTransform=qn(this.projectionDelta,this.treeScale),(this.projectionTransform!==p||this.treeScale.x!==f||this.treeScale.y!==d)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m)),ot.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),r){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(r,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=gt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=E(),d=l?l.source:void 0,m=this.layout?this.layout.source:void 0,p=d!==m,x=this.getStack(),T=!x||x.members.length<=1,v=!!(p&&!T&&this.options.crossfade===!0&&!this.path.some(yl));this.animationProgress=0;let g;this.mixTargetDelta=P=>{const V=P/1e3;es(h.x,r.x,V),es(h.y,r.y,V),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Mt(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),gl(this.relativeTarget,this.relativeTargetOrigin,f,V),g&&Ya(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=E()),N(g,this.relativeTarget)),p&&(this.animationValues=u,Ha(u,c,this.latestValues,V,v,T)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=V},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(X(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=A.update(()=>{Ht.hasAnimatedSinceResize=!0,this.currentAnimation=nl(0,Qn,{...r,onUpdate:a=>{this.mixTargetDelta(a),r.onUpdate&&r.onUpdate(a)},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Qn),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=r;if(!(!a||!l||!c)){if(this!==r&&this.layout&&c&&wi(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||E();const h=I(this.layout.layoutBox.x);l.x.min=r.target.x.min,l.x.max=l.x.min+h;const f=I(this.layout.layoutBox.y);l.y.min=r.target.y.min,l.y.max=l.y.min+f}N(a,l),yt(a,u),Dt(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new qa),this.sharedNodes.get(r).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var r;const{layoutId:a}=this.options;return a?((r=this.getStack())===null||r===void 0?void 0:r.lead)||this:this}getPrevLead(){var r;const{layoutId:a}=this.options;return a?(r=this.getStack())===null||r===void 0?void 0:r.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:l}=r;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const c={};for(let u=0;u<Zn.length;u++){const h="rotate"+Zn[u];l[h]&&(c[h]=l[h],r.setStaticValue(h,0))}r.render();for(const u in c)r.setStaticValue(u,c[u]);r.scheduleRender()}getProjectionStyles(r){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return sl;const c={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=Gt(r==null?void 0:r.pointerEvents)||"",c.transform=u?u(this.latestValues,""):"none",c;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const p={};return this.options.layoutId&&(p.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,p.pointerEvents=Gt(r==null?void 0:r.pointerEvents)||""),this.hasProjected&&!rt(this.latestValues)&&(p.transform=u?u({},""):"none",this.hasProjected=!1),p}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),c.transform=qn(this.projectionDeltaWithTransform,this.treeScale,f),u&&(c.transform=u(f,c.transform));const{x:d,y:m}=this.projectionDelta;c.transformOrigin=`${d.origin*100}% ${m.origin*100}% 0`,h.animationValues?c.opacity=h===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:c.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const p in $t){if(f[p]===void 0)continue;const{correct:x,applyTo:T}=$t[p],v=c.transform==="none"?f[p]:x(f[p],h);if(T){const g=T.length;for(let P=0;P<g;P++)c[T[P]]=v}else c[p]=v}return this.options.layoutId&&(c.pointerEvents=h===this?Gt(r==null?void 0:r.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Jn),this.root.sharedNodes.clear()}}}function rl(t){t.updateLayout()}function ol(t){var e;const n=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:o}=t.options,r=n.source!==t.layout.source;o==="size"?W(h=>{const f=r?n.measuredBox[h]:n.layoutBox[h],d=I(f);f.min=s[h].min,f.max=f.min+d}):wi(o,n.layoutBox,s)&&W(h=>{const f=r?n.measuredBox[h]:n.layoutBox[h],d=I(s[h]);f.max=f.min+d,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+d)});const a=gt();Dt(a,s,n.layoutBox);const l=gt();r?Dt(l,t.applyTransform(i,!0),n.measuredBox):Dt(l,s,n.layoutBox);const c=!Si(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const m=E();Mt(m,n.layoutBox,f.layoutBox);const p=E();Mt(p,s,d.layoutBox),Ci(m,p)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=p,t.relativeTargetOrigin=m,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function al(t){ot.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function ll(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function cl(t){t.clearSnapshot()}function Jn(t){t.clearMeasurements()}function ul(t){t.isLayoutDirty=!1}function hl(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ts(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function fl(t){t.resolveTargetDelta()}function dl(t){t.calcProjection()}function ml(t){t.resetRotation()}function pl(t){t.removeLeadSnapshot()}function es(t,e,n){t.translate=R(e.translate,0,n),t.scale=R(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function ns(t,e,n,s){t.min=R(e.min,n.min,s),t.max=R(e.max,n.max,s)}function gl(t,e,n,s){ns(t.x,e.x,n.x,s),ns(t.y,e.y,n.y,s)}function yl(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const vl={duration:.45,ease:[.4,0,.1,1]},ss=t=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(t),is=ss("applewebkit/")&&!ss("chrome/")?Math.round:L;function rs(t){t.min=is(t.min),t.max=is(t.max)}function xl(t){rs(t.x),rs(t.y)}function wi(t,e,n){return t==="position"||t==="preserve-aspect"&&!we(Yn(e),Yn(n),.2)}const Pl=Ai({attachResizeListener:(t,e)=>z(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ve={current:void 0},Di=Ai({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ve.current){const t=new Pl({});t.mount(window),t.setOptions({layoutScroll:!0}),ve.current=t}return ve.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Tl={pan:{Feature:Oa},drag:{Feature:ja,ProjectionNode:Di,MeasureLayout:Ti}},bl=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Vl(t){const e=bl.exec(t);if(!e)return[,];const[,n,s]=e;return[n,s]}function Le(t,e,n=1){const[s,i]=Vl(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const r=o.trim();return ui(r)?parseFloat(r):r}else return Pe(i)?Le(i,e,n+1):i}function Sl(t,{...e},n){const s=t.current;if(!(s instanceof Element))return{target:e,transitionEnd:n};n&&(n={...n}),t.values.forEach(i=>{const o=i.get();if(!Pe(o))return;const r=Le(o,s);r&&i.set(r)});for(const i in e){const o=e[i];if(!Pe(o))continue;const r=Le(o,s);r&&(e[i]=r,n||(n={}),n[i]===void 0&&(n[i]=o))}return{target:e,transitionEnd:n}}const Cl=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Mi=t=>Cl.has(t),Al=t=>Object.keys(t).some(Mi),os=t=>t===ut||t===b,as=(t,e)=>parseFloat(t.split(", ")[e]),ls=(t,e)=>(n,{transform:s})=>{if(s==="none"||!s)return 0;const i=s.match(/^matrix3d\((.+)\)$/);if(i)return as(i[1],e);{const o=s.match(/^matrix\((.+)\)$/);return o?as(o[1],t):0}},wl=new Set(["x","y","z"]),Dl=Ft.filter(t=>!wl.has(t));function Ml(t){const e=[];return Dl.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e.length&&t.render(),e}const xt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:ls(4,13),y:ls(5,14)};xt.translateX=xt.x;xt.translateY=xt.y;const Rl=(t,e,n)=>{const s=e.measureViewportBox(),i=e.current,o=getComputedStyle(i),{display:r}=o,a={};r==="none"&&e.setStaticValue("display",t.display||"block"),n.forEach(c=>{a[c]=xt[c](s,o)}),e.render();const l=e.measureViewportBox();return n.forEach(c=>{const u=e.getValue(c);u&&u.jump(a[c]),t[c]=xt[c](l,o)}),t},Ll=(t,e,n={},s={})=>{e={...e},s={...s};const i=Object.keys(e).filter(Mi);let o=[],r=!1;const a=[];if(i.forEach(l=>{const c=t.getValue(l);if(!t.hasValue(l))return;let u=n[l],h=Vt(u);const f=e[l];let d;if(Kt(f)){const m=f.length,p=f[0]===null?1:0;u=f[p],h=Vt(u);for(let x=p;x<m&&f[x]!==null;x++)d?ze(Vt(f[x])===d):d=Vt(f[x])}else d=Vt(f);if(h!==d)if(os(h)&&os(d)){const m=c.get();typeof m=="string"&&c.set(parseFloat(m)),typeof f=="string"?e[l]=parseFloat(f):Array.isArray(f)&&d===b&&(e[l]=f.map(parseFloat))}else h!=null&&h.transform&&(d!=null&&d.transform)&&(u===0||f===0)?u===0?c.set(d.transform(u)):e[l]=h.transform(f):(r||(o=Ml(t),r=!0),a.push(l),s[l]=s[l]!==void 0?s[l]:e[l],c.jump(f))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,c=Rl(e,t,a);return o.length&&o.forEach(([u,h])=>{t.getValue(u).set(h)}),t.render(),te&&l!==null&&window.scrollTo({top:l}),{target:c,transitionEnd:s}}else return{target:e,transitionEnd:s}};function El(t,e,n,s){return Al(e)?Ll(t,e,n,s):{target:e,transitionEnd:s}}const Fl=(t,e,n,s)=>{const i=Sl(t,e,s);return e=i.target,s=i.transitionEnd,El(t,e,n,s)},Ee={current:null},Ri={current:!1};function Bl(){if(Ri.current=!0,!!te)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ee.current=t.matches;t.addListener(e),e()}else Ee.current=!1}function kl(t,e,n){const{willChange:s}=e;for(const i in e){const o=e[i],r=n[i];if(O(o))t.addValue(i,o),qt(s)&&s.add(i);else if(O(r))t.addValue(i,vt(o,{owner:t})),qt(s)&&s.remove(i);else if(r!==o)if(t.hasValue(i)){const a=t.getValue(i);!a.hasAnimated&&a.set(o)}else{const a=t.getStaticValue(i);t.addValue(i,vt(a!==void 0?a:o,{owner:t}))}}for(const i in n)e[i]===void 0&&t.removeValue(i);return e}const cs=new WeakMap,Li=Object.keys(Lt),jl=Li.length,us=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Ol=je.length;class Il{constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>A.render(this.render,!1,!0);const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.isControllingVariants=ne(n),this.isVariantNode=ps(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:c,...u}=this.scrapeMotionValuesFromProps(n,{});for(const h in u){const f=u[h];a[h]!==void 0&&O(f)&&(f.set(a[h],!1),qt(c)&&c.add(h))}}scrapeMotionValuesFromProps(e,n){return{}}mount(e){this.current=e,cs.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),Ri.current||Bl(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ee.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){cs.delete(this.current),this.projection&&this.projection.unmount(),X(this.notifyUpdate),X(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,n){const s=ct.has(e),i=n.on("change",r=>{this.latestValues[e]=r,this.props.onUpdate&&A.update(this.notifyUpdate,!1,!0),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{i(),o()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}loadFeatures({children:e,...n},s,i,o){let r,a;for(let l=0;l<jl;l++){const c=Li[l],{isEnabled:u,Feature:h,ProjectionNode:f,MeasureLayout:d}=Lt[c];f&&(r=f),u(n)&&(!this.features[c]&&h&&(this.features[c]=new h(this)),d&&(a=d))}if((this.type==="html"||this.type==="svg")&&!this.projection&&r){this.projection=new r(this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:c,drag:u,dragConstraints:h,layoutScroll:f,layoutRoot:d}=n;this.projection.setOptions({layoutId:l,layout:c,alwaysMeasureLayout:!!u||h&&mt(h),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof c=="string"?c:"both",initialPromotionConfig:o,layoutScroll:f,layoutRoot:d})}return a}updateFeatures(){for(const e in this.features){const n=this.features[e];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):E()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}makeTargetAnimatable(e,n=!0){return this.makeTargetAnimatableFromInstance(e,this.props,n)}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<us.length;s++){const i=us[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=e["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=kl(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const s=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(s.initial=this.props.initial),s}const n={};for(let s=0;s<Ol;s++){const i=je[s],o=this.props[i];(Rt(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){n!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,n)),this.values.set(e,n),this.latestValues[e]=n.get()}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=vt(n,{owner:this}),this.addValue(e,s)),s}readValue(e){var n;return this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(n=this.getBaseTargetFromProps(this.props,e))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,e,this.options)}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:s}=this.props,i=typeof s=="string"||typeof s=="object"?(n=$e(this.props,s))===null||n===void 0?void 0:n[e]:void 0;if(s&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,e);return o!==void 0&&!O(o)?o:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new tn),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class Ei extends Il{sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:n,...s},{transformValues:i},o){let r=ea(s,e||{},this);if(i&&(n&&(n=i(n)),s&&(s=i(s)),r&&(r=i(r))),o){Jo(this,s,r);const a=Fl(this,s,r,n);n=a.transitionEnd,s=a.target}return{transition:e,transitionEnd:n,...s}}}function Ul(t){return window.getComputedStyle(t)}class Nl extends Ei{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,n){if(ct.has(n)){const s=Ye(n);return s&&s.default||0}else{const s=Ul(e),i=(xs(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return xi(e,n)}build(e,n,s,i){Ue(e,n,s,i.transformTemplate)}scrapeMotionValuesFromProps(e,n){return He(e,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;O(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(e,n,s,i){Cs(e,n,s,i)}}class Wl extends Ei{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(ct.has(n)){const s=Ye(n);return s&&s.default||0}return n=As.has(n)?n:Be(n),e.getAttribute(n)}measureInstanceViewportBox(){return E()}scrapeMotionValuesFromProps(e,n){return Ds(e,n)}build(e,n,s,i){We(e,n,s,this.isSVGTag,i.transformTemplate)}renderInstance(e,n,s,i){ws(e,n,s,i)}mount(e){this.isSVGTag=Ge(e.tagName),super.mount(e)}}const Gl=(t,e)=>Ie(t)?new Wl(e,{enableHardwareAcceleration:!1}):new Nl(e,{enableHardwareAcceleration:!0}),Hl={layout:{ProjectionNode:Di,MeasureLayout:Ti}},$l={...ga,...kr,...Tl,...Hl},ec=$i((t,e)=>br(t,e,$l,Gl));function Fi(){const t=y.useRef(!1);return Fe(()=>(t.current=!0,()=>{t.current=!1}),[]),t}function zl(){const t=Fi(),[e,n]=y.useState(0),s=y.useCallback(()=>{t.current&&n(e+1)},[e]);return[y.useCallback(()=>A.postRender(s),[s]),e]}class Kl extends y.Component{getSnapshotBeforeUpdate(e){const n=this.props.childRef.current;if(n&&e.isPresent&&!this.props.isPresent){const s=this.props.sizeRef.current;s.height=n.offsetHeight||0,s.width=n.offsetWidth||0,s.top=n.offsetTop,s.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function _l({children:t,isPresent:e}){const n=y.useId(),s=y.useRef(null),i=y.useRef({width:0,height:0,top:0,left:0});return y.useInsertionEffect(()=>{const{width:o,height:r,top:a,left:l}=i.current;if(e||!s.current||!o||!r)return;s.current.dataset.motionPopId=n;const c=document.createElement("style");return document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${r}px !important;
            top: ${a}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[e]),y.createElement(Kl,{isPresent:e,childRef:s,sizeRef:i},y.cloneElement(t,{ref:s}))}const xe=({children:t,initial:e,isPresent:n,onExitComplete:s,custom:i,presenceAffectsLayout:o,mode:r})=>{const a=Ms(Xl),l=y.useId(),c=y.useMemo(()=>({id:l,initial:e,isPresent:n,custom:i,onExitComplete:u=>{a.set(u,!0);for(const h of a.values())if(!h)return;s&&s()},register:u=>(a.set(u,!1),()=>a.delete(u))}),o?void 0:[n]);return y.useMemo(()=>{a.forEach((u,h)=>a.set(h,!1))},[n]),y.useEffect(()=>{!n&&!a.size&&s&&s()},[n]),r==="popLayout"&&(t=y.createElement(_l,{isPresent:n},t)),y.createElement(Jt.Provider,{value:c},t)};function Xl(){return new Map}function Yl(t){return y.useEffect(()=>()=>t(),[])}const at=t=>t.key||"";function ql(t,e){t.forEach(n=>{const s=at(n);e.set(s,n)})}function Zl(t){const e=[];return y.Children.forEach(t,n=>{y.isValidElement(n)&&e.push(n)}),e}const nc=({children:t,custom:e,initial:n=!0,onExitComplete:s,exitBeforeEnter:i,presenceAffectsLayout:o=!0,mode:r="sync"})=>{const a=y.useContext(Oe).forceRender||zl()[0],l=Fi(),c=Zl(t);let u=c;const h=y.useRef(new Map).current,f=y.useRef(u),d=y.useRef(new Map).current,m=y.useRef(!0);if(Fe(()=>{m.current=!1,ql(c,d),f.current=u}),Yl(()=>{m.current=!0,d.clear(),h.clear()}),m.current)return y.createElement(y.Fragment,null,u.map(v=>y.createElement(xe,{key:at(v),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:o,mode:r},v)));u=[...u];const p=f.current.map(at),x=c.map(at),T=p.length;for(let v=0;v<T;v++){const g=p[v];x.indexOf(g)===-1&&!h.has(g)&&h.set(g,void 0)}return r==="wait"&&h.size&&(u=[]),h.forEach((v,g)=>{if(x.indexOf(g)!==-1)return;const P=d.get(g);if(!P)return;const V=p.indexOf(g);let w=v;if(!w){const D=()=>{h.delete(g);const S=Array.from(d.keys()).filter(C=>!x.includes(C));if(S.forEach(C=>d.delete(C)),f.current=c.filter(C=>{const U=at(C);return U===g||S.includes(U)}),!h.size){if(l.current===!1)return;a(),s&&s()}};w=y.createElement(xe,{key:at(P),isPresent:!1,onExitComplete:D,custom:e,presenceAffectsLayout:o,mode:r},P),h.set(g,w)}u.splice(V,0,w)}),u=u.map(v=>{const g=v.key;return h.has(g)?v:y.createElement(xe,{key:at(v),isPresent:!0,presenceAffectsLayout:o,mode:r},v)}),y.createElement(y.Fragment,null,h.size?u:u.map(v=>y.cloneElement(v)))};export{nc as A,ec as m};
