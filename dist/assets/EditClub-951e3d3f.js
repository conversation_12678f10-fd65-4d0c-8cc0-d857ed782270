import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as u}from"./vendor-851db8c1.js";import{M as T,G as L,e as M,b as f}from"./index-13fd629e.js";let E=new T;function O({data:s,getData:p,setData:N,onCancelMembership:d,membershipLoading:g}){var _,w,C,S,D,$,A;const{dispatch:j}=u.useContext(L),[o,t]=u.useState(!1),y=async()=>{t(!0),E.setTable("user"),(await E.callRestAPI({id:s.user.id,status:1},"PUT")).error||(f(j,"Membership activated successfully",5e3,"success"),p(1,10),N(n=>({...n,user:{...n.user,status:1}})),t(!1)),t(!1)},l=(r,n=null)=>{try{return r?JSON.parse(r):n}catch(h){return console.error("Error parsing JSON:",h),n}},m=r=>{if(!r)return"N/A";try{const[n,h]=r.split(":"),b=parseInt(n),k=b>=12?"PM":"AM";return`${b%12||12}:${h} ${k}`}catch{return r}},c=l(s==null?void 0:s.club_location),i=l(s==null?void 0:s.splash_screen),x=l(s==null?void 0:s.times,[]),a=l(s==null?void 0:s.membership_settings,[]);l(s==null?void 0:s.account_settings);const v=l(s==null?void 0:s.fee_settings,[]);return e.jsxs("div",{className:"max-w-2xl",children:[o&&e.jsx(M,{}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-gray-900",children:"Basic Information"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Club Name"}),e.jsx("p",{className:"text-sm text-gray-900",children:(s==null?void 0:s.name)||"N/A"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Slug"}),e.jsx("p",{className:"text-sm text-gray-900",children:(s==null?void 0:s.slug)||"N/A"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Status"}),e.jsx("p",{className:"text-sm text-gray-900",children:(s==null?void 0:s.completed)===1?"Active":"Inactive"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"User ID"}),e.jsx("p",{className:"text-sm text-gray-900",children:(s==null?void 0:s.user_id)||"N/A"})]})]})]}),(s==null?void 0:s.description)&&e.jsxs("div",{className:"mb-8",children:[e.jsx("h4",{className:"mb-2 text-sm font-medium text-gray-600",children:"Description"}),e.jsx("p",{className:"text-sm text-gray-900",children:s.description})]}),c&&e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-gray-900",children:"Location"}),e.jsxs("div",{className:"space-y-2",children:[c.address&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Address"}),e.jsx("p",{className:"text-sm text-gray-900",children:c.address})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[c.lat&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Latitude"}),e.jsx("p",{className:"text-sm text-gray-900",children:c.lat})]}),c.lng&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Longitude"}),e.jsx("p",{className:"text-sm text-gray-900",children:c.lng})]})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-gray-900",children:"Operating Hours"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Opening Time"}),e.jsx("p",{className:"text-sm text-gray-900",children:m(s==null?void 0:s.opening_time)})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Closing Time"}),e.jsx("p",{className:"text-sm text-gray-900",children:m(s==null?void 0:s.closing_time)})]})]}),x.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h4",{className:"mb-2 text-sm font-medium text-gray-600",children:"Time Slots"}),e.jsx("div",{className:"space-y-1",children:x.map((r,n)=>e.jsxs("p",{className:"text-sm text-gray-900",children:[m(r.from)," - ",m(r.until)]},n))})]})]}),i&&(i.phone||i.email)&&e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-gray-900",children:"Contact Information"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[i.phone&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Phone"}),e.jsx("p",{className:"text-sm text-gray-900",children:i.phone})]}),i.email&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Email"}),e.jsx("p",{className:"text-sm text-gray-900",children:i.email})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-gray-900",children:"Features"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Court Selection"}),e.jsx("span",{className:`font-medium ${s!=null&&s.allow_user_court_selection?"text-green-600":"text-red-600"}`,children:s!=null&&s.allow_user_court_selection?"Enabled":"Disabled"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Show Clinic"}),e.jsx("span",{className:`font-medium ${s!=null&&s.show_clinic?"text-green-600":"text-red-600"}`,children:s!=null&&s.show_clinic?"Enabled":"Disabled"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Show Buddy"}),e.jsx("span",{className:`font-medium ${s!=null&&s.show_buddy?"text-green-600":"text-red-600"}`,children:s!=null&&s.show_buddy?"Enabled":"Disabled"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Show Coach"}),e.jsx("span",{className:`font-medium ${s!=null&&s.show_coach?"text-green-600":"text-red-600"}`,children:s!=null&&s.show_coach?"Enabled":"Disabled"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Show Groups"}),e.jsx("span",{className:`font-medium ${s!=null&&s.show_groups?"text-green-600":"text-red-600"}`,children:s!=null&&s.show_groups?"Enabled":"Disabled"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Show Court"}),e.jsx("span",{className:`font-medium ${s!=null&&s.show_court?"text-green-600":"text-red-600"}`,children:s!=null&&s.show_court?"Enabled":"Disabled"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Support Chat"}),e.jsx("span",{className:`font-medium ${(s==null?void 0:s.supportchat_enabled)==="1"?"text-green-600":"text-red-600"}`,children:(s==null?void 0:s.supportchat_enabled)==="1"?"Enabled":"Disabled"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Show Notifications"}),e.jsx("span",{className:`font-medium ${s!=null&&s.show_notification?"text-green-600":"text-red-600"}`,children:s!=null&&s.show_notification?"Enabled":"Disabled"})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-gray-900",children:"Fees & Pricing"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Club Fee"}),e.jsxs("p",{className:"text-sm text-gray-900",children:["$",(s==null?void 0:s.club_fee)||0]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Service Fee"}),e.jsxs("p",{className:"text-sm text-gray-900",children:["$",(s==null?void 0:s.service_fee)||0]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Max Players"}),e.jsx("p",{className:"text-sm text-gray-900",children:(s==null?void 0:s.max_players)||"N/A"})]})]}),v.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h4",{className:"mb-2 text-sm font-medium text-gray-600",children:"Fee Settings"}),v.map((r,n)=>e.jsxs("div",{className:"rounded-lg bg-gray-50 p-3",children:[e.jsxs("p",{className:"text-sm",children:[e.jsx("span",{className:"font-medium",children:"Type:"})," ",r.fee_type||"N/A"]}),r.tech_fee_percentage&&e.jsxs("p",{className:"text-sm",children:[e.jsx("span",{className:"font-medium",children:"Percentage:"})," ",r.tech_fee_percentage,"%"]}),r.tech_fee_fixed&&e.jsxs("p",{className:"text-sm",children:[e.jsx("span",{className:"font-medium",children:"Fixed Fee:"})," $",r.tech_fee_fixed]})]},n))]})]}),a.length>0&&e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-gray-900",children:"Membership Plans"}),e.jsx("div",{className:"space-y-4",children:a.map((r,n)=>e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsx("h4",{className:"font-medium text-gray-900",children:r.plan_name}),e.jsxs("span",{className:"text-lg font-semibold text-blue-600",children:["$",r.price]})]}),e.jsxs("div",{className:"mb-3 grid grid-cols-2 gap-2 text-sm",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Clinic"}),e.jsx("span",{className:`font-medium ${r.allow_clinic?"text-green-600":"text-red-600"}`,children:r.allow_clinic?"Yes":"No"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Buddy"}),e.jsx("span",{className:`font-medium ${r.allow_buddy?"text-green-600":"text-red-600"}`,children:r.allow_buddy?"Yes":"No"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Coach"}),e.jsx("span",{className:`font-medium ${r.allow_coach?"text-green-600":"text-red-600"}`,children:r.allow_coach?"Yes":"No"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Court"}),e.jsx("span",{className:`font-medium ${r.allow_court?"text-green-600":"text-red-600"}`,children:r.allow_court?"Yes":"No"})]})]}),r.features&&r.features.length>0&&e.jsxs("div",{children:[e.jsx("h5",{className:"mb-2 text-sm font-medium text-gray-600",children:"Features"}),e.jsx("ul",{className:"space-y-1",children:r.features.map((h,b)=>e.jsxs("li",{className:"text-sm text-gray-900",children:["• ",h.text]},b))})]})]},n))})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-gray-900",children:"Contact Person"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Name"}),e.jsx("p",{className:"text-sm text-gray-900",children:(_=s==null?void 0:s.user)!=null&&_.first_name||(w=s==null?void 0:s.user)!=null&&w.last_name?`${s.user.first_name||""} ${s.user.last_name||""}`.trim():"N/A"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Email"}),e.jsx("p",{className:"text-sm text-gray-900",children:((C=s==null?void 0:s.user)==null?void 0:C.email)||"N/A"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Phone"}),e.jsx("p",{className:"text-sm text-gray-900",children:((S=s==null?void 0:s.user)==null?void 0:S.phone)||"N/A"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Status"}),e.jsx("p",{className:"text-sm text-gray-900",children:((D=s==null?void 0:s.user)==null?void 0:D.status)===1?"Active":"Inactive"})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-gray-900",children:"Important Dates"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Created"}),e.jsx("p",{className:"text-sm text-gray-900",children:s!=null&&s.create_at?new Date(s.create_at).toLocaleDateString():"N/A"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Last Updated"}),e.jsx("p",{className:"text-sm text-gray-900",children:s!=null&&s.update_at?new Date(s.update_at).toLocaleDateString():"N/A"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-1 text-sm font-medium text-gray-600",children:"Years Under Panel"}),e.jsxs("p",{className:"text-sm text-gray-900",children:[Math.floor((new Date().getTime()-new Date(s==null?void 0:s.create_at).getTime())/(1e3*60*60*24*365.25))," ","years"]})]})]})]}),e.jsx("div",{className:"border-t border-gray-200 pt-6",children:e.jsx("button",{onClick:(($=s==null?void 0:s.user)==null?void 0:$.status)===1?d:y,className:"rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 disabled:opacity-50",disabled:o||g,children:((A=s==null?void 0:s.user)==null?void 0:A.status)===1?"Cancel Membership":"Activate Membership"})})]})}let P=new T;function R({data:s,getData:p,setData:N,onClose:d}){const{dispatch:g}=u.useContext(L),[j,o]=u.useState(!1),[t,y]=u.useState({name:(s==null?void 0:s.name)||"",address:(s==null?void 0:s.address)||"",payment_method:(s==null?void 0:s.payment_method)||"",revenue_type:(s==null?void 0:s.revenue_type)||"",country:(s==null?void 0:s.country)||"",state:(s==null?void 0:s.state)||"",years_under_panel:(s==null?void 0:s.years_under_panel)||""}),l=c=>{const{name:i,value:x}=c.target;y(a=>({...a,[i]:x}))},m=async c=>{c.preventDefault(),o(!0);try{P.setTable("clubs"),(await P.callRestAPI({id:s.id,...t},"PUT")).error||(f(g,"Club updated successfully",5e3,"success"),N(x=>({...x,...t})),p(1,10),d())}catch(i){console.error("Error updating club:",i),f(g,"Error updating club",5e3,"error")}finally{o(!1)}};return e.jsxs("div",{className:"max-w-2xl",children:[j&&e.jsx(M,{}),e.jsxs("form",{onSubmit:m,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Club Name"}),e.jsx("input",{type:"text",name:"name",value:t.name,onChange:l,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Address"}),e.jsx("input",{type:"text",name:"address",value:t.address,onChange:l,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Payment Method"}),e.jsx("input",{type:"text",name:"payment_method",value:t.payment_method,onChange:l,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Revenue Type"}),e.jsx("input",{type:"text",name:"revenue_type",value:t.revenue_type,onChange:l,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Country"}),e.jsx("input",{type:"text",name:"country",value:t.country,onChange:l,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"State"}),e.jsx("input",{type:"text",name:"state",value:t.state,onChange:l,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Years Under Panel"}),e.jsx("input",{type:"number",name:"years_under_panel",value:t.years_under_panel,onChange:l,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex justify-end gap-3 pt-4",children:[e.jsx("button",{type:"button",onClick:d,className:"rounded-md border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:j,className:"rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50",children:"Save Changes"})]})]})]})}export{O as C,R as E};
