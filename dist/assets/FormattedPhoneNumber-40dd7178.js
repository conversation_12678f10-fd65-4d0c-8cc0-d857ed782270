import{j as r}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";const n=(t,s="US")=>{if(!t)return"";const e=t.replace(/\D/g,"");switch(s){case"US":if(e.length===10)return`(${e.slice(0,3)}) ${e.slice(3,6)}-${e.slice(6,10)}`;if(e.length===11&&e.startsWith("1"))return`+1 (${e.slice(1,4)}) ${e.slice(4,7)}-${e.slice(7,11)}`;break;case"INTL":if(e.length>10)return`+${e.slice(0,e.length-10)} ${e.slice(e.length-10,e.length-7)} ${e.slice(e.length-7,e.length-4)} ${e.slice(e.length-4)}`;break;default:if(e.length>6)return`${e.slice(0,3)}-${e.slice(3,6)}-${e.slice(6)}`}return e.length>6?`${e.slice(0,3)}-${e.slice(3,6)}-${e.slice(6)}`:t},o=({phoneNumber:t,format:s="US",className:e=""})=>{const l=n(t,s);return r.jsx("span",{className:e,children:l})};export{o as F,n as f};
