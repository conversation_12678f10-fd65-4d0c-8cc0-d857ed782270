const _=t=>{try{if(!t)return[];if(typeof t=="object")return Array.isArray(t)?t:[];const o=JSON.parse(t);return Array.isArray(o)?o:[]}catch(o){return console.error("Error parsing custom request threshold:",o),[]}},c=t=>!Array.isArray(t)||t.length===0?!1:t.some(o=>o.sport_types&&Array.isArray(o.sport_types)),y=(t,o=[])=>{const l=[];return t.forEach(e=>{var p;((p=e.sport_types)==null?void 0:p.some(r=>r.type&&r.type.trim()!==""))?e.sport_types.forEach(r=>{r.type&&r.type.trim()!==""&&(l.push({id:`${e.id}_type_${r.club_sport_type_id||r.type}`,name:`${e.name} - ${r.type}`,sport_name:e.name,sport_id:e.id,type:r.type,club_sport_type_id:r.club_sport_type_id,threshold:r.threshold||0,level:"type"}),r.subtype&&Array.isArray(r.subtype)&&r.subtype.forEach((i,h)=>{i&&i.trim()!==""&&l.push({id:`${e.id}_subtype_${r.club_sport_type_id||r.type}_${h}`,name:`${e.name} - ${r.type} - ${i}`,sport_name:e.name,sport_id:e.id,type:r.type,subtype:i,club_sport_type_id:r.club_sport_type_id,threshold:0,level:"subtype"})}))}):l.push({id:e.id,name:e.name,sport_id:e.id,threshold:e.threshold||0,level:"sport"})}),l},u=(t,o,l=null,e=null,d=4,p=[])=>{try{const r=_(t);if(!Array.isArray(r)||!o)return d;let i=r;c(r)&&(console.log("Converting old threshold format to new format for lookup"),i=y(r,p));let h=null;if(e&&l){const s=i.find(n=>n.level==="subtype"&&n.sport_id.toString()===o.toString()&&n.type===l&&n.subtype===e);s&&s.threshold>0&&(h=s.threshold)}if(h===null&&l){const s=i.find(n=>n.level==="type"&&n.sport_id.toString()===o.toString()&&n.type===l);s&&s.threshold>0&&(h=s.threshold)}if(h===null){const s=i.find(n=>n.level==="sport"&&n.sport_id.toString()===o.toString());s&&s.threshold>0&&(h=s.threshold)}return h||d}catch(r){return console.error("Error getting custom request threshold:",r),d}};export{u as g};
