import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{b as j,r as i}from"./vendor-851db8c1.js";import{u as J}from"./react-hook-form-687afde5.js";import{o as Y}from"./yup-2824f222.js";import{c as Z,a as Q}from"./yup-54691517.js";import{M as W,T as X,A as V,G as D,e as ee,d as te,t as S,b as p}from"./index-13fd629e.js";import{I as se}from"./ImageCropModal-9d3311e2.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-image-crop-1f5038af.js";let x=new W,ae=new X;const He=()=>{const E=Z({email:Q().email().required()}).required(),{dispatch:y}=j.useContext(V),[v,F]=i.useState(""),[T,h]=i.useState(!1),[r,N]=i.useState({}),[I,C]=i.useState(!0),[L,u]=i.useState(null),[g,d]=i.useState(""),[U,P]=i.useState(!1),[A,B]=i.useState(null),[M,k]=i.useState(!1),[O,R]=i.useState(null),{dispatch:c}=j.useContext(D),{register:oe,handleSubmit:re,setError:_,setValue:l,formState:{errors:le}}=J({resolver:Y(E)}),z=localStorage.getItem("user");async function b(){var a;C(!0);try{const t=await ae.getList("profile",{filter:[`user_id,eq,${z}`],join:["user|user_id"]}),e=(a=t==null?void 0:t.list)==null?void 0:a[0];if(e){const o=e.user||{},m=e.id,n={...e,...o,profile_id:m,user_id:o.id};N(n),l("email",o==null?void 0:o.email),l("first_name",o==null?void 0:o.first_name),l("last_name",o==null?void 0:o.last_name),l("phone",o==null?void 0:o.phone),l("bio",o==null?void 0:o.bio),F(o==null?void 0:o.photo),l("gender",e==null?void 0:e.gender),l("address",e==null?void 0:e.address),l("city",e==null?void 0:e.city),l("state",e==null?void 0:e.state),l("zip_code",e==null?void 0:e.zip_code),y({type:"UPDATE_PROFILE",payload:n}),C(!1)}}catch(t){S(y,t.response.data.message?t.response.data.message:t.message)}}const G=["email","first_name","last_name","phone","bio","photo","alternative_phone","age_group","family_role","password","verify","status"],$=["gender","address","city","state","zip_code","date_of_birth","country","house_no"],f=async(a,t)=>{try{h(!0);const e={[a]:t},o=G.includes(a),m=$.includes(a);if(o){x.setTable("user");const n=await x.callRestAPI({id:r==null?void 0:r.user_id,...e},"PUT");n.error?w(n):(p(c,"Profile Updated",4e3),u(null),d(""),b())}else if(m){x.setTable("profile");const n=await x.callRestAPI({id:r==null?void 0:r.profile_id,...e},"PUT");n.error?w(n):(p(c,"Profile Updated",4e3),u(null),d(""),b())}else{p(c,"Unknown field type: "+a,4e3,"error"),h(!1);return}h(!1)}catch(e){h(!1),_(a,{type:"manual",message:e!=null&&e.message&&e==null?void 0:e.message}),S(y,e!=null&&e.message&&e==null?void 0:e.message)}},w=a=>{if(a.validation){const t=Object.keys(a.validation);for(let e=0;e<t.length;e++){const o=t[e];_(o,{type:"manual",message:a.validation[o]})}}},q=a=>{try{if(a.size>2*1024*1024){p(c,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}R(a.type);const t=new FileReader;t.onload=()=>{B(t.result),k(!0)},t.readAsDataURL(a)}catch(t){p(c,t==null?void 0:t.message,3e3,"error"),console.log(t)}},K=async a=>{try{P(!0);const t=O==="image/png",e=new File([a],`cropped_profile.${t?"png":"jpg"}`,{type:t?"image/png":"image/jpeg"});let o=new FormData;o.append("file",e);let m=await x.uploadImage(o);f("photo",m==null?void 0:m.url)}catch(t){p(c,t==null?void 0:t.message,3e3,"error"),console.log(t)}finally{P(!1)}},H=()=>{f("photo",null),N({...r,photo:null})};return j.useEffect(()=>{c({type:"SETPATH",payload:{path:"profile"}}),b()},[]),s.jsxs("div",{className:"p-5",children:[I||U&&s.jsx(ee,{}),s.jsx("div",{className:"",children:s.jsxs("div",{className:"",children:[s.jsx("div",{className:"mb-6",children:s.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile details"})}),s.jsx(se,{isOpen:M,onClose:()=>k(!1),image:A,onCropComplete:K}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("img",{src:v||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),s.jsx("div",{children:s.jsxs("div",{className:"flex flex-col items-start justify-between gap-2",children:[s.jsx("p",{className:"font-medium text-gray-700",children:"Profile Picture"}),s.jsxs("div",{className:"flex gap-2",children:[s.jsx("button",{onClick:H,disabled:!v,className:"rounded-xl border border-red-600 px-3 py-1.5 text-red-600 disabled:opacity-50",children:"Remove"}),s.jsxs("label",{className:"cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5 text-gray-700",children:["Change Photo",s.jsx("input",{type:"file",accept:"image/*",onChange:a=>q(a.target.files[0]),className:"hidden"})]})]}),s.jsx("p",{className:"text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"})]})})]}),s.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"gender",label:"Gender",type:"select",options:["male","female","other"]},{key:"email",label:"Email",note:"Your email is not shared with other users."},{key:"phone",label:"Phone"},{key:"address",label:"Address"},{key:"city",label:"City"},{key:"state",label:"State"},{key:"zip_code",label:"Zip Code"},{key:"bio",label:"Bio",type:"textarea"}].map(a=>s.jsx("div",{children:L===a.key?s.jsxs("div",{children:[s.jsxs("label",{className:"flex justify-between",children:[s.jsx("span",{className:"font-medium text-gray-700",children:a.label}),s.jsx("button",{onClick:()=>u(null),className:"text-primaryBlue underline hover:text-primaryBlue",children:"Cancel"})]}),a.type==="select"?s.jsxs("select",{value:g,onChange:t=>d(t.target.value),className:"mt-1 w-full rounded-md border border-gray-300 p-2",children:[s.jsxs("option",{value:"",children:["Select ",a.label.toLowerCase()]}),a.options.map(t=>s.jsx("option",{value:t,children:t.charAt(0).toUpperCase()+t.slice(1)},t))]}):a.type==="textarea"?s.jsx("textarea",{value:g,onChange:t=>d(t.target.value),rows:4,className:"mt-1 w-full rounded-md border border-gray-300 p-2"}):s.jsx("input",{type:"text",value:g,onChange:t=>d(t.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),a.note&&s.jsx("p",{className:"mt-1 text-xs text-gray-500",children:a.note}),s.jsx("div",{className:"mt-2",children:s.jsx(te,{loading:T,onClick:()=>f(a.key,g),className:"rounded-xl bg-primaryBlue px-4 py-2 font-medium text-white hover:bg-primaryBlue",children:"Save"})})]}):s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("p",{className:"font-medium text-gray-500",children:a.label}),s.jsx("button",{onClick:()=>{u(a.key),d((r==null?void 0:r[a.key])||"")},className:"text-primaryBlue hover:text-indigo-800",children:"Edit"})]}),s.jsx("p",{className:"mt-1",children:(r==null?void 0:r[a.key])||"--"}),a.note&&s.jsx("p",{className:"mt-1 text-xs text-gray-500",children:a.note})]})},a.key))})]})]})})]})};export{He as default};
