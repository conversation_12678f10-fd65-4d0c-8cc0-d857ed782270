import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";const f=({currentPage:i,pageCount:l,pageSize:a,canPreviousPage:t,canNextPage:o,updatePageSize:n,previousPage:h,nextPage:b,gotoPage:d})=>{const c=()=>{const e=[];if(l<=7)for(let r=1;r<=l;r++)e.push(r);else{e.push(1),i>3&&e.push("...");for(let r=Math.max(2,i-1);r<=Math.min(l-1,i+1);r++)e.push(r);i<l-2&&e.push("..."),e.push(l)}return e};return s.jsxs("div",{className:"mt-5 flex items-center justify-between",children:[s.jsx("div",{className:"flex items-center gap-2",children:s.jsxs("span",{children:["Page"," ",s.jsxs("strong",{children:[i," of ",l]})," "]})}),s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("button",{onClick:()=>d(1),disabled:!t,className:"rounded-lg border px-3 py-1 hover:bg-gray-100",title:"First page",children:"«"}),s.jsx("button",{onClick:h,disabled:!t,className:"rounded-lg border px-3 py-1 hover:bg-gray-100",title:"Previous page",children:"‹"}),c().map((e,r)=>s.jsx("button",{onClick:()=>typeof e=="number"&&d(e),className:`rounded-lg border px-3 py-1 ${e===i?"bg-blue-500 text-white":"hover:bg-gray-100"} ${e==="..."?"cursor-default":""}`,disabled:e==="...",children:e},r)),s.jsx("button",{onClick:b,disabled:!o,className:"rounded-lg border px-3 py-1 hover:bg-gray-100",title:"Next page",children:"›"}),s.jsx("button",{onClick:()=>d(l),disabled:!o,className:"rounded-lg border px-3 py-1 hover:bg-gray-100",title:"Last page",children:"»"})]})," ",s.jsx("select",{className:"h-8 max-h-8 rounded-lg border border-gray-200 !py-0",value:a,onChange:e=>{n(Number(e.target.value))},children:[8,16].map(e=>s.jsxs("option",{value:e,children:[e," / page"]},e))})]})};export{f as default};
