import{j as e,ar as x}from"./@nivo/heatmap-ba1ecfff.js";import{r as h}from"./vendor-851db8c1.js";const f=({isOpen:t,onClose:r,title:a,children:d})=>t?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),e.jsxs("div",{className:"inline-block transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-5xl sm:p-6 sm:align-middle",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:a}),e.jsxs("button",{onClick:r,className:"rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none",children:[e.jsx("span",{className:"sr-only",children:"Close"}),e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})]})]}),d]})]})}):null,p=({stats:t})=>{const[r,a]=h.useState(!1),d=h.useMemo(()=>{if(!(t!=null&&t.revenueHeatMap))return[];const i={};return t.revenueHeatMap.forEach(n=>{const s=new Date(n.date),o=s.getMonth(),l=s.getDate();i[o]||(i[o]={}),i[o][l]=parseFloat(n.total_revenue)}),Array.from({length:12},(n,s)=>({id:(s+1).toString(),data:Array.from({length:31},(o,l)=>{var m;return{x:(l+1).toString(),y:((m=i[s])==null?void 0:m[l+1])||0}})}))},[t==null?void 0:t.revenueHeatMap]),c=({height:i})=>e.jsx(x,{data:d,margin:{top:60,right:90,bottom:60,left:90},valueFormat:">-.2s",axisTop:{tickSize:5,tickPadding:5,tickRotation:-90,legend:"Day of Month",legendOffset:46},axisRight:{tickSize:5,tickPadding:5,tickRotation:0,legend:"Month",legendPosition:"middle",legendOffset:70,format:n=>`Month ${n}`},axisLeft:{tickSize:5,tickPadding:5,tickRotation:0,legend:"Month",legendPosition:"middle",legendOffset:-72,format:n=>`Month ${n}`},colors:{type:"sequential",scheme:"greens"},emptyColor:"#555555",legends:[{anchor:"bottom",translateX:0,translateY:30,length:400,thickness:8,direction:"row",tickPosition:"after",tickSize:3,tickSpacing:4,tickOverlap:!1,title:"Revenue ($) →",titleAlign:"start",titleOffset:4}]});return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"rounded-2xl bg-white p-6 shadow-sm",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-800",children:"Revenue Heat Map"}),e.jsx("button",{onClick:()=>a(!0),className:"text-sm font-medium text-blue-600 hover:text-blue-700",children:"Full View"})]}),e.jsx("div",{className:"h-[400px]",children:e.jsx(c,{height:400})})]}),e.jsx(f,{isOpen:r,onClose:()=>a(!1),title:"Revenue Heat Map",children:e.jsx("div",{className:"h-[800px] w-full",children:e.jsx(c,{height:800})})})]})};export{p as H,f as M};
