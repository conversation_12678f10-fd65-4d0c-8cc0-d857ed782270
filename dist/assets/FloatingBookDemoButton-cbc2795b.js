import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as s,L as r}from"./vendor-851db8c1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";const m=()=>{const[i,e]=s.useState(!1);return s.useEffect(()=>{const o=()=>{window.scrollY>300?e(!0):e(!1)};return window.addEventListener("scroll",o),()=>window.removeEventListener("scroll",o)},[]),t.jsx("div",{className:`fixed bottom-6 right-4 z-50 transition-all duration-300 md:hidden ${i?"translate-y-0 opacity-100":"translate-y-10 opacity-0"}`,children:t.jsx(r,{to:"/contact",children:t.jsx("button",{className:"font-ubuntu flex animate-pulse items-center justify-center rounded-full bg-[#005954] px-6 py-3 text-white shadow-xl transition-all hover:bg-[#004a45]",children:"Book Demo"})})})};export{m as default};
