import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{j as E,f as R,r,k as C}from"./vendor-851db8c1.js";import{u as T,e as I,a9 as P,aY as M,aZ as B,a_ as Y,af as p,P as n,aR as L,M as A}from"./index-13fd629e.js";import{B as o}from"./BackButton-11ba52b2.js";import{h as c}from"./moment-a9aaa855.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const u=new A;function Ne(){const{reservationId:m}=E(),a=R(),[h,l]=r.useState(!0),[s,v]=r.useState(null),[j,f]=r.useState(null),{user_profile:N}=T(),[g]=C(),y=g.get("type"),[b,w]=r.useState(!1),[S,d]=r.useState(null);r.useEffect(()=>{k()},[m]),r.useEffect(()=>{if(s){const t=()=>{const _=c(s.reservation_updated_at),x=15-c().diff(_,"minutes");x<=0?(w(!0),d(0)):d(x)};t();const i=setInterval(t,6e4);return()=>clearInterval(i)}},[s]);const k=async()=>{try{l(!0);const t=await u.callRawAPI(`/v3/api/custom/courtmatchup/user/reservations/${y}/${m}`,{},"GET");if(t.error)throw new Error(t.message);v(t.reservation);const i=await u.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:t.reservation.price},"POST");f(i.client_secret)}catch(t){console.error("Error fetching reservation:",t)}finally{l(!1)}};return h?e.jsx(I,{}):s?s.booking_status===P.SUCCESS?e.jsxs("div",{className:"p-4",children:[e.jsx(o,{onBack:()=>a(-1)}),e.jsx("div",{className:"mx-auto mt-6 max-w-2xl",children:e.jsxs("div",{className:"rounded-xl bg-green-50 p-6 text-center",children:[e.jsx(M,{className:"mx-auto mb-4 text-4xl text-green-500"}),e.jsx("h2",{className:"mb-2 text-xl font-medium text-green-800",children:"Payment Already Completed"}),e.jsx("p",{className:"mb-4 text-green-600",children:"You have already paid for this reservation. No further action is needed."}),e.jsx("button",{onClick:()=>a("/user/my-reservations"),className:"rounded-lg bg-green-600 px-6 py-2 text-white transition-colors hover:bg-green-700",children:"View My Reservations"})]})})]}):b?e.jsxs("div",{className:"p-4",children:[e.jsx(o,{onBack:()=>a(-1)}),e.jsx("div",{className:"mx-auto mt-6 max-w-2xl",children:e.jsxs("div",{className:"rounded-xl bg-red-50 p-6 text-center",children:[e.jsx(B,{className:"mx-auto mb-4 text-4xl text-red-500"}),e.jsx("h2",{className:"mb-2 text-xl font-medium text-red-800",children:"Reservation Expired"}),e.jsx("p",{className:"mb-4 text-red-600",children:"The 15-minute payment window has expired. Please make a new reservation."}),e.jsx("button",{onClick:()=>a("/user/reserve-court"),className:"rounded-lg bg-red-600 px-6 py-2 text-white transition-colors hover:bg-red-700",children:"Make New Reservation"})]})})]}):e.jsxs("div",{className:"p-4",children:[e.jsx(o,{onBack:()=>a(-1)}),e.jsxs("div",{className:"mx-auto max-w-2xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Y,{className:"text-2xl"}),e.jsxs("span",{className:"!text-sm",children:["Time remaining: ",S," minutes to complete the payment, otherwise the reservation will be canceled."]})]})}),e.jsxs("div",{className:"mt-6 space-y-6",children:[e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-lg font-medium",children:"Reservation Summary"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Sport"}),e.jsxs("span",{children:[s.sport_name," • ",s.type," •"," ",s.sub_type]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Date"}),e.jsx("span",{children:c(s.booking_date).format("MMM D, YYYY")})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Time"}),e.jsxs("span",{children:[p(s.start_time)," -"," ",p(s.end_time)]})]})]})]}),e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-lg font-medium",children:"Payment Details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Club fee"}),e.jsx("span",{children:n(s.club_fee)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:n(s.service_fee)})]}),e.jsxs("div",{className:"flex justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:n(s.price)})]}),e.jsx(L,{user:N,bookingId:s.booking_id,reservationId:s.reservation_id,clientSecret:j,paymentIntent:s.payment_intent,navigateRoute:`/user/payment-success/${s.reservation_id}?type=court`})]})]})]})]})]}):e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"rounded-lg bg-red-50 p-4 text-red-800",children:"Reservation not found"})})}export{Ne as default};
