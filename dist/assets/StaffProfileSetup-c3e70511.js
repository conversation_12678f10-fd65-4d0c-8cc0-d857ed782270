import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{M as E,d as k,A,G as M,u as B,E as I,e as P,t as N,b as S}from"./index-13fd629e.js";import{b as V,r as a,f as C}from"./vendor-851db8c1.js";import{A as T}from"./AuthLayout-ceb43654.js";import{u as G}from"./react-hook-form-687afde5.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const _=new E;function $({onNext:o,stripeConnectionData:s,isSubmitting:j,setStripeConnectionData:x}){const[d]=V.useState(j||!1),[l,u]=a.useState(!1),[w,n]=a.useState(!1),[h,m]=a.useState(!1),f=localStorage.getItem("role"),p=async()=>{try{const r=await _.callRawAPI(`/v3/api/custom/courtmatchup/${f}/stripe/account/verify`,{},"POST");return x&&x(r),r}catch(r){return console.error("Error checking Stripe connection:",r),!1}},y=async()=>{u(!0);try{const r=await _.callRawAPI(`/v3/api/custom/courtmatchup/${f}/stripe/onboarding`,{},"POST");r&&r.url&&window.open(r.url,"_blank")}catch(r){console.error("Error connecting to Stripe:",r)}u(!1)};a.useEffect(()=>{if(l===!1){const r=setTimeout(()=>{p()},2e3);return()=>clearTimeout(r)}},[l]);const b=async()=>{n(!0),await o(),n(!1)},i=async()=>{m(!0);try{await o({skip_payment:!0})}finally{m(!1)}};return e.jsx("div",{className:"flex flex-col bg-white pb-7",children:e.jsxs("section",{className:"flex w-[432px] max-w-full flex-col justify-center",children:[e.jsx("div",{className:"flex w-full max-w-[432px] flex-col self-center max-md:max-w-full",children:e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3",children:[e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_26852)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_26852)"}),e.jsxs("g",{filter:"url(#filter0_d_397_26852)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M47.3125 34.4999H47V42.7054C47.6877 42.8975 48.2531 43.4195 48.4868 44.1207L48.9035 45.3707C49.3756 46.7872 48.3213 48.2499 46.8282 48.2499H27.1718C25.6787 48.2499 24.6244 46.7872 25.0965 45.3707L25.5132 44.1207C25.7469 43.4195 26.3123 42.8975 27 42.7054V34.4999H26.6875C25.4794 34.4999 24.5 33.5206 24.5 32.3124V31.7352C24.5 30.9099 24.9645 30.1549 25.7011 29.7828L36.0136 24.5729C36.6339 24.2596 37.3661 24.2596 37.9864 24.5729L48.2989 29.7828C49.0355 30.1549 49.5 30.9099 49.5 31.7352V32.3124C49.5 33.5206 48.5206 34.4999 47.3125 34.4999ZM42 34.4999H45.125V42.6249H42V34.4999ZM32 42.6249H28.875V34.4999H32V42.6249ZM33.875 42.6249V34.4999H40.125V42.6249H33.875Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_26852",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_26852"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_26852",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_26852",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_26852",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]}),e.jsx("p",{className:"w-full text-center text-2xl font-medium leading-none text-gray-950 max-md:max-w-full",children:"Connect stripe"})]})}),e.jsxs("div",{className:"mt-10 flex w-full flex-col gap-4 self-center max-md:max-w-full",children:[((s==null?void 0:s.complete)||(s==null?void 0:s.details_submitted))&&e.jsxs("div",{className:"flex flex-col gap-4 rounded-lg border border-green-200 bg-green-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-lg font-medium text-gray-900",children:s!=null&&s.complete?"Stripe account connected":"Stripe account details submitted"})]}),e.jsxs("div",{className:"mt-2 grid grid-cols-1 gap-3",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-xs text-gray-500",children:"Account ID"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:s.account_id})]}),e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${s.complete?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Complete"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${s.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${s.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]})]}),e.jsx("div",{className:"mt-2 text-sm text-gray-600",children:s!=null&&s.complete?"You can now receive payments from your club members.":"Your Stripe account details have been submitted and are pending approval. You can continue with the setup process."}),e.jsx(k,{onClick:b,className:"mt-4 w-full rounded-xl bg-primaryGreen px-4 py-3 text-sm font-medium text-white",loading:d||w,children:d?"Processing...":"Complete Setup"})]}),!(s!=null&&s.complete||s!=null&&s.details_submitted)&&e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"rounded-lg border border-yellow-200 bg-yellow-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-yellow-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"No Stripe account connected"})]}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Connect your Stripe account to pay your club staffs and coaches. This is required for processing payments on the platform."}),s&&e.jsxs("div",{className:"mt-3 grid grid-cols-1 gap-2",children:[e.jsx("div",{className:"text-xs text-gray-500",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${s.complete?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Complete"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${s.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${s.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]})]}),e.jsx("button",{onClick:y,className:"w-full rounded-xl bg-primaryBlue px-4 py-3 text-sm font-medium text-white",disabled:l,children:l?"Connecting...":"Connect Stripe Account"}),e.jsxs("div",{className:"mt-4 text-center",children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"- OR -"}),e.jsx("button",{onClick:i,className:"w-full rounded-xl border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50",disabled:l||h,children:h?"Processing...":"Will not be paid through Court Matchup"})]})]})]})]})})}const g=new E;function ye(){const o=C(),{dispatch:s}=a.useContext(A),[j,x]=a.useState(!1),{dispatch:d}=a.useContext(M),[l,u]=a.useState(null),[w,n]=a.useState(!1),[h,m]=a.useState(!1),[f,p]=a.useState(null),y=localStorage.getItem("role"),{triggerRefetch:b}=B(),{setValue:i,getValues:r}=G({defaultValues:{hourly_rate:"",bio:"",photo:null,account_details:[]}}),v=async()=>{m(!0);try{const t=await g.callRawAPI("/v3/api/custom/courtmatchup/staff/profile",{},"GET");if((t==null?void 0:t.completed)==1){o("/staff/dashboard");return}const c=await g.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${t.club_id}`,{},"GET");return u(c.model),i("hourly_rate",(t==null?void 0:t.hourly_rate)||""),i("bio",(t==null?void 0:t.bio)||""),i("photo",(t==null?void 0:t.photo)||""),i("account_details",(t==null?void 0:t.account_details)||[]),t}catch(t){N(s,t.code),S(d,t.message,3e3,"error")}finally{m(!1)}},L=async t=>{r(),n(!0);try{const c={};t&&t.skip_payment?c.not_paid_through_platform=1:c.not_paid_through_platform=0,c.completed=1,await g.callRawAPI("/v3/api/custom/courtmatchup/staff/profile-edit",c,"POST"),await v(),b(),x(!0)}catch(c){console.error(c),S(d,c.message,3e3,"error"),N(s,c.code)}finally{n(!1)}};a.useEffect(()=>{(async()=>await v())()},[]),a.useEffect(()=>{I({title:l==null?void 0:l.name,path:"/staff/profile-setup",clubName:l==null?void 0:l.name,favicon:l==null?void 0:l.club_logo,description:"Staff Profile Setup"})},[l]);const R=async()=>{try{const t=await g.callRawAPI(`/v3/api/custom/courtmatchup/${y}/stripe/account/verify`,{},"POST");p(t)}catch(t){return console.error("Error checking Stripe connection:",t),!1}};return a.useEffect(()=>{R()},[]),e.jsxs(T,{children:[h&&e.jsx(P,{}),e.jsxs("div",{className:"flex flex-col bg-white pb-7",children:[e.jsx("div",{className:"flex w-full flex-col px-20 pt-4 max-md:max-w-full max-md:px-5",children:e.jsx("div",{className:"mb-10"})}),e.jsx("div",{className:"flex flex-1 items-center justify-center",children:e.jsx("div",{className:"",children:e.jsx($,{onNext:L,isSubmitting:w,stripeConnectionData:f,setStripeConnectionData:p})})}),j&&e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsx("div",{className:"w-full max-w-xl rounded-2xl bg-white ",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"flex items-start gap-4 p-5",children:[e.jsx("div",{className:"",children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#EFFAF6"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z",fill:"#38C793"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:"Sign up complete!"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Congratulations! You have successfully signed up for Court Matchup. You can now access your staff portal"})]})]}),e.jsx("div",{className:"flex w-full justify-end border-t border-gray-200 p-5",children:e.jsx(k,{onClick:()=>o("/staff/dashboard"),className:"w-fit rounded-xl bg-primaryGreen px-5 py-3 text-white",children:"Continue to Staff portal!"})})]})})})]})]})}export{ye as default};
