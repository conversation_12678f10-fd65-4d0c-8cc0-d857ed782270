import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{r,b as c,f as Re}from"./vendor-851db8c1.js";import{M as Ne,T as _e,G as $e,ab as ye,aF as Me,ac as Ae,c as Pe,P as Se,d as ke,ad as Fe,aG as Ee,Y as Oe,X as Be,Z as Ie,b as V,A as He,a5 as Ze,a6 as ze,t as Ge,i as Ve,a7 as qe,a8 as Te,a9 as ve,aa as Je}from"./index-13fd629e.js";import{c as Ke,a as ne}from"./yup-54691517.js";import{u as Ue}from"./react-hook-form-687afde5.js";import{o as Ye}from"./yup-2824f222.js";import{P as Qe}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import Xe from"./Skeleton-1e8bf077.js";import{C as We}from"./CheckinModal-eec9c2ce.js";import{L as es}from"./LoadingOverlay-87926629.js";import{P as ss,R as ts,T as as,F as ns}from"./ReservationStatus-5ced670f.js";import{D as rs}from"./DataTable-8a547681.js";import"./numeral-ea653b2a.js";import{H as is}from"./HistoryComponent-0ba6cfc0.js";import{B as ls}from"./BottomDrawer-3018f655.js";import{c as os}from"./index.esm-3f8dc7b8.js";import"./SelectionOption-658322e6.js";import"./SelectionOptionsCard-30c39f7f.js";import{B as ds}from"./BackButton-11ba52b2.js";import{T as ms}from"./TimeSlots-933ca6d9.js";import{C as cs}from"./Calendar-35bce269.js";import{S as us}from"./SportTypeSelection-1b7f983d.js";import{L as De}from"./ReservationSummary-5582c1fb.js";import{f as xs}from"./date-fns-cca0f4f7.js";new Ne;new _e;let re=new Ne;new _e;const gs=({isOpen:j,onClose:d,players:q,sports:L,club:i})=>{var _,w,T,z,G;r.useState("selection");const[$,ie]=r.useState(null);r.useState(null),r.useState(null);const[v,h]=r.useState(null),[P,J]=r.useState(new Date);r.useState(null);const[b,le]=r.useState(0),[K,C]=r.useState(0),[B,oe]=r.useState(null),[I,de]=r.useState(null),{state:we,dispatch:E}=r.useContext($e),[U,R]=r.useState(!1),[H,me]=r.useState([]),[S,M]=r.useState("main"),[N,A]=r.useState(""),[o,Y]=r.useState([]),[y,ce]=r.useState([]),[ue,xe]=r.useState([]),[a,ge]=r.useState(null),[Q,X]=r.useState(!1),[Z,be]=r.useState(!0),{start_time:W,end_time:ee,duration:fe}=ye(y),p=localStorage.getItem("role");r.useEffect(()=>{},[S]);const pe=async()=>{X(!0),xs(new Date(v),"yyyy-MM-dd");try{re.setTable("coach");const e=await re.callRestAPI({filter:[`courtmatchup_coach.club_id,eq,${i==null?void 0:i.id}`],join:["user|user_id"]},"GETALL");if(!e.error){if(e.list.length===0){V(E,"No coaches found for the selected time slot",4e3,"error");return}xe(e.list),M("coaches")}}catch(e){console.error("ERROR",e),V(E,e.message,5e3)}finally{X(!1)}},he=()=>{const e=new Date(P);e.setMonth(e.getMonth()-1),J(e)},se=()=>{const e=new Date(P);e.setMonth(e.getMonth()+1),J(e)},je=e=>{h(e)},te=async()=>{R(!0);try{const e={sport_id:$,type:B,sub_type:I,date:v,player_ids:o.map(g=>g.id),start_time:W,end_time:ee,price:K,duration:fe,coach_id:a==null?void 0:a.id,reservation_type:Ee.lesson,club_id:i==null?void 0:i.id,buddy_request:0,payment_intent:null,buddy_details:null,payment_details:null};console.log("form data",e);const n=await re.callRawAPI(`/v3/api/custom/courtmatchup/${p}/reservations`,e,"POST");await Oe(re,{user_id:localStorage.getItem("user"),activity_type:Be.lesson,action_type:Ie.CREATE,data:e,club_id:i==null?void 0:i.id,description:"Created a lesson reservation"}),n.error||(V(E,"Lesson created successfully",3e3,"success"),d())}catch(e){console.error(e),V(E,e.message||"Error creating lesson",3e3,"error")}finally{R(!1)}},t=e=>{ce([{from:e.from,until:e.until}])},l=e=>{Y(n=>n.some(f=>f.id===e.id)?n.filter(f=>f.id!==e.id):[...n,e])},m=e=>o.some(n=>n.id===e),x=q.filter(e=>`${e.first_name} ${e.last_name}`.toLowerCase().includes(N.toLowerCase())),u=()=>{pe()},F=()=>{if(!a){V(E,"Please select a coach",3e3,"error");return}M("players")},k=ue.filter(e=>{var g,f;return`${(e==null?void 0:e.first_name)||((g=e==null?void 0:e.user)==null?void 0:g.first_name)} ${(e==null?void 0:e.last_name)||((f=e==null?void 0:e.user)==null?void 0:f.last_name)}`.toLowerCase().includes(N.toLowerCase())});return r.useEffect(()=>{if(a&&y.length>0){const e=y.reduce((g,f)=>{const D=new Date(`2000/01/01 ${f.from}`),ae=(new Date(`2000/01/01 ${f.until}`)-D)/(1e3*60*60);return g+ae},0),n=Me({hourlyRate:a==null?void 0:a.hourly_rate,hours:e,playerCount:o.length||1,feeSettings:i==null?void 0:i.fee_settings});le(n.total/(o.length||1)),C(n.total)}else b&&(o!=null&&o.length)?C(b*(o==null?void 0:o.length)):C(b)},[b,o,a,y,i==null?void 0:i.fee_settings]),s.jsxs(ls,{isOpen:j,onClose:d,title:S==="main"?"Add New Lesson":S==="coaches"?"Select Coach":"Select Players",showActions:!1,saveLabel:"Create Lesson",onSave:te,isSubmitting:U,children:[s.jsx(ds,{onBack:()=>{S=="main"?d():M("main")}}),S==="main"?s.jsx("div",{className:" p-4",children:s.jsx("div",{className:"space-y-6",children:s.jsx("div",{className:"mx-auto max-w-7xl p-4",children:s.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[s.jsx(us,{sports:L,courts:(i==null?void 0:i.courts)||[],filterMode:"lesson",onSelectionChange:({sport:e,type:n,subType:g})=>{ie(e),oe(n),de(g)}}),s.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:s.jsx(cs,{currentMonth:P,selectedDate:v,onDateSelect:je,onPreviousMonth:he,onNextMonth:se,allowPastDates:!1,minDate:new Date,maxDate:new Date(new Date().setMonth(new Date().getMonth()+3)),daysOff:i!=null&&i.days_off?JSON.parse(i.days_off):[]})}),v&&s.jsx(ms,{onTimeClick:t,selectedDate:v,isLoading:Q,timeRange:H,timeSlots:Ae(),onNext:u,nextButtonText:"Next: Select Coach",startHour:0,endHour:24,interval:30,className:"h-fit rounded-lg bg-white p-4 shadow-5",multipleSlots:!1,individualSelection:!0,isTimeSlotAvailable:e=>!0,clubTimes:i!=null&&i.times?JSON.parse(i.times):[]})]})})})}):S==="coaches"?s.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[s.jsx(De,{selectedSport:$,sports:L,selectedType:B,selectedSubType:I,selectedDate:v,selectedTimes:y,selectedCoach:a,timeRange:ye(y)}),s.jsxs("div",{className:"col-span-2 h-fit rounded-lg bg-white shadow-5",children:[s.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:s.jsx("h2",{className:"text-base font-medium",children:"Select a Coach"})}),s.jsxs("div",{className:"p-4",children:[s.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[s.jsx("div",{className:"flex-1",children:s.jsxs("div",{className:"flex items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none",children:[s.jsx(Pe,{className:"text-gray-500"}),s.jsx("input",{type:"text",placeholder:"Search coaches",value:N,onChange:e=>A(e.target.value),className:"w-full border-none bg-transparent py-2 focus:outline-none focus:ring-0"})]})}),s.jsx("button",{onClick:()=>be(!Z),className:"ml-2 flex items-center gap-1 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50",children:s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("span",{children:"A-Z"}),s.jsx(os,{className:`text-xs transition-transform ${Z?"":"rotate-180"}`})]})})]}),s.jsx("div",{className:"max-h-[calc(90vh-200px)] space-y-4 overflow-y-auto",children:k.length>0?k.sort((e,n)=>{var D,O,ae,Ce;const g=`${(e==null?void 0:e.first_name)||((D=e==null?void 0:e.user)==null?void 0:D.first_name)} ${(e==null?void 0:e.last_name)||((O=e==null?void 0:e.user)==null?void 0:O.last_name)}`.toLowerCase(),f=`${(n==null?void 0:n.first_name)||((ae=n==null?void 0:n.user)==null?void 0:ae.first_name)} ${(n==null?void 0:n.last_name)||((Ce=n==null?void 0:n.user)==null?void 0:Ce.last_name)}`.toLowerCase();return Z?g.localeCompare(f):f.localeCompare(g)}).map(e=>{var n,g,f,D,O;return s.jsxs("div",{className:`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${(a==null?void 0:a.id)===e.id?"border-primaryBlue bg-blue-50":"border-gray-100 hover:bg-gray-50"}`,onClick:()=>ge(e),children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("img",{src:(e==null?void 0:e.photo)||((n=e==null?void 0:e.user)==null?void 0:n.photo)||"/default-avatar.png",alt:`${(e==null?void 0:e.first_name)||((g=e==null?void 0:e.user)==null?void 0:g.first_name)} ${(e==null?void 0:e.last_name)||((f=e==null?void 0:e.user)==null?void 0:f.last_name)}`,className:"h-10 w-10 rounded-full object-cover"}),s.jsx("div",{className:"flex flex-col",children:s.jsxs("span",{className:"font-medium capitalize",children:[(e==null?void 0:e.first_name)||((D=e==null?void 0:e.user)==null?void 0:D.first_name)," ",(e==null?void 0:e.last_name)||((O=e==null?void 0:e.user)==null?void 0:O.last_name)]})})]}),s.jsxs("span",{className:"text-gray-600",children:[Se(e.hourly_rate),"/h"]})]},e.id)}):s.jsx("p",{className:"text-center text-sm text-gray-500",children:"No coaches found"})}),s.jsx("div",{className:"mt-6",children:s.jsx(ke,{loading:Q,onClick:F,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",disabled:!a,children:"Next: Select Players"})})]})]})]}):s.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[s.jsx(De,{selectedSport:$,sports:L,selectedType:B,selectedSubType:I,selectedDate:v,selectedTimes:y,selectedCoach:a,timeRange:ye(y)}),s.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[s.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:s.jsx("h2",{className:"text-base font-medium",children:"Reservation for:"})}),s.jsxs("div",{className:"p-4",children:[s.jsx("div",{className:"mb-4",children:s.jsxs("div",{className:"flex items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none",children:[s.jsx("span",{className:"w-5",children:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),s.jsx("input",{type:"text",placeholder:"search by name",value:N,onChange:e=>A(e.target.value),className:"w-full border-none bg-transparent focus:outline-none focus:ring-0"})]})}),o.length>0&&s.jsx("div",{className:"mb-4 flex flex-wrap gap-2",children:o.map(e=>s.jsxs("div",{className:"flex items-center gap-2 rounded-lg bg-gray-100 px-3 py-1",children:[s.jsx("div",{className:"h-6 w-6 overflow-hidden rounded-full bg-gray-200",children:s.jsx("img",{src:(e==null?void 0:e.photo)||"/default-avatar.png",alt:`${e.first_name} ${e.last_name}`,className:"h-full w-full object-cover"})}),s.jsx("span",{className:"text-sm",children:`${e.first_name} ${e.last_name}`}),s.jsx("button",{onClick:n=>{n.stopPropagation(),l(e)},className:"text-gray-400 hover:text-gray-600",children:s.jsx(Fe,{className:"h-4 w-4"})})]},e.id))}),s.jsx("div",{className:"max-h-64 space-y-2 overflow-y-auto rounded-xl bg-gray-50",children:x.length>0?x.map(e=>s.jsxs("div",{onClick:()=>l(e),className:"flex cursor-pointer items-center space-x-3 rounded-lg p-2 hover:bg-gray-50",children:[s.jsx("input",{type:"checkbox",checked:m(e.id),onChange:()=>{},className:"h-4 w-4 rounded border-gray-300 text-blue-600"}),s.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full bg-gray-200",children:s.jsx("img",{src:(e==null?void 0:e.photo)||"/default-avatar.png",alt:`${e.first_name} ${e.last_name}`,className:"h-full w-full object-cover"})}),s.jsx("span",{children:`${e.first_name} ${e.last_name}`})]},e.id)):s.jsx("p",{className:"text-center text-sm text-gray-500",children:"No players found"})})]})]}),s.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[s.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:s.jsx("h2",{className:"text-base font-medium",children:"Reserving details"})}),s.jsx("div",{className:"p-4",children:s.jsxs("div",{className:"space-y-6",children:[a&&s.jsxs("div",{children:[s.jsx("h3",{className:"text-sm text-gray-500",children:"COACH"}),s.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[s.jsx("div",{className:"h-6 w-6 overflow-hidden rounded-full bg-gray-200",children:s.jsx("img",{src:(a==null?void 0:a.photo)||((_=a==null?void 0:a.user)==null?void 0:_.photo)||"/default-avatar.png",alt:`${(a==null?void 0:a.first_name)||((w=a==null?void 0:a.user)==null?void 0:w.first_name)} ${(a==null?void 0:a.last_name)||((T=a==null?void 0:a.user)==null?void 0:T.last_name)}`,className:"h-full w-full object-cover"})}),s.jsxs("div",{className:"text-sm",children:[(a==null?void 0:a.first_name)||((z=a==null?void 0:a.user)==null?void 0:z.first_name)," ",(a==null?void 0:a.last_name)||((G=a==null?void 0:a.user)==null?void 0:G.last_name)]})]})]}),s.jsxs("div",{children:[s.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",o.length,")"]}),s.jsx("div",{className:"mt-1",children:o.map(e=>s.jsxs("div",{className:"text-sm",children:[e.first_name," ",e.last_name]},e.id))})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[s.jsx("span",{children:"Fee"}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("span",{className:"text-gray-500",children:"$"}),s.jsx("input",{type:"text",className:"w-20 rounded-lg border border-gray-200 bg-gray-50 px-2 py-1 text-right",value:b,onChange:e=>{le(e.target.value)}})]})]})]}),s.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[s.jsx("span",{children:"Total"}),s.jsx("span",{className:"font-medium",children:Se(K)})]}),s.jsx("div",{className:"rounded-lg bg-blue-700 p-3 text-sm text-white",children:s.jsxs("div",{className:"flex items-start gap-2",children:[s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:s.jsx("path",{d:"M10 2C5.58172 2 2 5.58172 2 10C2 14.4183 5.58172 18 10 18C14.4183 18 18 14.4183 18 10C18 5.58172 14.4183 2 10 2ZM0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10ZM9 5C9 4.44772 9.44772 4 10 4C10.5523 4 11 4.44772 11 5V11C11 11.5523 10.5523 12 10 12C9.44772 12 9 11.5523 9 11V5ZM9 15C9 14.4477 9.44772 14 10 14C10.5523 14 11 14.4477 11 15C11 15.5523 10.5523 16 10 16C9.44772 16 9 15.5523 9 15Z",fill:"currentColor"})}),s.jsx("span",{children:o.length>0?`${o.map(e=>`${e.first_name} ${e.last_name}`).join(", ")} will be notified about this reservation.`:"selected players will be notified about this reservation."})]})}),s.jsx(ke,{loading:U,onClick:te,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:"Create Lesson"}),s.jsx("p",{className:"text-center text-sm text-gray-500",children:"(You will not be charged yet)"})]})})]})]})]})};let Le=new Ne,fs=new _e;const ps=[{header:"Date",accessor:"date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"By",accessor:"user",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Sport",accessor:"sport",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{1:"Paid",0:"Reserved",2:"Failed"}}],hs=({sports:j,club:d,courts:q})=>{const{dispatch:L,state:i}=c.useContext($e),{dispatch:$}=c.useContext(He),[ie,v]=c.useState([]),[h,P]=c.useState(10),[J,b]=c.useState(0),[le,K]=c.useState(0),[C,B]=c.useState(1),[oe,I]=c.useState(!1),[de,we]=c.useState(!1),[E,U]=c.useState(!1);c.useState([]),c.useState([]);const[R,H]=c.useState(!0);Re();const me=c.useRef(null),[S,M]=c.useState(!1),[N,A]=c.useState(null),[o,Y]=r.useState(!1),[y,ce]=r.useState(!1),[ue,xe]=r.useState(!1);r.useState([]);const[a,ge]=r.useState([]),Q=Ke({id:ne(),email:ne(),role:ne(),status:ne()}),{register:X,handleSubmit:Z,formState:{errors:be}}=Ue({resolver:Ye(Q)});function W(){p(C-1,h)}function ee(){p(C+1,h)}const fe=async()=>{try{Le.setTable("user");const t=await Le.callRestAPI({filter:[`club_id,eq,${d==null?void 0:d.id}`,"role,cs,user"]},"GETALL");ge(t.list||[])}catch(t){console.error("Error fetching players:",t),showToast(L,"Error fetching players",3e3,"error")}};async function p(t,l,m={},x=[]){H(!0);try{const u=await fs.getPaginate("reservation",{page:t,limit:l,filter:[...x,`courtmatchup_reservation.club_id,cs,${d==null?void 0:d.id}`,`courtmatchup_booking.reservation_type,cs,${Ee.lesson}`],join:["clubs|club_id","booking|booking_id","user|user_id"],size:h}),F=u.list;if(u){const k=F.map(_=>{const w=j.find(T=>T.id===_.sport_id);return{..._,sport:w?w.name:"--"}});H(!1),v(k),P(u.limit),b(u.num_pages),B(u.page),K(u.total),I(u.page>1),we(u.page+1<=u.num_pages)}}catch(u){H(!1),console.log("ERROR",u),Ge($,u.message)}}const pe=t=>{t.search?p(1,h,{},[`first_name,cs,${t.search}`,`last_name,cs,${t.search}`]):p(1,h)},he=async t=>{const l=t.target.value;l===""?await p(1,h):await p(1,h,{},[`sport_id,eq,${parseInt(l)}`])};c.useEffect(()=>{L({type:"SETPATH",payload:{path:"lessons"}}),d!=null&&d.id&&(p(1,h,{}),fe())},[d==null?void 0:d.id]);const se=t=>{me.current&&!me.current.contains(t.target)&&U(!1)};c.useEffect(()=>(document.addEventListener("mousedown",se),()=>{document.removeEventListener("mousedown",se)}),[]);const je=t=>{var m,x,u,F,k,_,w,T,z,G,e,n;const l={...t,id:(m=t.booking)==null?void 0:m.id,date:(x=t.booking)==null?void 0:x.date,startTime:(u=t.booking)==null?void 0:u.start_time,endTime:(F=t.booking)==null?void 0:F.end_time,sport_id:(k=t.booking)==null?void 0:k.sport_id,type:(_=t.booking)==null?void 0:_.type,sub_type:(w=t.booking)==null?void 0:w.subtype,reservation_type:(T=t.booking)==null?void 0:T.reservation_type,price:(z=t.booking)==null?void 0:z.price,status:(G=t.booking)==null?void 0:G.status,player_ids:(e=t.booking)==null?void 0:e.player_ids,coach_ids:(n=t.booking)==null?void 0:n.coach_ids};A(l),M(!0)},te={type:t=>{var l;return s.jsx("span",{className:"capitalize",children:((l=Ve.find(m=>{var x;return m.value==((x=t==null?void 0:t.booking)==null?void 0:x.reservation_type)}))==null?void 0:l.label)||"--"})},sport:t=>{var l;return s.jsx("span",{className:"capitalize",children:((l=j.find(m=>{var x;return m.id===((x=t==null?void 0:t.booking)==null?void 0:x.sport_id)}))==null?void 0:l.name)||"--"})},date:t=>{var l,m,x;return s.jsxs(s.Fragment,{children:[qe((l=t==null?void 0:t.booking)==null?void 0:l.date)," "," | "," ",Te((m=t==null?void 0:t.booking)==null?void 0:m.start_time)," "," - "," ",Te((x=t==null?void 0:t.booking)==null?void 0:x.end_time)]})},players:t=>{var l,m;return s.jsx(s.Fragment,{children:(l=t==null?void 0:t.booking)!=null&&l.player_ids?`${JSON.parse((m=t==null?void 0:t.booking)==null?void 0:m.player_ids).length} players`:"0 players"})},bill:t=>{var l;return s.jsx(s.Fragment,{children:Se((l=t==null?void 0:t.booking)==null?void 0:l.price)})},user:t=>{var l,m;return s.jsx(s.Fragment,{children:`${((l=t==null?void 0:t.user)==null?void 0:l.first_name)||""} ${((m=t==null?void 0:t.user)==null?void 0:m.last_name)||""}`})},status:t=>s.jsxs(s.Fragment,{children:[t.booking.status==ve.SUCCESS&&s.jsx(ss,{}),t.booking.status==ve.PENDING&&s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(ts,{}),s.jsx(as,{timeLeft:Je(t==null?void 0:t.reservation_updated_at)})]}),t.booking.status==ve.FAIL&&s.jsx(ns,{})]})};return s.jsxs("div",{className:"h-screen px-2 md:px-8",children:[R&&s.jsx(es,{}),s.jsx("div",{className:"flex flex-col gap-4 py-3",children:s.jsxs("div",{className:"flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between",children:[s.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center",children:[s.jsxs("form",{className:"relative flex flex-1 items-center",onSubmit:Z(pe),children:[s.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:s.jsx(Pe,{className:"text-gray-500"})}),s.jsx("input",{type:"text",className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search users",...X("search")})]}),s.jsxs("div",{className:"flex flex-col gap-2 xs:flex-row xs:gap-4",children:[s.jsx("input",{type:"date",className:"w-full rounded-lg border border-gray-200 text-sm text-gray-500 xs:w-auto"}),s.jsx("input",{type:"time",defaultValue:"00:00",className:"w-full rounded-lg border border-gray-200 text-sm text-gray-500 xs:w-auto"})]})]}),s.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:flex-wrap sm:items-center",children:[s.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700 sm:w-auto",defaultValue:"Sport: All",onChange:he,children:[s.jsx("option",{value:"",children:"Sport: All"}),j==null?void 0:j.map(t=>s.jsx("option",{value:t.id,children:t.name},t.id))]}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsxs("button",{onClick:()=>Y(!0),className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[s.jsx("span",{children:"+"}),"Add new"]}),s.jsx(is,{title:"Lesson History",emptyMessage:"No lesson history found"})]})]})]})}),R?s.jsx(Xe,{}):s.jsx("div",{className:"overflow-x-auto",children:s.jsx(rs,{columns:ps,data:ie,loading:R,renderCustomCell:te,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer",cellClassName:"whitespace-nowrap px-6 py-4",headerClassName:"px-6 py-4 text-left text-sm font-medium text-gray-500",emptyMessage:"No data available",onClick:t=>je(t)})}),s.jsx(Qe,{currentPage:C,pageCount:J,pageSize:h,canPreviousPage:oe,canNextPage:de,updatePageSize:t=>{P(t),p(1,t)},previousPage:W,nextPage:ee,gotoPage:t=>p(t,h)}),s.jsx(Ze,{isOpen:S,onClose:()=>M(!1),event:N,users:a,sports:j,club:d,fetchData:p,courts:q}),s.jsx(gs,{isOpen:o,club:d,onClose:()=>Y(!1),sports:j,players:a}),y&&s.jsx(We,{courts:q,onClose:()=>ce(!1),reservation:N,getData:p,sports:j,setReservation:A}),ue&&s.jsx(ze,{reservation:N,onClose:()=>xe(!1),getData:p,setReservation:A})]})},zs=hs;export{zs as L};
