import{r as i}from"./vendor-851db8c1.js";function S(){return S=Object.assign||function(s){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(s[a]=e[a])}return s},S.apply(this,arguments)}var L=function(s){var t=i.useRef(s);return t.current=s,t},T=function(s,t){var e;return function(){for(var a=this,l=arguments.length,g=new Array(l),c=0;c<l;c++)g[c]=arguments[c];e!==null&&(clearTimeout(e),e=null),e=setTimeout(function(){return s.apply(a,g)},t)}},D="💡 use-places-autocomplete: Google Maps Places API library must be loaded. See: https://github.com/wellyshen/use-places-autocomplete#load-the-library",Q=function(t){var e=t===void 0?{}:t,a=e.requestOptions,l=e.debounce,g=l===void 0?200:l,c=e.cache,p=c===void 0?24*60*60:c,y=e.cacheKey,d=y===void 0?"upa":y,V=e.googleMaps,v=e.callbackName,_=e.defaultValue,x=_===void 0?"":_,P=e.initOnMount,R=P===void 0?!0:P,$=i.useState(!1),G=$[0],E=$[1],A=i.useState(x),I=A[0],N=A[1],M=i.useState({loading:!1,status:"",data:[]}),j=M[0],m=M[1],w=i.useRef(),C=L(a),h=L(V),b=i.useCallback(function(){var r;if(!w.current){var n=window,o=n.google,u=h.current,f=(u==null?void 0:u.places)||(o==null||(r=o.maps)==null?void 0:r.places);if(!f){console.error(D);return}w.current=new f.AutocompleteService,E(!0)}},[h]),O=i.useCallback(function(){m({loading:!1,status:"",data:[]})},[]),q=i.useCallback(function(r){r===void 0&&(r=d);try{sessionStorage.removeItem(r)}catch{}},[d]),K=i.useCallback(T(function(r){var n;if(!r){O();return}m(function(u){return S({},u,{loading:!0})});var o={};try{o=JSON.parse(sessionStorage.getItem(d)||"{}")}catch{}if(p&&(o=Object.keys(o).reduce(function(u,f){return o[f].maxAge-Date.now()>=0&&(u[f]=o[f]),u},{}),o[r])){m({loading:!1,status:"OK",data:o[r].data});return}(n=w.current)==null||n.getPlacePredictions(S({},C.current,{input:r}),function(u,f){if(m({loading:!1,status:f,data:u||[]}),p&&f==="OK"){o[r]={data:u,maxAge:Date.now()+p*1e3};try{sessionStorage.setItem(d,JSON.stringify(o))}catch{}}})},g),[p,d,O,C]),J=i.useCallback(function(r,n){n===void 0&&(n=!0),N(r),w.current&&n&&K(r)},[K]);return i.useEffect(function(){if(!R)return function(){return null};var r=window,n=r.google;return!h.current&&!(n!=null&&n.maps)&&v?window[v]=b:b(),function(){window[v]&&delete window[v]}},[v,h,b,R]),{ready:G,value:I,suggestions:j,setValue:J,clearSuggestions:O,clearCache:q,init:b}},z="💡 use-places-autocomplete: Please provide an address when using getGeocode() with the componentRestrictions.",U=function(t){var e=new window.google.maps.Geocoder;return new Promise(function(a,l){e.geocode(t,function(g,c){c!=="OK"&&l(c),!t.address&&t.componentRestrictions&&(console.error(z),a(g)),a(g)})})},W=function(t){var e=t.geometry.location,a=e.lat,l=e.lng;return{lat:a(),lng:l()}};export{W as a,U as g,Q as u};
