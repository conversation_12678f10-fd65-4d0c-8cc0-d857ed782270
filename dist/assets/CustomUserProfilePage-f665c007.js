import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as z,r as a,f as ge,L as he,k as Se}from"./vendor-851db8c1.js";import{u as ne}from"./react-hook-form-687afde5.js";import{o as ie}from"./yup-2824f222.js";import{c as oe,a as ce,e as be}from"./yup-54691517.js";import{M as Q,T as ye,A as je,G as J,e as se,d as de,t as q,b as p,H as Ce,J as _e,R as Pe,K as ke,u as Ie,N as Me,O as Ae,P as Ee,f as Fe,h as Te,Q as $e,F as Be,S as De}from"./index-13fd629e.js";import"./index-02625b16.js";import{I as Le}from"./ImageCropModal-9d3311e2.js";import{F as Re,a as Ue}from"./index.esm-51ae62c8.js";import{S as ze}from"./index.esm-92169588.js";import{b as Oe}from"./index.esm-c561e951.js";import{u as we,a as Ze,C as fe}from"./@stripe/react-stripe-js-64f0e61f.js";import{L as Ge}from"./index.esm-3a36c7d6.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import{B as He}from"./BackButton-11ba52b2.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-image-crop-1f5038af.js";let V=new Q,qe=new ye;const Ye=()=>{const b=oe({email:ce().email().required()}).required(),{dispatch:v}=z.useContext(je),[N,y]=a.useState("");z.useState({});const[P,E]=a.useState("");a.useState(!1);const[u,f]=a.useState(!1),[x,M]=a.useState({}),[G,k]=a.useState(!0),[Y,F]=a.useState(null),[$,h]=a.useState(""),{dispatch:j}=z.useContext(J),[w,B]=a.useState(!1),[g,R]=a.useState(null),[O,D]=a.useState(!1),[T,S]=a.useState(null),{register:Z,handleSubmit:s,setError:l,setValue:m,formState:{errors:o}}=ne({resolver:ie(b)}),C=localStorage.getItem("user");async function L(){var i;k(!0);try{const n=await qe.getList("profile",{filter:[`user_id,eq,${C}`],join:["user|user_id"]}),r=(i=n==null?void 0:n.list)==null?void 0:i[0];if(r){const d=r.user||{},t=r.id,c={...r,...d,profile_id:t,user_id:d.id};M(c),m("email",d==null?void 0:d.email),m("first_name",d==null?void 0:d.first_name),m("last_name",d==null?void 0:d.last_name),m("phone",d==null?void 0:d.phone),m("bio",d==null?void 0:d.bio),y(d==null?void 0:d.email),E(d==null?void 0:d.photo),m("gender",r==null?void 0:r.gender),m("address",r==null?void 0:r.address),m("city",r==null?void 0:r.city),m("state",r==null?void 0:r.state),m("zip_code",r==null?void 0:r.zip_code),m("ntrp",r==null?void 0:r.ntrp),v({type:"UPDATE_PROFILE",payload:c}),k(!1)}}catch(n){q(v,n.response.data.message?n.response.data.message:n.message)}}const _=["email","first_name","last_name","phone","bio","photo","alternative_phone","age_group","family_role","password","verify","status"],K=["gender","address","city","state","zip_code","ntrp","date_of_birth","country","house_no"];console.log("User Profile Data:",{user_id:x==null?void 0:x.user_id,profile_id:x==null?void 0:x.profile_id,defaultValues:x});const I=async(i,n)=>{try{f(!0);const r={[i]:n},d=_.includes(i),t=K.includes(i);if(d){V.setTable("user");const c=await V.callRestAPI({id:x==null?void 0:x.user_id,...r},"PUT");c.error?U(c):(p(j,"Profile Updated",4e3),F(null),h(""),L())}else if(t){V.setTable("profile");const c=await V.callRestAPI({id:x==null?void 0:x.profile_id,...r},"PUT");c.error?U(c):(p(j,"Profile Updated",4e3),F(null),h(""),L())}else{p(j,"Unknown field type: "+i,4e3,"error"),f(!1);return}f(!1)}catch(r){f(!1),l(i,{type:"manual",message:r!=null&&r.message&&r==null?void 0:r.message}),q(v,r!=null&&r.message&&r==null?void 0:r.message)}},U=i=>{if(i.validation){const n=Object.keys(i.validation);for(let r=0;r<n.length;r++){const d=n[r];l(d,{type:"manual",message:i.validation[d]})}}},te=i=>{try{if(i.size>2*1024*1024){p(j,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}S(i.type);const n=new FileReader;n.onload=()=>{R(n.result),D(!0)},n.readAsDataURL(i)}catch(n){p(j,n==null?void 0:n.message,3e3,"error"),console.log(n)}},X=async i=>{try{B(!0);const n=T==="image/png",r=new File([i],`cropped_profile.${n?"png":"jpg"}`,{type:n?"image/png":"image/jpeg"});let d=new FormData;d.append("file",r);let t=await V.uploadImage(d);I("photo",t==null?void 0:t.url)}catch(n){p(j,n==null?void 0:n.message,3e3,"error"),console.log(n)}finally{B(!1)}},ae=()=>{I("photo",null),M({...x,photo:null})};return z.useEffect(()=>{L()},[]),e.jsxs("div",{className:"",children:[G||w&&e.jsx(se,{}),e.jsx("div",{className:"",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile details"})}),e.jsx(Le,{isOpen:O,onClose:()=>D(!1),image:g,onCropComplete:X}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:P||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsx("div",{children:e.jsxs("div",{className:"flex flex-col items-start justify-between gap-2",children:[e.jsx("p",{className:" font-medium text-gray-700",children:"Profile Picture"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:ae,disabled:!P,className:"rounded-xl border border-red-600 px-3 py-1.5  text-red-600 disabled:opacity-50",children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5  text-gray-700",children:["Change Photo",e.jsx("input",{type:"file",accept:"image/*",onChange:i=>te(i.target.files[0]),className:"hidden"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"})]})})]}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"gender",label:"Gender",type:"select",options:["male","female","other"]},{key:"email",label:"Email",note:"Your email is not shared with other users."},{key:"phone",label:"Phone"},{key:"address",label:"Address"},{key:"city",label:"City"},{key:"state",label:"State"},{key:"zip_code",label:"Zip Code"},{key:"ntrp",label:"NTRP"},{key:"bio",label:"Bio",type:"textarea"}].map(i=>e.jsx("div",{children:Y===i.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{className:" font-medium text-gray-700",children:i.label}),e.jsx("button",{onClick:()=>F(null),className:" text-primaryBlue underline hover:text-primaryBlue",children:"Cancel"})]}),i.type==="select"?e.jsxs("select",{value:$,onChange:n=>h(n.target.value),className:"mt-1 w-full rounded-md border border-gray-300 p-2",children:[e.jsxs("option",{value:"",children:["Select ",i.label.toLowerCase()]}),i.options.map(n=>e.jsx("option",{value:n,children:n.charAt(0).toUpperCase()+n.slice(1)},n))]}):i.type==="textarea"?e.jsx("textarea",{value:$,onChange:n=>h(n.target.value),rows:4,className:"mt-1 w-full rounded-md border border-gray-300 p-2"}):e.jsx("input",{type:"text",value:$,onChange:n=>h(n.target.value),className:"mt-1  w-full rounded-xl border border-gray-300 p-2"}),i.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:i.note}),e.jsx("div",{className:"mt-2",children:e.jsx(de,{loading:u,onClick:()=>I(i.key,$),className:"rounded-xl bg-primaryBlue px-4 py-2  font-medium text-white hover:bg-primaryBlue",children:"Save"})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:" font-medium text-gray-500",children:i.label}),e.jsx("button",{onClick:()=>{F(i.key),h((x==null?void 0:x[i.key])||"")},className:" text-primaryBlue hover:text-indigo-800",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:(x==null?void 0:x[i.key])||"--"}),i.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:i.note})]})},i.key))})]})]})})]})};let Je=new Q;function Ke({getData:b,onClose:v}){const[N,y]=a.useState(!1),P=we(),E=Ze(),{dispatch:u}=a.useContext(J),f=oe({user:be(),token:ce()}),{register:x,setValue:M,handleSubmit:G,setError:k,formState:{errors:Y}}=ne({resolver:ie(f)}),F=async $=>{y(!0),P.createToken(E.getElement(fe)).then(async h=>{if(console.log(h),h.error){p(u,h.error||"Something went wrong");return}const j={sourceToken:h.token.id};try{const w=await Je.createCustomerStripeCard(j);if(!w.error)p(u,"Card added successfully");else if(w.validation){const B=Object.keys(w.validation);for(let g=0;g<B.length;g++){const R=B[g];p(u,w.validation[R],3e3)}}b(),v()}catch(w){console.error(w),p(u,w.message,5e3),q(u,w.code)}finally{y(!1)}})};return e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mt-5",children:e.jsxs("form",{className:"",onSubmit:G(F),children:[e.jsx(fe,{className:"mb-3 rounded p-4 shadow-inner",options:{hidePostalCode:!0,style:{base:{backgroundColor:"",fontSize:"14px",lineHeight:"20px"}}}}),e.jsx(de,{loading:N,type:"submit",className:"inline-block rounded-lg bg-primaryBlue px-3 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:N?"Adding...":"Add card"})]})})})}let le=new Q;function Ve(){var Z;const[b,v]=a.useState("bank_cards"),[N,y]=a.useState(!1),[P,E]=a.useState(!1),[u,f]=a.useState(""),[x,M]=a.useState(10),[G,k]=a.useState(!1),[Y,F]=a.useState(!1),[$,h]=a.useState(!1),[j,w]=z.useState({});we();const B=oe({user:be(),token:ce()}),{dispatch:g}=z.useContext(J);ne({resolver:ie(B)});const R=[{id:"bank_cards",label:"Bank cards",icon:e.jsx(ke,{})}],O=async s=>{E(!0);try{console.log("Saving card:",s),await new Promise(l=>setTimeout(l,1e3)),y(!1)}catch(l){console.error("Error saving card:",l)}finally{E(!1)}};async function D(s){var l,m;try{h(!0);const{data:o,limit:C,error:L,message:_}=await le.getCustomerStripeCards(s);if(console.log(o),L&&p(g,_,5e3),!o)return;u||f(((l=o==null?void 0:o.data[0])==null?void 0:l.id)??""),w(o),M(+C),k(u&&u!==((m=o.data[0])==null?void 0:m.id)),F(o.has_more)}catch(o){console.error("ERROR",o),p(g,o.message,5e3),q(dispatch,o.code)}finally{h(!1)}}const T=async s=>{h(!0);const{error:l,message:m}=await le.setStripeCustomerDefaultCard(s);if(p(g,m),l){console.error(l);return}D({}),h(!1)},S=async s=>{h(!0);const{isDeleted:l,error:m,message:o}=await le.deleteCustomerStripeCard(s);if(p(g,o),m){console.error(m);return}D({}),h(!1)};return z.useEffect(()=>{D({})},[]),e.jsxs("div",{className:"mx-auto max-w-2xl p-4 sm:p-6",children:[e.jsx("h2",{className:"mb-6 text-2xl font-semibold",children:"Payment methods"}),$&&e.jsx(se,{}),e.jsx("div",{className:"mb-6 flex flex-wrap gap-4 border-b sm:flex-nowrap sm:gap-0 sm:space-x-4",children:R.map(s=>e.jsxs("button",{onClick:()=>v(s.id),className:`flex items-center space-x-2 px-1 pb-2 ${b===s.id?"border-b-2 border-primaryBlue text-primaryBlue":"text-gray-500 hover:text-gray-700"}`,children:[e.jsx("span",{className:"text-lg",children:s.icon}),e.jsx("span",{children:s.label})]},s.id))}),b==="bank_cards"&&e.jsxs("div",{className:"space-y-4",children:[(Z=j==null?void 0:j.data)==null?void 0:Z.map(s=>e.jsxs("div",{className:"flex flex-col justify-between gap-3 rounded-xl border p-4",children:[e.jsxs("div",{className:"flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("div",{className:"text-sm text-gray-700 sm:text-base",children:s.customer.email}),e.jsx("div",{className:"flex items-center justify-end space-x-4",children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>T(s.id),className:`text-sm sm:text-base ${s.id===s.customer.default_source?"text-green-600":"text-blue-600"} underline`,children:s.id===s.customer.default_source?"Default":"Set Default"}),e.jsx("button",{onClick:()=>S(s.id),className:"text-sm text-gray-500 underline sm:text-base",children:"Delete"})]})})]}),e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"flex space-x-2",children:[s.brand==="Visa"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#1A1F71] text-white",children:e.jsx(Oe,{size:18})}),s.brand==="Mastercard"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#EB001B] text-white",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"2.11676in",height:"1.5in",viewBox:"0 0 152.407 108",children:e.jsxs("g",{children:[e.jsx("rect",{width:"152.407",height:"108",style:"fill: none"}),e.jsxs("g",{children:[e.jsx("rect",{x:"60.4117",y:"25.6968",width:"31.5",height:"56.6064",style:"fill: #ff5f00"}),e.jsx("path",{d:"M382.20839,306a35.9375,35.9375,0,0,1,13.7499-28.3032,36,36,0,1,0,0,56.6064A35.938,35.938,0,0,1,382.20839,306Z",transform:"translate(-319.79649 -252)",style:"fill: #eb001b"}),e.jsx("path",{d:"M454.20349,306a35.99867,35.99867,0,0,1-58.2452,28.3032,36.00518,36.00518,0,0,0,0-56.6064A35.99867,35.99867,0,0,1,454.20349,306Z",transform:"translate(-319.79649 -252)",style:"fill: #f79e1b"}),e.jsx("path",{d:"M450.76889,328.3077v-1.1589h.4673v-.2361h-1.1901v.2361h.4675v1.1589Zm2.3105,0v-1.3973h-.3648l-.41959.9611-.41971-.9611h-.365v1.3973h.2576v-1.054l.3935.9087h.2671l.39351-.911v1.0563Z",transform:"translate(-319.79649 -252)",style:"fill: #f79e1b"})]})]})})}),s.brand==="MasterCard"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded  text-white",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"2.11676in",height:"1.5in",viewBox:"0 0 152.407 108",children:e.jsxs("g",{children:[e.jsx("rect",{width:"152.407",height:"108",style:{fill:"none"}}),e.jsxs("g",{children:[e.jsx("rect",{x:"60.4117",y:"25.6968",width:"31.5",height:"56.6064",style:{fill:"#ff5f00"}}),e.jsx("path",{d:"M382.20839,306a35.9375,35.9375,0,0,1,13.7499-28.3032,36,36,0,1,0,0,56.6064A35.938,35.938,0,0,1,382.20839,306Z",transform:"translate(-319.79649 -252)",style:{fill:"#eb001b"}}),e.jsx("path",{d:"M454.20349,306a35.99867,35.99867,0,0,1-58.2452,28.3032,36.00518,36.00518,0,0,0,0-56.6064A35.99867,35.99867,0,0,1,454.20349,306Z",transform:"translate(-319.79649 -252)",style:{fill:"#f79e1b"}}),e.jsx("path",{d:"M450.76889,328.3077v-1.1589h.4673v-.2361h-1.1901v.2361h.4675v1.1589Zm2.3105,0v-1.3973h-.3648l-.41959.9611-.41971-.9611h-.365v1.3973h.2576v-1.054l.3935.9087h.2671l.39351-.911v1.0563Z",transform:"translate(-319.79649 -252)",style:{fill:"#f79e1b"}})]})]})})}),s.brand==="American Express"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#006FCF] text-white",children:e.jsx(ze,{size:18})}),s.brand==="Discover"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#FF6000] text-sm font-bold text-white",children:"DISC"}),s.brand==="Diners Club"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#0069AA] text-white",children:e.jsx(Ce,{size:18})}),s.brand==="JCB"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#0B4EA2] text-white",children:e.jsx(_e,{size:18})}),s.brand==="UnionPay"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#00447C] text-sm font-bold text-white",children:"UP"})]}),e.jsxs("p",{className:"text-sm text-black sm:text-base",children:[s.brand," • ",s.last4]})]}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-500",children:["Exp. ",s.exp_month,"/",s.exp_year.toString().slice(-2)]})})]})]},s.id)),e.jsxs("button",{onClick:()=>y(!0),className:"flex w-full items-center justify-center space-x-2 rounded-lg border border-blue-600 px-4 py-2 text-blue-600 hover:bg-blue-50 sm:w-auto",children:[e.jsx("span",{children:"+"}),e.jsx("span",{children:"Add card"})]})]}),e.jsx(Pe,{isOpen:N,onClose:()=>y(!1),title:"Add card",primaryButtonText:"Add card",onPrimaryAction:O,submitting:P,showFooter:!1,children:e.jsx(Ke,{onSubmit:O,getData:D,onClose:()=>y(!1)})})]})}let W=new Q,We=new ye;const ee=b=>new Date(b*1e3).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});function Qe(){const[b,v]=a.useState({}),[N,y]=a.useState([]),[P,E]=a.useState(10),[u,f]=a.useState(1),[x,M]=a.useState(1),[G,k]=a.useState(0),[Y,F]=a.useState(!1),[$,h]=a.useState(!1),[j,w]=a.useState(!1),[B,g]=a.useState(!1),[R,O]=a.useState([]),[D,T]=a.useState(!1),[S,Z]=a.useState({}),[s,l]=a.useState({}),{triggerRefetch:m}=Ie(),o=ge(),{dispatch:C}=a.useContext(J),{dispatch:L,state:_}=z.useContext(je),K=a.useRef(null);if(K.current===null){const t=parseInt(localStorage.getItem("user")),c=_.originalUserId||(isNaN(t)?null:t);c&&(K.current=c)}const I=K.current,U=_.isFamilyMemberSwitch?_.user:I,te=I;async function X(t,c,A={}){w(!0);try{console.log(_),A.user_id=U;const H=await W.getCustomerStripeSubscriptions({page:t,limit:c},A),{list:me,total:ve,limit:Ne,num_pages:pe,page:re}=H,xe={};me.forEach(ue=>{ue.status==="active"&&(xe[ue.subId]=!0)}),v(xe),y(me),E(+Ne),f(+pe),M(+re),k(+ve),F(+re>1),h(+re+1<=+pe)}catch(H){console.error(H),q(L,H.code)}finally{w(!1)}}async function ae(){try{const t=await W.getCustomerStripeSubscription(U);l(t.customer)}catch(t){console.error(t),q(L,t.code)}}const i=async t=>{T(!0);try{const c=await W.cancelStripeSubscription(t);if(c.error){console.error(c.message),p(C,c.message,7500,"error");return}p(C,c.message,1e4,"success"),X(1,P),g(!1),Z({})}catch(c){console.error(c),p(C,c.message,7500,"error"),q(L,c.code)}finally{T(!1)}},n=async()=>{try{if(!I||isNaN(I)){console.error("Invalid mainAccountUserId:",I);return}const t=I,A=(await We.getList("user",{filter:[`guardian,eq,${t}`,"role,cs,user"]})).list.filter(H=>H.id!==I);O(A)}catch(t){console.error("Error fetching family members:",t)}};a.useEffect(()=>{X(1,P)},[U]),a.useEffect(()=>{ae()},[U]),a.useEffect(()=>{n()},[]);const r=async t=>{try{if(t===I)return;if(!R.find(H=>H.id===t)){p(C,"Invalid family member selected",7500,"error");return}const A=await W.callRawAPI("/v3/api/custom/courtmatchup/user/groups/switch-family-member",{family_member_id:t},"POST");if(A.error){p(C,A.message||"Failed to switch user",7500,"error");return}L({type:"SWITCH_TO_FAMILY_MEMBER",payload:{user_id:A.family_member.id,token:A.token,role:"user",first_name:A.family_member.first_name,last_name:A.family_member.last_name,original_user_id:te,family_member:A.family_member}}),p(C,`Switched to ${A.family_member.first_name}'s account`,3e3,"success")}catch(c){console.error("Error switching user:",c),p(C,c.message||"Failed to switch user",7500,"error")}},d=async()=>{try{if(!_.isFamilyMemberSwitch)return;const t=await W.callRawAPI("/v3/api/custom/courtmatchup/user/data/switch-back",{},"GET");if(t.error){p(C,t.message||"Failed to switch back",7500,"error");return}L({type:"SWITCH_BACK_TO_MAIN",payload:{user_id:t.user_id,token:t.token,role:"user",first_name:t.first_name,last_name:t.last_name}}),p(C,"Switched back to your account",3e3,"success")}catch(t){console.error("Error switching back:",t),p(C,t.message||"Failed to switch back",7500,"error")}};return e.jsxs("div",{className:"mx-auto max-w-3xl p-3 sm:p-6",children:[j&&e.jsx(se,{}),e.jsxs("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Membership"}),e.jsxs("div",{className:"flex flex-col gap-2 sm:flex-row",children:[e.jsx(he,{to:"/user/membership/buy",className:"w-full rounded-lg bg-gray-600 px-4 py-2 text-center text-white transition-colors hover:bg-gray-700 sm:w-auto",children:"Browse Plans"}),e.jsx("button",{onClick:()=>{const t=new URLSearchParams;_.isFamilyMemberSwitch&&(t.set("familyMemberId",U),_.familyMemberDetails&&t.set("familyMemberName",_.familyMemberDetails.first_name)),o(`/user/membership/buy?${t.toString()}`)},className:"w-full rounded-lg bg-blue-600 px-4 py-2 text-center text-white transition-colors hover:bg-blue-700 sm:w-auto",children:(()=>_.isFamilyMemberSwitch&&_.familyMemberDetails?`Buy plan for ${_.familyMemberDetails.first_name}`:"Buy new plan")()})]})]}),e.jsx("div",{className:"mb-4 border-b border-gray-200"}),e.jsx("div",{className:"relative mb-4 flex justify-end",children:e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"relative",children:e.jsxs("select",{value:U,onChange:t=>{const c=parseInt(t.target.value);c!==U&&(c===I?d():r(c))},className:"w-full appearance-none rounded-lg border border-gray-300 bg-white px-3 py-2 pr-8 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",children:[e.jsx("option",{value:I,children:"Myself"}),R.map(t=>e.jsxs("option",{value:t.id,children:[t.first_name," ",t.last_name," (",t.family_role||"Family Member",")"]},t.id))]})}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Select the user whose membership you want to manage"})]})}),!j&&N.length===0&&e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-xl bg-gray-50 px-4 py-12 text-center",children:[e.jsx("div",{className:"mb-4 rounded-full bg-blue-100 p-3",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"No Active Memberships"}),e.jsx("p",{className:"mb-6 text-sm text-gray-600",children:"You currently don't have any active membership plans."}),e.jsx(he,{to:"/user/membership/buy",className:"rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700",children:"Browse Plans"})]}),N.map(t=>e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50",children:[e.jsxs("button",{onClick:()=>{v(c=>({...c,[t.subId]:!c[t.subId]}))},className:"flex w-full cursor-pointer flex-col gap-2 p-4 hover:bg-gray-100 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[b[t.subId]?e.jsx(Me,{size:20}):e.jsx(Ae,{size:20}),e.jsxs("span",{className:"text-sm font-medium sm:text-base",children:[ee(t.currentPeriodStart)," -"," ",ee(t.currentPeriodEnd)]})]}),e.jsxs("div",{className:"flex items-center justify-between gap-3 pl-7 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600 sm:text-base",children:t.planName}),e.jsx("span",{className:`rounded-full px-3 py-1 text-sm capitalize ${t.status==="active"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:t.status})]})]}),b[t.subId]&&e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6 rounded-xl border bg-white p-4",children:[e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row sm:items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Plan price"}),e.jsxs("div",{className:"text-left sm:text-right",children:[e.jsx("div",{className:"font-semibold",children:Ee(t.planAmount)}),e.jsx("div",{className:"text-sm text-gray-500",children:"Billed annually"})]})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Purchased on"}),e.jsx("span",{className:"text-sm sm:text-base",children:ee(t.createdAt)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Valid until"}),e.jsx("span",{className:"text-sm sm:text-base",children:ee(t.currentPeriodEnd)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Subscription ID"}),e.jsx("span",{className:"break-all text-sm sm:text-base",children:t.subId})]}),t.status==="active"&&e.jsx("div",{className:"flex justify-center sm:justify-start",children:e.jsx("button",{onClick:()=>{g(!0),Z(t)},className:"w-full rounded-xl bg-red-500 px-5 py-2 text-white transition-colors hover:bg-red-600 sm:w-auto",children:"Cancel plan"})})]})})]},t.subId)),e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center p-4 ${B?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-lg rounded-3xl bg-white p-4 sm:p-6",children:[e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row",children:[e.jsx("div",{children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#FDEDF0"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1 22.7V24.5H20.9V22.7H19.1ZM19.1 15.5V20.9H20.9V15.5H19.1Z",fill:"#DF1C41"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Are you sure?"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Are you sure you want to cancel membership?"})]})]}),e.jsxs("div",{className:"flex flex-col-reverse gap-3 border-t pt-4 sm:flex-row sm:justify-end",children:[e.jsx("button",{onClick:()=>{g(!1),Z({})},className:"w-full rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:"Go back"}),e.jsx(de,{onClick:()=>{i(S.subId),m()},className:"w-full rounded-lg bg-red-600 px-4 py-2 text-white hover:bg-red-700 sm:w-auto",loading:D,children:"Yes, cancel"})]})]})]})]})}let Xe=new Q;function es(){const{dispatch:b}=z.useContext(J),[v,N]=a.useState({}),[y,P]=a.useState([]);a.useState(0),a.useState(!1),a.useState(!1);const[E,u]=a.useState(""),[f,x]=a.useState(""),[M,G]=a.useState(""),[k,Y]=a.useState("desc"),[F,$]=a.useState(!1),[h,j]=a.useState(null),w=a.useRef({}),B=s=>{N(l=>({...l,[s]:!l[s]}))},g=async(s={})=>{$(!0);try{let l=new URLSearchParams;s.sort&&l.append("sort",s.sort),s.invoice_type&&l.append("invoice_type",s.invoice_type),s.search&&l.append("search",s.search);const m=await Xe.callRawAPI(`/v3/api/custom/courtmatchup/user/billing/invoices${l.toString()?`?${l.toString()}`:""}`,{},"GET");if(m.error){p(b,m.message,5e3);return}P(m.invoices||[])}catch(l){console.error("ERROR",l),p(b,l.message,5e3),q(dispatch,l.message)}finally{$(!1)}},R=s=>{u(s.target.value),g({sort:k})},O=()=>!f&&!M?y:y.filter(s=>{try{const l=new Date(s.date);if(isNaN(l.getTime()))return!1;const m=f?new Date(f):null,o=M?new Date(M):null;return m&&o?l>=m&&l<=o:m?l>=m:o?l<=o:!0}catch{return console.error("Invalid date:",s.date),!1}}),D=()=>{const s=k==="asc"?"desc":"asc";Y(s),g({sort:s})};a.useEffect(()=>{g({sort:k})},[]);const T=s=>new Date(s).toLocaleDateString(),S=s=>Number(s).toLocaleString("en-US",{style:"currency",currency:"usd"}),Z=s=>{j(s);const l=window.open("","_blank");if(!l){p(b,"Please allow pop-ups to print receipts",5e3);return}if(!w.current[s]){p(b,"Receipt content not found",5e3);return}const o=y.find(C=>C.id===s);l.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt #${o.receipt_id}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .receipt-header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 1px solid #eee;
              padding-bottom: 20px;
            }
            .receipt-title {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .receipt-id {
              font-size: 16px;
              color: #666;
            }
            .receipt-date {
              font-size: 14px;
              color: #666;
              margin-top: 5px;
            }
            .receipt-body {
              margin-bottom: 30px;
            }
            .receipt-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 10px;
              padding: 5px 0;
              border-bottom: 1px solid #f5f5f5;
            }
            .receipt-label {
              font-weight: 500;
              color: #666;
            }
            .receipt-total {
              margin-top: 20px;
              font-size: 18px;
              font-weight: bold;
              border-top: 2px solid #eee;
              padding-top: 10px;
            }
            .receipt-footer {
              margin-top: 40px;
              text-align: center;
              font-size: 14px;
              color: #999;
            }
            @media print {
              body {
                padding: 0;
                margin: 0;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="receipt-header">
            <div class="receipt-title">Payment Receipt</div>
            <div class="receipt-id">Invoice #${o.receipt_id}</div>
            <div class="receipt-date">Date: ${T(o.date)}</div>
          </div>

          <div class="receipt-body">
            <div class="receipt-row">
              <span class="receipt-label">Type:</span>
              <span>${o.type}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Amount:</span>
              <span>${S(o.amount)}</span>
            </div>
            ${o.coach_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Coach Fee:</span>
              <span>${S(o.coach_fee)}</span>
            </div>
            `:""}
            ${o.service_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Service Fee:</span>
              <span>${S(o.service_fee)}</span>
            </div>
            `:""}
            ${o.club_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Club Fee:</span>
              <span>${S(o.club_fee)}</span>
            </div>
            `:""}
            <div class="receipt-row">
              <span class="receipt-label">Valid Until:</span>
              <span>${T(o.valid_until)}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Payment Method:</span>
              <span>${o.payment_method}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Status:</span>
              <span class="capitalize">${o.status}</span>
            </div>

            <div class="receipt-total">
              <div class="receipt-row">
                <span class="receipt-label">Total:</span>
                <span>${S(o.total_amount)}</span>
              </div>
            </div>
          </div>

          <div class="receipt-footer">
            Thank you for your payment.
          </div>

          <script>
            window.onload = function() {
              window.print();
              setTimeout(function() {
                window.close();
              }, 500);
            };
          <\/script>
        </body>
      </html>
    `),l.document.close(),setTimeout(()=>{j(null)},1e3)};return e.jsxs("div",{className:"mx-auto max-w-3xl p-4 sm:p-6",children:[F&&e.jsx(se,{}),e.jsxs("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Billing"}),e.jsxs("select",{className:"w-full rounded-lg border-gray-200 px-4 py-2 capitalize sm:w-auto",onChange:s=>g({invoice_type:s.target.value,sort:k}),children:[e.jsx("option",{value:"",children:"All bills"}),e.jsx("option",{value:"subscription",children:"Subscription"}),e.jsx("option",{value:"lesson",children:"Lesson"}),e.jsx("option",{value:"clinic",children:"Clinic"}),e.jsx("option",{value:"club_court",children:"Club Court"})]})]}),e.jsx("div",{className:"mb-5 border-b border-gray-200"}),e.jsxs("div",{className:"mb-6 flex flex-col gap-3 sm:flex-row",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(Fe,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("input",{type:"text",value:E,onChange:R,placeholder:"Search by plan name or invoice number",className:"w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex w-full gap-2 sm:w-auto",children:[e.jsx("input",{type:"date",value:f,onChange:s=>x(s.target.value),placeholder:"Start date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),e.jsx("input",{type:"date",value:M,onChange:s=>G(s.target.value),placeholder:"End date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),(f||M)&&e.jsx("button",{onClick:()=>{x(""),G("")},className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 hover:bg-gray-50 hover:text-gray-700",title:"Clear dates",children:"✕"})]}),e.jsxs("button",{onClick:D,className:"flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:[k==="asc"?"Oldest first":"Latest first",e.jsx(Ge,{className:`transform ${k==="desc"?"rotate-180":""}`})]})]}),e.jsx("div",{className:"space-y-4",children:O().length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"No billing records found"}):O().map(s=>e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm",children:[e.jsxs("button",{onClick:()=>B(s.id),className:"flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Te,{className:`transform transition-transform ${v[s.id]?"":"-rotate-90"}`}),e.jsx("span",{className:"text-sm sm:text-base",children:s.type})]}),e.jsxs("div",{className:"flex items-center justify-between gap-4 pl-6 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600",children:T(s.create_at)}),e.jsx("span",{className:"font-medium",children:S(s.total_amount)})]})]}),v[s.id]&&e.jsx("div",{className:"mt-2 rounded-lg bg-white p-4",children:e.jsxs("div",{className:"space-y-4",ref:l=>w.current[s.id]=l,children:[e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Amount"}),e.jsx("span",{children:S(s.amount)})]}),s.coach_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Coach fee"}),e.jsx("span",{children:S(s.coach_fee)})]}),s.service_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Service fee"}),e.jsx("span",{children:S(s.service_fee)})]}),s.club_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Club fee"}),e.jsx("span",{children:S(s.club_fee)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Invoice ID"}),e.jsxs("span",{children:["#",s.receipt_id]})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Date"}),e.jsx("span",{children:T(s.date)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Valid until"}),e.jsx("span",{children:T(s.valid_until)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Payment method"}),e.jsx("span",{children:s.payment_method})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Status"}),e.jsx("span",{className:"capitalize",children:s.status})]}),e.jsxs("button",{onClick:l=>{l.stopPropagation(),Z(s.id)},className:"mt-4 flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50",children:[e.jsx($e,{className:"text-lg"}),"Print Receipt"]})]})})]},s.id))})]})}const Hs=()=>{const{dispatch:b}=z.useContext(J),[v]=Se(),[N,y]=a.useState("profile"),P=ge();a.useEffect(()=>{const u=v.get("tab");u&&y({"payment-methods":"payment-methods",profile:"profile",membership:"membership",billing:"billing"}[u]||"profile")},[v.get("tab")]);const E=[{label:"Profile details",value:"profile",icon:Be},{label:"Payment methods",value:"payment-methods",icon:Re},{label:"Membership",value:"membership",icon:Ue},{label:"Billing",value:"billing",icon:De}];return a.useEffect(()=>{b({type:"SETPATH",payload:{path:"profile"}})},[]),e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"mx-auto  max-w-6xl px-4",children:[e.jsx(He,{onBack:()=>P("/user/dashboard")}),e.jsxs("div",{className:"flex flex-col gap-8 md:flex-row",children:[e.jsx("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-sm md:max-w-xs",children:e.jsx("nav",{className:"no-scrollbar flex w-full flex-row overflow-x-auto p-4 md:flex-col",children:E.map(u=>{const f=u.icon;return e.jsxs("button",{onClick:()=>{y(u.value),P(`/user/profile?tab=${u.value}`)},className:`mr-2 flex min-w-fit items-center whitespace-nowrap rounded-lg px-4 py-3 transition-colors md:mb-2 md:mr-0 ${N===u.value?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50"}`,children:[e.jsx(f,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{className:"text-sm",children:u.label})]},u.value)})})}),e.jsxs("div",{className:" w-full rounded-xl bg-white p-6 shadow-sm",children:[N==="profile"&&e.jsx(Ye,{}),N==="payment-methods"&&e.jsx(Ve,{}),N==="membership"&&e.jsx(Qe,{}),N==="billing"&&e.jsx(es,{})]})]})]})})};export{Hs as default};
