import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import{P as e}from"./@fortawesome/react-fontawesome-13437837.js";const c=({isLoading:n,children:r,message:i="Loading...",spinnerSize:o="md",opacity:a=50,blur:l=!1,customSpinner:t=null,className:d=""})=>{if(!n)return r;const m={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-10 w-10"};return s.jsxs("div",{className:"relative",children:[r,s.jsx("div",{className:`absolute inset-0 flex items-center justify-center
          ${l?"backdrop-blur-sm":""}
          ${`bg-white/${a}`}
          ${d}`,children:s.jsxs("div",{className:"flex flex-col items-center gap-3 rounded-lg bg-white/90 p-4 shadow-lg",children:[t||s.jsx("div",{className:`border-3 animate-spin rounded-full border-gray-300 border-b-navy-700
                ${m[o]}`}),i&&s.jsx("span",{className:"text-sm font-medium text-gray-700",children:i})]})})]})};c.propTypes={isLoading:e.bool.isRequired,children:e.node.isRequired,message:e.string,spinnerSize:e.oneOf(["sm","md","lg","xl"]),opacity:e.number,blur:e.bool,customSpinner:e.node,className:e.string};export{c as L};
