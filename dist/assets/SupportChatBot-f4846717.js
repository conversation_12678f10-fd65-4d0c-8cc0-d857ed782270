import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r}from"./vendor-851db8c1.js";import{M as $e,T as Ue,u as He,m as Ge,n as Qe,A as ze,o as Ke,p as We,q as de,r as ue,v as Je,x as Ye,y as O,z as me,C as Ve,D as pe}from"./index-13fd629e.js";import{u as Xe}from"./react-hook-form-687afde5.js";const at=r.memo(()=>{var ce;console.log("🤖 SupportChatBot component rendered!");const[h,V]=r.useState(!1),[X,Z]=r.useState(null),[ee,q]=r.useState(null),[ge,he]=r.useState([]),[fe,xe]=r.useState([]),[ye,L]=r.useState([]),[be,A]=r.useState(!1),[M,K]=r.useState(""),[$,u]=r.useState([]),[U,H]=r.useState(!1),[C,G]=r.useState(!0),[we,je]=r.useState(!1),[Q,k]=r.useState(!1),[R,y]=r.useState(!1),[l,W]=r.useState(null),[i,I]=r.useState(null),[w,B]=r.useState(null),d=r.useMemo(()=>new $e,[]),Se=r.useMemo(()=>new Ue,[]),[j,f]=r.useState({categories:!1,subcategories:!1,faqs:!1,ticket:!1,chat:!1}),{club:o}=He(),g=localStorage.getItem("user"),{register:Ne,handleSubmit:ve,reset:_e}=Xe(),[F,Ce]=r.useState([]),[S,z]=r.useState(!1),[x,J]=r.useState(null),N=r.useRef(null),{state:v,markMessagesAsRead:T,subscribeToRoomMessages:te,publishRoomMessages:se}=Ge(),{subscribe:ae,setPollingConfig:ke}=Qe(),{state:b}=r.useContext(ze),_=v.unreadMessages;console.log("🔍 SupportChatBot context state:",{chatStateRooms:v.rooms,roomsLength:(ce=v.rooms)==null?void 0:ce.length,unreadCount:_,authUser:b.user,isAuthenticated:b.isAuthenticated,clubSupportEnabled:o==null?void 0:o.supportchat_enabled});const E=r.useRef(null),Y=()=>{E.current&&(E.current.scrollTop=E.current.scrollHeight)},P=()=>{setTimeout(()=>{Y()},100)};r.useEffect(()=>{Re(),Ee()},[o]),r.useEffect(()=>{var e;if(console.log("🔍 fetchExistingRooms useEffect triggered",{clubSupportEnabled:o==null?void 0:o.supportchat_enabled,roomsLength:(e=v.rooms)==null?void 0:e.length,rooms:v.rooms,isAuthenticated:b.isAuthenticated,authUser:b.user}),(o==null?void 0:o.supportchat_enabled)!==1){console.log("❌ Support chat not enabled, skipping fetchExistingRooms");return}console.log("✅ Support chat enabled, calling fetchExistingRooms"),Ae()},[v.rooms,o==null?void 0:o.supportchat_enabled]),r.useEffect(()=>{const e=setInterval(()=>{F!=null&&F.setting_value&&oe(F)},3e5);return()=>clearInterval(e)},[F]);const Re=async()=>{f(e=>({...e,categories:!0}));try{d.setTable("faq_category");const e=await d.callRestAPI({},"GETALL");if(e.list){const s=e.list.filter(n=>n.club_id===0),a=e.list.filter(n=>n.club_id===(o==null?void 0:o.id));he([...s,...a])}}catch(e){console.error("Error loading FAQ categories:",e)}finally{f(e=>({...e,categories:!1}))}},Te=async e=>{try{const s=await d.callRawAPI("/v3/api/lambda/realtime/online",{room_id:e});console.log("Realtime response:",s)}catch(s){console.error("Error fetching realtime:",s)}};r.useEffect(()=>{i&&Te(i)},[i]);const oe=e=>{if(!(e!=null&&e.setting_value)){G(!1);return}try{const s=JSON.parse(e.setting_value),n=new Date().toTimeString().slice(0,8),m=s.some(c=>{const p=c.from,D=c.until;return D<p?n>=p||n<=D:n>=p&&n<=D});G(m)}catch(s){console.error("Error parsing working hours:",s),G(!1)}},Ee=async()=>{try{const e=await Se.getOne("setting",5,{});console.log("result",e),e.model&&(Ce(e.model),oe(e.model))}catch(e){console.error("Error fetching working hours:",e),G(!1)}},re=async e=>{try{d.setTable("user");const s=await d.callRestAPI({id:e},"GET");s&&s.model&&(W({first_name:s.model.first_name,last_name:s.model.last_name,photo:s.model.photo}),console.log("Support member fetched:",s.model))}catch(s){console.error("Error fetching support member:",s)}},Ae=async()=>{console.log("🚀 fetchExistingRooms function called!");try{const e=v.rooms;if(console.log("fetchExistingRooms called with rooms:",(e==null?void 0:e.length)||0),e&&e.length>0){const a=e.sort((n,m)=>new Date(m.update_at)-new Date(n.update_at)).find(n=>n.resolved===0);console.log("unresolvedRoom",a),a?(console.log("Found unresolved room, setting up chat:",a.id),J(a),z(!0),I(a.id),B(a.chat_id),y(!0),k(!0),A(!0),a.admin_id&&a.admin_id!==0?(console.log("Room already has a support agent assigned:",a.admin_id),y(!0),N.current=a.admin_id,await re(a.admin_id)):(N.current=null,W(null)),await Ie(a)):(console.log("No unresolved rooms found, showing FAQs/categories only"),z(!1),I(null),B(null),y(!1),k(!1),A(!1),N.current=null)}else console.log("No rooms found, resetting states"),z(!1),J(null),I(null),B(null),y(!1),k(!1),A(!1),N.current=null}catch(e){console.error("Error fetching existing rooms:",e)}},Ie=async e=>{try{console.log("Loading existing messages for room:",e.id,"chat_id:",e.chat_id);let s=new Date().toISOString().split("T")[0];const a=await d.getChats(e.id,e.chat_id,s);if(!a||!a.model){console.log("No messages found in response"),u([]);return}const n=a.model;console.log("Found messages for room:",n.length);const m=n.map(c=>{let p;return typeof c.chat=="string"?p=JSON.parse(c.chat):p=c.chat,{id:c.id,type:p.user_id===g.toString()?"user":"support",content:p.message,timestamp:new Date(p.timestamp||c.timeStamp||c.create_at)}}).sort((c,p)=>new Date(c.timestamp)-new Date(p.timestamp));console.log("Formatted messages:",m.length),m.length>0?u([{type:"system",content:"You have been reconnected to your previous conversation with support.",timestamp:new Date},...m]):u([]),e.admin_id&&e.admin_id!==0?(console.log("Room already has a support agent assigned:",e.admin_id),y(!0),N.current=e.admin_id,await re(e.admin_id)):(N.current=null,W(null)),P(),h&&await T(e.id)}catch(s){console.error("Error loading existing messages:",s),u([])}},Be=async e=>{f(s=>({...s,subcategories:!0}));try{d.setTable("faq_subcategory");const s=await d.callRestAPI({filter:[`category_id,eq,${e}`]},"GETALL");s.list&&xe(s.list)}catch(s){console.error("Error loading FAQ subcategories:",s)}finally{f(s=>({...s,subcategories:!1}))}},Fe=async e=>{f(s=>({...s,faqs:!0}));try{d.setTable("faq");const s=await d.callRestAPI({filter:[`subcategory_id,eq,${e}`],join:["faq_subcategory|subcategory_id"]},"GETALL");s.list&&L(s.list)}catch(s){console.error("Error loading FAQs:",s)}finally{f(s=>({...s,faqs:!1}))}},Pe=e=>{Z(e),q(null),L([]),Be(e.id),u(s=>[...s,{type:"user",content:`I'll go with ${e.name}.`},{type:"bot",content:`Great! Here are some common questions related to ${e.name}:`}])},De=e=>{q(e),Fe(e.id),u(s=>[...s,{type:"user",content:e.name},{type:"bot",content:"Here's the information you're looking for:"}])},Oe=async e=>{f(s=>({...s,ticket:!0}));try{d.setTable("ticket"),await d.callRestAPI({resolved:0,club_id:o==null?void 0:o.id,user_id:g,request:e.message},"POST"),je(!0),u(s=>[...s,{type:"bot",content:t.jsxs("div",{className:"flex items-start gap-2",children:[t.jsx(Ve,{className:"mt-1 text-green-500"}),t.jsx("span",{children:"Your ticket has been created successfully. We'll get back to you via email shortly."})]})}]),H(!1),_e()}catch(s){console.error("Error creating ticket:",s)}finally{f(s=>({...s,ticket:!1}))}},qe=()=>{if(S&&x&&x.resolved===0){k(!0),A(!0),y(!0);return}C?(k(!0),A(!0),u(e=>[...e,{type:"bot",content:"I see! This sounds like something our support team can help with. Please type your message below and we'll connect you with a live support agent."}])):(H(!0),u(e=>[...e,{type:"bot",content:t.jsxs("div",{className:"flex items-start gap-2",children:[t.jsx(pe,{className:"mt-1 text-orange-500"}),t.jsx("span",{children:"Our support team is currently offline. Please create a ticket and we'll get back to you via email as soon as possible."})]})}]))};async function Le(){try{const e=await d.callRawAPI("/v3/api/custom/courtmatchup/user/realtime/room",{user_id:g,other_user_id:1},"POST");return d.setTable("room"),await d.callRestAPI({id:e.room_id,admin_id:0,other_user_id:0},"PUT"),e}catch(e){console.log(e)}}const ne=async()=>{if(M.length<1)return;if(!C){u(s=>[...s,{type:"system",content:t.jsxs("div",{className:"flex items-start gap-2",children:[t.jsx(pe,{className:"mt-1 text-orange-500"}),t.jsx("span",{children:"Our support team is currently offline. Please create a ticket for assistance."})]}),timestamp:new Date}]),K(""),k(!1),H(!0);return}const e=M;u(s=>[...s,{type:"user",content:e,timestamp:new Date}]),K(""),P();try{let s=new Date().toISOString().split("T")[0];f(m=>({...m,chat:!0}));let a=i,n=w;if(console.log("Current room state:",{roomId:i,chatId:w,hasOpenRoom:S,currentExistingRoom:x==null?void 0:x.id}),S&&x&&x.resolved===0)a?console.log("Using current room:",a):(console.log("Using existing unresolved room:",x.id),a=x.id,n=x.chat_id,I(a),B(n),y(!0));else if(!a||!n){console.log("Creating new room - no existing unresolved room found");const m=await Le();a=m.room_id,n=m.chat_id,I(a),B(n),z(!0),J({id:a,chat_id:n,resolved:0,admin_id:0}),N.current=null,R||(u(c=>[...c,{type:"system",content:"You are now connected to our support team. Please wait while we assign an agent to assist you.",timestamp:new Date}]),y(!0))}else console.log("Using current room:",a);await d.postMessage({room_id:a,chat_id:n,user_id:g,message:e,date:s})}catch(s){console.error(s),u(a=>[...a,{type:"system",content:"There was an error sending your message. Please try again.",timestamp:new Date}])}finally{f(s=>({...s,chat:!1}))}},Me=()=>{Z(null),q(null),L([]),u(e=>[...e,{type:"user",content:"Let's try a different category."},{type:"bot",content:"Please choose a category below to get started:"}])};r.useEffect(()=>{P(),h&&i&&$.length>0&&$.some(s=>s.type==="support"&&s.user_id!==g)&&T(i)},[$,h,i,g]),r.useEffect(()=>{h&&i&&_>0&&T(i)},[h,i,_]),r.useEffect(()=>{h&&E.current&&P()},[h,E.current]),r.useEffect(()=>{g&&(o==null?void 0:o.supportchat_enabled)===1&&b.isAuthenticated?ke(g,null):b.isAuthenticated||console.log("🚫 User not authenticated - skipping SupportChatBot polling setup")},[g,o==null?void 0:o.supportchat_enabled,b.isAuthenticated]);const le=r.useCallback(async e=>{if(console.log("📡 SupportChatBot: Received room-specific message update:",e),e.messages&&e.messages.length>0){const s=e.messages.map(a=>{let n;return typeof a.chat=="string"?n=JSON.parse(a.chat):n=a.chat,{id:a.id,type:n.user_id===g.toString()?"user":"support",content:n.message,timestamp:new Date(n.timestamp||a.timeStamp||a.create_at)}}).sort((a,n)=>new Date(a.timestamp)-new Date(n.timestamp));u(a=>{const n=new Set(a.map(c=>c.id)),m=s.filter(c=>!n.has(c.id));return m.length>0?[...a,...m].sort((p,D)=>new Date(p.timestamp)-new Date(D.timestamp)):a}),h&&await T(i),P()}},[g,h,i,T]),ie=r.useCallback(async e=>{if(e.message&&i&&w){console.log("📡 SupportChatBot: Received general message update from optimized polling");try{let s=new Date().toISOString().split("T")[0];const a=await d.getChats(i,w,s);if(a&&a.model){const n=a.model;se(i,n,{source:"polling",timestamp:Date.now()})}}catch(s){console.error("Error fetching messages for room:",s)}}},[i,w,d,se]);return r.useEffect(()=>{if(i&&w&&(o==null?void 0:o.supportchat_enabled)===1){const e=te(i,le),s=ae("message",ie);return()=>{e(),s()}}},[i,w,o==null?void 0:o.supportchat_enabled,te,ae,le,ie]),console.log("🤖 SupportChatBot Debug:",{clubSupportChatEnabled:o==null?void 0:o.supportchat_enabled,isAuthenticated:b.isAuthenticated,userId:g,clubName:o==null?void 0:o.name}),(o==null?void 0:o.supportchat_enabled)!=1?(console.log("🚫 SupportChatBot: Support chat not enabled for club"),null):t.jsx(t.Fragment,{children:(o==null?void 0:o.supportchat_enabled)==1&&t.jsxs("div",{className:"fixed bottom-4 right-4 z-[9999] max-w-[220px] space-y-2",children:[h?t.jsx("button",{onClick:()=>V(!1),className:"shadow-full flex h-14 w-14 items-center justify-center rounded-full bg-primaryBlue p-2",children:t.jsx(We,{className:"text-xl text-white"})}):t.jsx("button",{onClick:()=>{V(!0),i&&_>0&&T(i),setTimeout(()=>{Y()},200)},className:"relative w-full rounded-xl bg-gray-100 p-2 shadow-lg transition-colors hover:bg-gray-200",children:t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(Ke,{className:"text-2xl text-primaryBlue"}),t.jsxs("div",{className:"flex-1",children:[t.jsx("p",{className:"text-lg font-medium",children:"Need help?"}),t.jsx("p",{className:"text-sm text-gray-500",children:"Chat with assistant"})]}),_>0&&t.jsx("div",{className:"absolute -right-1 -top-1 flex h-6 w-6 items-center justify-center rounded-full bg-red-600 text-xs font-bold text-white",children:_>9?"9+":_})]})}),t.jsx(t.Fragment,{children:h&&t.jsxs("div",{className:"fixed bottom-48 left-2 flex max-h-[500px] w-[350px] flex-col overflow-hidden rounded-xl bg-white shadow-lg",children:[t.jsx("div",{className:"rounded-t-2xl bg-primaryBlue p-2",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("div",{className:"flex items-center gap-2",children:R?t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"relative",children:[l!=null&&l.photo?t.jsx("img",{className:"h-8 w-8 rounded-full object-cover",src:l.photo,alt:"Support member"}):t.jsx(de,{className:"text-2xl text-white"}),t.jsx("div",{className:"absolute -bottom-1 -right-1 h-3 w-3 rounded-full bg-green-400 ring-2 ring-white"})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-lg font-medium text-white",children:l!=null&&l.first_name&&(l!=null&&l.last_name)?`${l.first_name} ${l.last_name}`:"Support Team"}),t.jsx("p",{className:"text-xs text-white opacity-80",children:"Online"})]})]}):t.jsxs(t.Fragment,{children:[t.jsx(ue,{className:"text-2xl text-white"}),t.jsxs("div",{children:[t.jsx("p",{className:"text-lg font-medium text-white",children:"AceBot"}),t.jsx("p",{className:"text-xs text-white opacity-80",children:C?"Support Available":"Support Offline"})]})]})}),R?t.jsx("div",{className:"rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800",children:"Live Support"}):t.jsx("div",{className:`rounded-full px-2 py-1 text-xs font-medium ${C?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"}`,children:C?"Available":"Offline"})]})}),t.jsx("div",{ref:E,className:"min-h-0 flex-1 overflow-y-auto p-4",onLoad:()=>Y(),children:t.jsxs("div",{className:"space-y-4",children:[!S&&t.jsxs("div",{className:"mb-6 flex items-start gap-2",children:[t.jsx("div",{className:"rounded-full bg-yellow-50 p-2",children:t.jsx(Je,{className:"text-yellow-500"})}),t.jsx("div",{className:"max-w-[80%] rounded-lg bg-gray-50 p-3",children:t.jsxs("p",{className:"text-gray-800",children:["Hi there! 👋 How can I help you today?",t.jsx("br",{}),"Please choose a category below to get started:"]})})]}),$.map((e,s)=>t.jsxs("div",{className:`flex items-start gap-2 ${e.type==="user"?"justify-end":""}`,children:[e.type==="bot"&&t.jsx("div",{className:"rounded-full bg-yellow-50 p-2",children:t.jsx(ue,{className:"text-primaryBlue"})}),e.type==="support"&&t.jsx("div",{className:"rounded-full bg-green-50 p-2",children:l!=null&&l.photo?t.jsx("img",{className:"h-6 w-6 rounded-full object-cover",src:l.photo,alt:"Support member"}):t.jsx(de,{className:"text-green-600"})}),e.type==="system"&&t.jsx("div",{className:"rounded-full bg-gray-100 p-2",children:t.jsx(Ye,{className:"text-gray-600"})}),t.jsxs("div",{className:`max-w-[80%] rounded-lg p-3 ${e.type==="user"?"bg-primaryBlue text-white":e.type==="support"?"bg-green-100 text-green-900":e.type==="system"?"border border-gray-200 bg-gray-100 text-gray-800":"bg-gray-50"}`,children:[t.jsx("p",{className:"whitespace-pre-wrap",children:e.content}),e.timestamp&&t.jsx("p",{className:"mt-1 text-xs opacity-70",children:new Date(e.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})]},s)),!S&&!X&&!U&&!Q&&t.jsx("div",{className:"flex flex-col items-end space-y-4",children:j.categories?t.jsx("div",{className:"flex items-center justify-center py-8",children:t.jsx(O,{className:"h-6 w-6 animate-spin text-primaryBlue"})}):t.jsx(t.Fragment,{children:t.jsxs("div",{className:"w-full",children:[t.jsx("p",{className:"mb-2 text-right font-medium text-gray-700",children:"General"}),t.jsx("div",{className:"flex flex-col items-end space-y-2",children:ge.filter(e=>e.club_id===0).map((e,s)=>t.jsxs("button",{onClick:()=>Pe(e),className:"flex w-fit items-center gap-2 rounded-lg bg-blue-50 px-4 py-2 text-left text-sm text-blue-900 transition-colors hover:bg-blue-100",children:[t.jsxs("span",{children:[s+1,"️⃣"]}),e.name]},e.id))})]})})}),!S&&X&&!ee&&!U&&!Q&&t.jsxs("div",{className:"flex flex-col items-end space-y-2",children:[t.jsxs("button",{onClick:Me,className:"mb-2 flex w-fit items-end justify-end gap-2 rounded-lg bg-gray-50 px-4 py-2 text-left text-sm text-gray-700 transition-colors hover:bg-gray-100",children:[t.jsx(me,{className:"text-gray-500"}),"Back to Categories"]}),j.subcategories?t.jsx("div",{className:"flex items-center justify-center py-8",children:t.jsx(O,{className:"h-6 w-6 animate-spin text-primaryBlue"})}):fe.map(e=>t.jsx("button",{onClick:()=>De(e),className:"w-fit rounded-lg bg-blue-50 px-4 py-2 text-left text-sm text-blue-900 transition-colors hover:bg-blue-100",children:e.name},e.id))]}),!S&&ee&&!U&&!Q&&t.jsxs("div",{className:"flex flex-col items-end space-y-4",children:[t.jsxs("button",{onClick:()=>{q(null),L([])},className:"mb-2 flex w-fit items-center gap-2 rounded-lg bg-gray-50 px-4 py-2 text-left text-sm text-gray-700 transition-colors hover:bg-gray-100",children:[t.jsx(me,{className:"text-gray-500"}),"Back to Questions"]}),j.faqs?t.jsx("div",{className:"flex items-center justify-center py-8",children:t.jsx(O,{className:"h-6 w-6 animate-spin text-primaryBlue"})}):ye.map(e=>t.jsxs("div",{className:"w-fit rounded-lg bg-gray-50 p-4 text-sm text-gray-900",children:[t.jsx("p",{className:"mb-2 font-medium",children:e.faq_subcategory.name}),t.jsx("p",{className:"mb-4 whitespace-pre-line",children:e.answer}),t.jsxs("div",{className:"mt-4 space-y-4",children:[t.jsx("div",{className:"text-center font-medium text-gray-700",children:"Problem still not resolved?"}),C?t.jsx("button",{onClick:qe,className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Chat with Support"}):t.jsx("button",{onClick:()=>H(!0),className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Create Ticket"})]})]},e.id))]}),U&&!we&&t.jsxs("form",{onSubmit:ve(Oe),className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Please describe your issue"}),t.jsx("textarea",{...Ne("message",{required:!0}),rows:4,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",placeholder:"Please provide details about your issue..."})]}),t.jsx("button",{type:"submit",disabled:j.ticket,className:"flex w-full items-center justify-center rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50",children:j.ticket?t.jsx(O,{className:"h-4 w-4 animate-spin"}):"Submit Ticket"})]})]})}),(Q||be||R)&&t.jsxs("div",{className:"flex-shrink-0 border-t border-gray-200 bg-white p-3 shadow-md",children:[R&&t.jsx("div",{className:"mb-2 flex items-center justify-between",children:t.jsxs("div",{className:"flex items-center gap-1 rounded-full bg-green-100 px-3 py-1 text-xs text-green-800",children:[t.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),t.jsxs("span",{children:["Connected to"," ",l!=null&&l.first_name&&(l!=null&&l.last_name)?`${l.first_name} ${l.last_name}`:"Support Team"]})]})}),t.jsxs("form",{onSubmit:e=>{e.preventDefault(),ne()},className:"flex gap-2",children:[t.jsx("input",{type:"text",value:M,onChange:e=>K(e.target.value),placeholder:R?"Type your message to support...":"Type your message...",className:"flex-1 rounded-lg border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-primaryBlue",onKeyDown:e=>{e.key==="Enter"&&!e.shiftKey&&(e.preventDefault(),ne())}}),t.jsx("button",{type:"submit",disabled:j.chat||M.trim()==="",className:"flex items-center justify-center rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80 disabled:opacity-50",children:j.chat?t.jsx(O,{className:"h-4 w-4 animate-spin"}):"Send"})]})]})]})})]})})});export{at as S};
