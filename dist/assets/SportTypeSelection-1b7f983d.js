import{j as E}from"./@nivo/heatmap-ba1ecfff.js";import{b as q,r}from"./vendor-851db8c1.js";import{S as V}from"./SelectionOptionsCard-30c39f7f.js";const B=q.memo(({sports:m,onSelectionChange:$,isChildren:G=!1,initialSport:f=null,initialType:a=null,initialSubType:h=null,userPermissions:S=null,courts:y=[],filterMode:i="reservation"})=>{const[o,z]=r.useState(f),[n,T]=r.useState(a),[w,b]=r.useState(h),[K,j]=r.useState([]),[J,x]=r.useState([]),[v,H]=r.useState(!1),[N,O]=r.useState(!1),[c,L]=r.useState(!1);console.log("=== SportTypeSelection Debug ==="),console.log("Sports count:",m||0),console.log("Courts count:",y||0),console.log("FilterMode:",i);const A=r.useRef(f),_=r.useRef(a),F=r.useRef(h),Q=(e,t)=>{try{if(!e.court_settings)return!0;const l=JSON.parse(e.court_settings);switch(t){case"reservation":return l.allow_reservation!==!1;case"lesson":return l.allow_lesson!==!1;case"clinic":return l.allow_clinic!==!1;case"buddy":return l.allow_buddy!==!1;default:return!0}}catch(l){return console.warn(`Failed to parse court_settings for court ${e.id}:`,l),!1}},R=(e,t=null,l=null)=>{if(!y||!Array.isArray(y)||!i)return!0;const s=y.filter(p=>{var d;return!(((d=p.sport_id)==null?void 0:d.toString())!==(e==null?void 0:e.toString())||t!==null&&p.type!==t||l!==null&&p.sub_type!==l)});return s.length===0?!1:s.some(p=>Q(p,i))},u=q.useMemo(()=>{if(!m||!Array.isArray(m))return[];let e=m;return e=e.filter(t=>t.status===1),S!=null&&S.applicable_sports&&Array.isArray(S.applicable_sports)&&(e=e.filter(t=>S.applicable_sports.includes(t.id))),i&&y&&y.length>0&&(e=e.filter(t=>R(t.id))),e},[m,S,y,i]),g=u==null?void 0:u.find(e=>e.id===o);return r.useEffect(()=>{z(f),T(a),b(h),A.current=f,_.current=a,F.current=h,L(!0)},[f,a,h]),r.useEffect(()=>{if((u==null?void 0:u.length)>0&&c){const e=u==null?void 0:u.find(t=>t.id===f);if(e){const t=(e==null?void 0:e.sport_types)||[],s=t.filter(d=>d.type&&d.type.trim()!=="").length>0;j(t),H(s);let p=!1;if(a&&s){const d=e.sport_types.find(C=>C.type===a),k=((d==null?void 0:d.subtype)||[]).filter(C=>C&&C.trim()!=="");p=k.length>0,p&&x(k)}O(p),(f!==null||a!==null||h!==null)&&$({sport:f,type:a,subType:h,hasTypes:s,hasSubTypes:p})}}},[u,c,f,a,h]),r.useEffect(()=>{if(g){const e=g.sport_types||[],t=i?e.filter(s=>!s.type||s.type.trim()===""?R(o):R(o,s.type)):e;j(t);const l=t.filter(s=>s.type&&s.type.trim()!=="");H(l.length>0),c&&o!==A.current&&(l.length===0?T(""):T(null),b(null))}else j([]),H(!1),c&&o!==A.current&&(T(null),b(null))},[o,g,c,y,i]),r.useEffect(()=>{if(g&&n!==null){if(!v||n===""){x([]),O(!1),c&&n!==_.current&&b("");return}const e=g.sport_types.find(s=>s.type===n),t=(e==null?void 0:e.subtype)||[],l=i?t.filter(s=>!s||s.trim()===""?!1:R(o,n,s)):t.filter(s=>s&&s.trim()!=="");x(l),O(l.length>0),c&&n!==_.current&&(l.length===0?b(""):b(null))}else(!g||n===null)&&(x([]),O(!1),c&&n!==_.current&&b(null))},[n,g,v,c,o,y,i]),r.useEffect(()=>{if(c){const e=o!==A.current,t=n!==_.current,l=w!==F.current;(e||t||l)&&($({sport:o,type:n,subType:w,hasTypes:v,hasSubTypes:N}),A.current=o,_.current=n,F.current=w)}},[o,n,w,v,N,c]),E.jsxs("div",{className:`h-fit w-full space-y-6 rounded-lg bg-white ${G?"p-0 shadow-none":"p-4 shadow-5"}`,children:[E.jsx(V,{title:"Sports",options:u||[],selectedOption:o,onOptionSelect:z,emptyMessage:"No sports found",optionType:"sport"}),v&&E.jsx(V,{title:"Type",options:K,selectedOption:n,onOptionSelect:T,showPlaceholder:!o,optionType:"type"}),v&&n&&N&&J.length>0&&E.jsx(V,{title:"Sub-Type",options:J,selectedOption:w,onOptionSelect:e=>{console.log("Subtype selected:",e),b(e)},showPlaceholder:!n,optionType:"subtype"})]})});B.displayName="SportTypeSelection";const Z=B;export{Z as S};
