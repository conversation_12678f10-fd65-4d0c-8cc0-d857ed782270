import{j as c}from"./@nivo/heatmap-ba1ecfff.js";import{r as a,b as G}from"./vendor-851db8c1.js";const P=({days:F,isSelected:y,handleTimeSelect:g,handleDeleteTime:N,containerRef:H,renderTimeSlotContent:M,disableTimeSlot:f})=>{const i=a.useRef(null),[l,R]=a.useState(!1),[U,E]=a.useState(null),[X,S]=a.useState(null),[o,h]=a.useState({start:null,current:null}),D=a.useRef(new Map),d=a.useRef(null),v=a.useRef(new Set),k=t=>{const[e,n]=t.split(":"),r=parseInt(e),s=r>=12?"PM":"AM";return`${r%12||12}:${n} ${s}`},x=(()=>{const t=[];for(let e=8;e<=22;e++)for(let n=0;n<60;n+=30){const r=`${e.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}:00`;t.push({display:k(r),value:r})}return t})(),C=a.useCallback((t,e)=>{if(!t||!e.start||!e.current)return!1;const n=i.current.getBoundingClientRect(),r=t.getBoundingClientRect(),s=i.current.querySelector(".overflow-x-auto"),m=s?s.scrollLeft:0,u={left:r.left-n.left+m,right:r.right-n.left+m,top:r.top-n.top,bottom:r.bottom-n.top},$=Math.min(e.start.x,e.current.x),b=Math.max(e.start.x,e.current.x),T=Math.min(e.start.y,e.current.y),q=Math.max(e.start.y,e.current.y);return!(u.right<$||u.left>b||u.bottom<T||u.top>q)},[]),j=a.useCallback(t=>{d.current&&cancelAnimationFrame(d.current),d.current=requestAnimationFrame(()=>{const e=new Set;D.current.forEach((n,r)=>{if(C(n,t)){const[s,m]=r.split("|"),u=x.find(b=>b.value===m);(f&&u?f(u,s):!1)||(e.add(r),v.current.has(r)||g(m,s))}}),v.current=e})},[C,g,f,x]),L=(t,e)=>{if(!i.current)return{x:0,y:0};const n=i.current.getBoundingClientRect(),r=i.current.querySelector(".overflow-x-auto"),s=r?r.scrollLeft:0;return{x:t-n.left+s,y:e-n.top}},A=(t,e,n)=>{n.preventDefault(),R(!0),E(t),S(e);const r=L(n.clientX,n.clientY);h({start:r,current:r}),v.current.clear()},I=(t,e)=>{y(t,e)?N(t,e):g(t,e)},p=a.useCallback(t=>{if(l){t.preventDefault();const e=L(t.clientX,t.clientY),n={...o,current:e};h(n),j(n)}},[l,o,j]),w=a.useCallback(()=>{R(!1),E(null),S(null),h({start:null,current:null}),v.current.clear(),d.current&&(cancelAnimationFrame(d.current),d.current=null),window.removeEventListener("mousemove",p),window.removeEventListener("mouseup",w)},[p]);G.useEffect(()=>{const t=()=>{l&&w()};if(l)return window.addEventListener("mousemove",p),window.addEventListener("mouseup",t),()=>{window.removeEventListener("mousemove",p),window.removeEventListener("mouseup",t)}},[l,p,w]);const B=(t,e)=>{l&&g(t,e)};return c.jsxs("div",{className:"relative w-full overflow-hidden",ref:i,children:[l&&o.start&&o.current&&c.jsx("div",{style:{position:"absolute",left:Math.min(o.start.x,o.current.x),top:Math.min(o.start.y,o.current.y),width:Math.abs(o.current.x-o.start.x),height:Math.abs(o.current.y-o.start.y),backgroundColor:"rgba(59, 130, 246, 0.2)",border:"2px solid rgb(59, 130, 246)",pointerEvents:"none",zIndex:1e3}}),c.jsx("div",{className:"w-full overflow-x-auto pb-4",children:c.jsx("div",{className:"grid min-w-[900px] grid-cols-7 gap-5",children:F.map(t=>c.jsxs("div",{className:"rounded-md bg-white p-2 text-center",children:[c.jsx("div",{className:"mb-2 rounded-md bg-[#F6F8FA] px-3 py-2 font-medium",children:t}),c.jsx("div",{className:"space-y-2",children:x.map(e=>{const n=y(e.value,t),r=f?f(e,t):!1;return c.jsxs("div",{className:"relative",onMouseEnter:()=>!r&&B(e.value,t),children:[c.jsx("button",{ref:s=>{s&&D.current.set(`${t}|${e.value}`,s)},className:`w-full rounded-md border-2 border-gray-100 px-3 py-2 text-sm font-medium ${r?"cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400":n?"border-2 border-primaryBlue bg-[#EBF1FF] text-primaryBlue":"border-gray-300 text-gray-500 hover:border-gray-400"}`,disabled:r,onMouseDown:s=>!r&&A(e.value,t,s),onClick:s=>{!l&&!r&&I(e.value,t)},children:e.display}),M&&M(e,t)]},`${t}-${e.value}`)})})]},t))})})]})},J=P;export{J as T};
