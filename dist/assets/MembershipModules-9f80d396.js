import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as ie}from"./index.esm-51ae62c8.js";import{r as o,b as oe}from"./vendor-851db8c1.js";import{u as ce,d as re,G as de,P as ue,ad as D,R as be,_ as xe,M as me,X as le,Z as ne,b as M}from"./index-13fd629e.js";import{b as T}from"./@headlessui/react-a5400090.js";import{C as pe}from"./ChevronRightIcon-efb4c46c.js";import{T as ge}from"./TrashIcon-7d213648.js";function he({title:h,titleId:r,...i},u){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:u,"aria-labelledby":r},i),h?o.createElement("title",{id:r},h):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}const _e=o.forwardRef(he),$=_e;function ye({onSubmit:h,onClose:r,initialData:i,mode:u,onDelete:F}){var R,f;const{sports:x}=ce(),[j,d]=o.useState({name:u==="create",price:u==="create",features:new Set});console.log("currentMode",u);const[A,N]=o.useState(!1),I=i?{...i,advance_booking_days:i.advance_booking_days||{court:10,lesson:10,clinic:10,buddy:10},advance_booking_enabled:i.advance_booking_enabled||{court:!0,lesson:!0,clinic:!0,buddy:!0},applicable_sports:i.applicable_sports||[]}:{plan_id:null,plan_name:"",price:0,allow_clinic:!1,allow_buddy:!1,allow_coach:!1,allow_groups:!1,allow_court:!1,features:[],advance_booking_days:{court:10,lesson:10,clinic:10,buddy:10},advance_booking_enabled:{court:!0,lesson:!0,clinic:!0,buddy:!0},applicable_sports:[]},[l,n]=o.useState(I),k=async()=>{N(!0);const s={...l,advance_booking_enabled:{court:!!l.advance_booking_enabled.court,lesson:!!l.advance_booking_enabled.lesson,clinic:!!l.advance_booking_enabled.clinic,buddy:!!l.advance_booking_enabled.buddy}};console.log("Submitting plan data:",s),await h(s,u),N(!1)},O=(s,t)=>{n(a=>({...a,features:a.features.map(c=>c.id===s?{...c,text:t}:c)})),d(a=>{const c=new Set(a.features);return c.delete(s),{...a,features:c}})},B=s=>{n(t=>({...t,features:t.features.filter(a=>a.id!==s)}))},m=()=>{const s=Math.max(...l.features.map(t=>t.id),0)+1;n(t=>({...t,features:[...t.features,{id:s,text:""}]})),d(t=>({...t,features:new Set([...t.features,s])}))},w={allow_court:"Court booking",allow_clinic:"Clinics",allow_coach:"Lesson",allow_buddy:"Find a Buddy",allow_groups:"My Groups"},C=(s,t)=>{n(a=>({...a,[s]:t})),d(a=>({...a,[s]:!1}))},_=s=>{n(t=>({...t,[s]:!t[s]}))},L=s=>{n(t=>{const a=t.applicable_sports||[];return a.includes(s)?{...t,applicable_sports:a.filter(b=>b!==s)}:{...t,applicable_sports:[...a,s]}})},q=()=>{const t=((x==null?void 0:x.filter(a=>a.status===1))||[]).map(a=>a.id);n(a=>({...a,applicable_sports:t}))},G=()=>{n(s=>({...s,applicable_sports:[]}))};return e.jsxs("div",{className:"flex h-full flex-col gap-4",children:[e.jsxs("div",{className:"flex-1 space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Plan name"}),e.jsx("button",{className:"text-sm text-blue-600",onClick:()=>d(s=>({...s,name:!0})),children:"Edit"})]}),j.name?e.jsx("input",{type:"text",value:l.plan_name,onChange:s=>n(t=>({...t,plan_name:s.target.value})),onBlur:()=>C("plan_name",l.plan_name),className:"w-full rounded-md border border-gray-300 px-3 py-2",autoFocus:!0}):e.jsx("div",{children:l==null?void 0:l.plan_name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Price"}),e.jsx("button",{className:"text-sm text-blue-600",onClick:()=>d(s=>({...s,price:!0})),children:"Edit"})]}),j.price?e.jsx("input",{type:"number",value:l.price,onChange:s=>n(t=>({...t,price:parseFloat(s.target.value)})),onBlur:()=>C("price",l.price),className:"w-full rounded-md border border-gray-300 px-3 py-2",step:"0.01",autoFocus:!0}):e.jsx("div",{children:l.price===0?e.jsx("span",{className:"font-semibold text-green-600",children:"Free"}):`$${(R=l.price)==null?void 0:R.toFixed(2)}`})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Module"}),e.jsx("div",{className:"space-y-4",children:Object.entries(w).map(([s,t])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:t}),e.jsx(T,{checked:l[s],onChange:()=>_(s),className:`${l[s]?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${l[s]?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]},s))})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Applicable Sports"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{type:"button",onClick:q,className:"text-xs text-blue-600 hover:text-blue-800",children:"Select All"}),e.jsx("span",{className:"text-xs text-gray-400",children:"|"}),e.jsx("button",{type:"button",onClick:G,className:"text-xs text-blue-600 hover:text-blue-800",children:"Clear All"})]})]}),e.jsx("div",{className:"space-y-3",children:(x==null?void 0:x.filter(s=>s.status===1).length)>0?x.filter(s=>s.status===1).map(s=>{var t;return e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",id:`sport-${s.id}`,checked:((t=l.applicable_sports)==null?void 0:t.includes(s.id))||!1,onChange:()=>L(s.id),className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e.jsx("label",{htmlFor:`sport-${s.id}`,className:"cursor-pointer text-sm font-medium text-gray-700",children:s.name})]})},s.id)}):e.jsx("div",{className:"text-sm italic text-gray-500",children:"No active sports available. Please add sports in your club settings first."})}),((f=l.applicable_sports)==null?void 0:f.length)===0&&e.jsx("div",{className:"rounded-md bg-amber-50 p-2 text-xs text-amber-600",children:"⚠️ No sports selected. This membership will not apply to any specific sports."})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Advance Booking Days"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:"Court Reservation"}),e.jsx(T,{checked:l.advance_booking_enabled.court,onChange:()=>{const s=!l.advance_booking_enabled.court;console.log("Toggling court booking enabled:",s),n(t=>({...t,advance_booking_enabled:{...t.advance_booking_enabled,court:s}}))},className:`${l.advance_booking_enabled.court?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${l.advance_booking_enabled.court?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),l.advance_booking_enabled.court&&e.jsxs("div",{className:"flex items-center justify-between pl-4",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),e.jsx("input",{type:"number",min:"1",max:"365",value:l.advance_booking_days.court,onChange:s=>n(t=>({...t,advance_booking_days:{...t.advance_booking_days,court:parseInt(s.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:"Lesson Booking"}),e.jsx(T,{checked:l.advance_booking_enabled.lesson,onChange:()=>{const s=!l.advance_booking_enabled.lesson;console.log("Toggling lesson booking enabled:",s),n(t=>({...t,advance_booking_enabled:{...t.advance_booking_enabled,lesson:s}}))},className:`${l.advance_booking_enabled.lesson?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${l.advance_booking_enabled.lesson?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),l.advance_booking_enabled.lesson&&e.jsxs("div",{className:"flex items-center justify-between pl-4",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),e.jsx("input",{type:"number",min:"1",max:"365",value:l.advance_booking_days.lesson,onChange:s=>n(t=>({...t,advance_booking_days:{...t.advance_booking_days,lesson:parseInt(s.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:"Clinic/Program Booking"}),e.jsx(T,{checked:l.advance_booking_enabled.clinic,onChange:()=>{const s=!l.advance_booking_enabled.clinic;console.log("Toggling clinic booking enabled:",s),n(t=>({...t,advance_booking_enabled:{...t.advance_booking_enabled,clinic:s}}))},className:`${l.advance_booking_enabled.clinic?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${l.advance_booking_enabled.clinic?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),l.advance_booking_enabled.clinic&&e.jsxs("div",{className:"flex items-center justify-between pl-4",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),e.jsx("input",{type:"number",min:"1",max:"365",value:l.advance_booking_days.clinic,onChange:s=>n(t=>({...t,advance_booking_days:{...t.advance_booking_days,clinic:parseInt(s.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:"Find-a-Buddy Booking"}),e.jsx(T,{checked:l.advance_booking_enabled.buddy,onChange:()=>{const s=!l.advance_booking_enabled.buddy;console.log("Toggling buddy booking enabled:",s),n(t=>({...t,advance_booking_enabled:{...t.advance_booking_enabled,buddy:s}}))},className:`${l.advance_booking_enabled.buddy?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${l.advance_booking_enabled.buddy?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),l.advance_booking_enabled.buddy&&e.jsxs("div",{className:"flex items-center justify-between pl-4",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),e.jsx("input",{type:"number",min:"1",max:"365",value:l.advance_booking_days.buddy,onChange:s=>n(t=>({...t,advance_booking_days:{...t.advance_booking_days,buddy:parseInt(s.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]})]})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Plan features"}),e.jsxs("div",{className:"space-y-4",children:[l.features.map(s=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:["Feature ",s.id]}),e.jsxs("div",{className:"space-x-4",children:[e.jsx("button",{className:"text-sm text-red-600",onClick:()=>B(s.id),children:"Delete"}),e.jsx("button",{className:"text-sm text-blue-600",onClick:()=>d(t=>({...t,features:new Set([...t.features,s.id])})),children:"Edit"})]})]}),j.features.has(s.id)?e.jsx("input",{type:"text",value:s.text,onChange:t=>n(a=>({...a,features:a.features.map(c=>c.id===s.id?{...c,text:t.target.value}:c)})),onBlur:()=>O(s.id,s.text),className:"w-full rounded-md border border-gray-300 px-3 py-2",autoFocus:!0}):e.jsx("div",{children:s.text})]},s.id)),e.jsx("button",{className:"text-sm text-blue-600",onClick:m,children:"+ Add feature"})]})]})]}),e.jsxs("div",{className:"flex  flex-shrink-0 justify-between gap-4 border-t border-gray-200 px-4 py-4",children:[e.jsx("div",{className:"flex gap-2",children:u==="edit"&&F&&e.jsx("button",{type:"button",className:"rounded-xl border border-red-200 bg-red-50 px-3 py-2 text-sm font-semibold text-red-600 hover:bg-red-100",onClick:()=>F(i),children:"Delete Plan"})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{type:"button",className:"rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:r,children:"Cancel"}),e.jsx(re,{loading:A,type:"submit",className:"rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",onClick:k,children:u=="edit"?"Save changes":"Create plan"})]})]})]})}let g=new me;function Se({fetchProfileSettings:h,membershipPlans:r,profileSettings:i,role:u}){const[F,x]=o.useState(null),[j,d]=o.useState(!1),[A,N]=o.useState(""),[I,l]=o.useState(!1),[n,k]=o.useState(null),[O,B]=o.useState(!1),{club:m,sports:w}=ce(),C=localStorage.getItem("user"),{dispatch:_,state:L}=oe.useContext(de),q=a=>{if(!a||a.length===0)return"All Sports";if(!w||w.length===0)return"Loading...";const c=a.map(b=>{var p;return(p=w.find(v=>v.id===b))==null?void 0:p.name}).filter(Boolean);return c.length===0?"No Sports":c.length>2?`${c.slice(0,2).join(", ")} +${c.length-2} more`:c.join(", ")},G=a=>{x(a),d(!0)},R=async(a,c)=>{var b,p,v,V,J,U,X,K,W,Z,z;try{let y;if(c==="edit")y={membership_settings:r.map(S=>{var E,Q,Y,P,ee,ae,se,te;return S.plan_id===a.plan_id?{plan_id:a.plan_id,plan_name:a.plan_name,price:a.price,allow_clinic:a.allow_clinic,allow_buddy:a.allow_buddy,allow_coach:a.allow_coach,allow_groups:a.allow_groups,allow_court:a.allow_court,features:a.features,applicable_sports:a.applicable_sports||[],advance_booking_days:{court:((E=a.advance_booking_days)==null?void 0:E.court)||10,lesson:((Q=a.advance_booking_days)==null?void 0:Q.lesson)||10,clinic:((Y=a.advance_booking_days)==null?void 0:Y.clinic)||10,buddy:((P=a.advance_booking_days)==null?void 0:P.buddy)||10},advance_booking_enabled:{court:((ee=a.advance_booking_enabled)==null?void 0:ee.court)!==!1,lesson:((ae=a.advance_booking_enabled)==null?void 0:ae.lesson)!==!1,clinic:((se=a.advance_booking_enabled)==null?void 0:se.clinic)!==!1,buddy:((te=a.advance_booking_enabled)==null?void 0:te.buddy)!==!1}}:S})};else{const S=await g.callRawAPI("/v3/api/custom/courtmatchup/stripe/product",{name:a.plan_name,description:a.plan_name,club_id:(p=(b=L.clubProfile)==null?void 0:b.club)==null?void 0:p.id},"POST");console.log("stripeProductResponse",S);const E=await g.callRawAPI("/v3/api/custom/courtmatchup/stripe/price",{product_id:S.model,name:a.plan_name,amount:a.price,type:"recurring",interval:"month",interval_count:1,trial_days:0,usage_type:"licenced",usage_limit:0},"POST");y={membership_settings:[...r,{plan_id:E.model,plan_name:a.plan_name,price:a.price,allow_clinic:a.allow_clinic,allow_buddy:a.allow_buddy,allow_coach:a.allow_coach,allow_groups:a.allow_groups,allow_court:a.allow_court,features:a.features,applicable_sports:a.applicable_sports||[],advance_booking_days:{court:((v=a.advance_booking_days)==null?void 0:v.court)||10,lesson:((V=a.advance_booking_days)==null?void 0:V.lesson)||10,clinic:((J=a.advance_booking_days)==null?void 0:J.clinic)||10,buddy:((U=a.advance_booking_days)==null?void 0:U.buddy)||10},advance_booking_enabled:{court:((X=a.advance_booking_enabled)==null?void 0:X.court)||!0,lesson:((K=a.advance_booking_enabled)==null?void 0:K.lesson)||!0,clinic:((W=a.advance_booking_enabled)==null?void 0:W.clinic)||!0,buddy:((Z=a.advance_booking_enabled)==null?void 0:Z.buddy)||!0}}]}}console.log("Submitting membership plan data:",JSON.stringify(y,null,2));const H=await g.callRawAPI(u==="club"?"/v3/api/custom/courtmatchup/club/profile-edit":`/v3/api/custom/courtmatchup/admin/profile-edit/${(z=i==null?void 0:i.user)==null?void 0:z.id}`,y,"POST");g.setTable("activity_logs"),await g.callRestAPI({user_id:C,activity_type:le.club_ui,action_type:ne.UPDATE,data:JSON.stringify(y),club_id:m==null?void 0:m.id,description:"Updated membership plans"},"POST"),H.error&&M(_,H.message||"Failed to save plan",3e3,"error"),d(!1),h(),f()}catch(y){M(_,y.message||"Failed to save plan",3e3,"error")}},f=()=>{d(!1),x(null)},s=a=>{k(a),l(!0)},t=async()=>{var a;if(n){B(!0);try{const c=r.filter(v=>v.plan_id!==n.plan_id),b={membership_settings:c};console.log("Deleting membership plan:",n.plan_name,"Remaining plans:",c.length);const p=await g.callRawAPI(u==="club"?"/v3/api/custom/courtmatchup/club/profile-edit":`/v3/api/custom/courtmatchup/admin/profile-edit/${(a=i==null?void 0:i.user)==null?void 0:a.id}`,b,"POST");g.setTable("activity_logs"),await g.callRestAPI({user_id:C,activity_type:le.club_ui,action_type:ne.DELETE,data:JSON.stringify({deleted_plan:n,remaining_plans:c.length}),club_id:m==null?void 0:m.id,description:`Deleted membership plan: ${n.plan_name}`},"POST"),p.error?M(_,p.message||"Failed to delete plan",3e3,"error"):(M(_,`Successfully deleted plan: ${n.plan_name}`,3e3,"success"),h()),l(!1),k(null)}catch(c){M(_,c.message||"Failed to delete plan",3e3,"error")}finally{B(!1)}}};return e.jsxs("div",{className:"flex flex-col gap-4 p-5",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-xl font-medium",children:"Membership settings"}),e.jsxs("button",{onClick:()=>{N("create"),d(!0)},className:"flex items-center gap-2 rounded-lg border bg-primaryBlue px-3 py-2 text-sm text-white ",children:[e.jsx("span",{children:"New plan"}),e.jsx(pe,{className:"h-4 w-4"})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"text-left text-sm text-gray-500",children:[e.jsx("th",{className:"pb-4",children:"Plan"}),e.jsx("th",{className:"pb-4",children:"Price"}),e.jsx("th",{className:"pb-4 text-center",children:"Court booking"}),e.jsx("th",{className:"pb-4 text-center",children:"Lessons"}),e.jsx("th",{className:"pb-4 text-center",children:"Clinics"}),e.jsx("th",{className:"pb-4 text-center",children:"Find a Buddy"}),e.jsx("th",{className:"pb-4 text-center",children:"My Groups"}),e.jsx("th",{className:"pb-4 text-center",children:"Sports Covered"}),e.jsx("th",{className:"pb-4 text-center",children:"Advanced Booking"}),e.jsx("th",{className:"pb-4 text-center",children:"Actions"})]})}),e.jsx("tbody",{children:r.length>0?r==null?void 0:r.map(a=>{var c,b;return e.jsxs("tr",{className:"overflow-hidden",children:[e.jsx("td",{className:"rounded-l-xl bg-white px-4 py-3 text-gray-600",children:a.plan_name}),e.jsx("td",{className:"bg-white px-4 py-3",children:ue(a.price)}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:a.allow_court?e.jsx($,{className:"h-5 w-5 text-green-500"}):e.jsx(D,{className:"h-5 w-5 text-gray-400"})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:a.allow_coach?e.jsx($,{className:"h-5 w-5 text-green-500"}):e.jsx(D,{className:"h-5 w-5 text-gray-400"})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:a.allow_clinic?e.jsx($,{className:"h-5 w-5 text-green-500"}):e.jsx(D,{className:"h-5 w-5 text-gray-400"})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:a.allow_buddy?e.jsx($,{className:"h-5 w-5 text-green-500"}):e.jsx(D,{className:"h-5 w-5 text-gray-400"})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:a.allow_groups?e.jsx($,{className:"h-5 w-5 text-green-500"}):e.jsx(D,{className:"h-5 w-5 text-gray-400"})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"text-xs text-gray-600",children:q(a.applicable_sports)})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"whitespace-nowrap text-gray-500",children:((c=a.advance_booking_enabled)==null?void 0:c.court)===!1?"Disabled":`${((b=a.advance_booking_days)==null?void 0:b.court)||10}d`})})}),e.jsx("td",{className:"rounded-r-xl bg-white px-4 py-3",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("button",{className:"flex items-center justify-center text-gray-500 transition-colors hover:text-gray-700",onClick:()=>{N("edit"),G(a)},title:"Edit plan",children:e.jsx(ie,{className:"h-4 w-4"})}),e.jsx("button",{className:"flex items-center justify-center text-red-500 transition-colors hover:text-red-700",onClick:()=>s(a),title:"Delete plan",children:e.jsx(ge,{className:"h-4 w-4"})})]})})]},a.name)}):e.jsx("tr",{className:"text-center text-sm text-gray-500",children:e.jsx("td",{colSpan:"10",children:"No plans available"})})})]})}),e.jsx(be,{isOpen:j,onClose:f,title:"Plan details",onPrimaryAction:()=>{d(!1)},showFooter:!1,children:e.jsx(ye,{initialData:F,mode:A,onSubmit:a=>R(a,A),onClose:f,onDelete:s})}),e.jsx(xe,{isOpen:I,onClose:()=>{l(!1),k(null)},title:"Delete Membership Plan",message:`Are you sure you want to delete the "${n==null?void 0:n.plan_name}" membership plan? This action cannot be undone and may affect existing members with this plan.`,onDelete:t,buttonText:"Delete Plan",loading:O,requireConfirmation:!0})]})}export{Se as M};
