import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as i,b}from"./vendor-851db8c1.js";import{X as C}from"./react-image-crop-1f5038af.js";import{d as m}from"./index-13fd629e.js";const N=2*1024*1024,k=({isOpen:h,onClose:n,image:x,onCropComplete:u})=>{const[p,g]=i.useState({unit:"%",width:100,aspect:1}),[s,f]=i.useState(null),o=b.useRef(null),w=i.useCallback(e=>{o.current=e},[]),j=i.useCallback(async()=>{if(!s||!o.current)return;const e=o.current,a=document.createElement("canvas"),l=a.getContext("2d");Math.max(e.naturalWidth,e.naturalHeight);const r=Math.min(s.width,s.height);return a.width=r,a.height=r,l.clearRect(0,0,a.width,a.height),l.drawImage(e,s.x*(e.naturalWidth/e.width),s.y*(e.naturalHeight/e.height),s.width*(e.naturalWidth/e.width),s.height*(e.naturalHeight/e.height),0,0,r,r),new Promise(y=>{const c=e.src.toLowerCase().includes(".png")||e.src.toLowerCase().includes("image/png");a.toBlob(d=>{if(d.size>N){alert("Cropped image exceeds 2MB limit. Please try a smaller selection or a different image.");return}y(d)},c?"image/png":"image/jpeg",c?void 0:.9)})},[s]),v=async()=>{const e=await j();e&&(u(e),n())};return h?t.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:t.jsxs("div",{className:"flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0",children:[t.jsx("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:t.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),t.jsxs("div",{className:"inline-block transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6 sm:align-middle",children:[t.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:"Crop Image"}),t.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Draw area you'd like to crop."})]}),t.jsxs("button",{onClick:n,className:"rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none",children:[t.jsx("span",{className:"sr-only",children:"Close"}),t.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})]})]}),t.jsx("div",{className:"mt-4 max-h-[60vh] overflow-auto",children:t.jsx(C,{crop:p,onChange:e=>g(e),onComplete:e=>f(e),aspect:1,className:"max-w-full",children:t.jsx("img",{ref:w,src:x,alt:"Crop me",className:"max-w-full"})})}),t.jsxs("div",{className:"mt-5 flex justify-end gap-3",children:[t.jsx(m,{onClick:n,className:"rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),t.jsx(m,{onClick:v,className:"rounded-md bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Save"})]})]})]})}):null},R=k;export{R as I};
