import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,b as Q,k as fe,f as ge}from"./vendor-851db8c1.js";import{M as pe,G as me,b as I,e as de,T as be,A as je,d as he,t as xe,u as ve,f as _e,h as Ne,i as we,R as Ce,F as Se,j as ke,k as Ee,l as Pe}from"./index-13fd629e.js";import{B as Te}from"./BackButton-11ba52b2.js";import{S as Ie}from"./StripeConnectionStatus-f94a65e0.js";import{u as Ae}from"./react-hook-form-687afde5.js";import{o as Le}from"./yup-2824f222.js";import{c as $e,a as Re}from"./yup-54691517.js";import"./index-02625b16.js";import{I as Fe}from"./ImageCropModal-9d3311e2.js";import{L as Me}from"./index.esm-3a36c7d6.js";import{b as ie}from"./index.esm-9c6194ba.js";import{M as ce}from"./react-tooltip-7a26650a.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-image-crop-1f5038af.js";const Be=new pe;function Oe(){const[u,A]=r.useState([{account_nickname:"",account_number:"",account_routing_number:"",account_type:"savings"}]);r.useState(!1),r.useState(null),r.useState({}),r.useState(!1),r.useState({account_nickname:"",account_number:"",account_routing_number:"",account_type:"savings"});const{dispatch:p}=Q.useContext(me),[R,y]=r.useState(!0),F=Q.useRef(null),m=i=>{console.log("Stripe connection status changed:",i)};r.useEffect(()=>{j()},[]);const j=async()=>{y(!0);try{const i=await Be.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET"),S=JSON.parse(i.account_details||"[]");S.length>0&&Array.isArray(S)&&A(S)}catch(i){I(p,i.message,3e3,"error")}finally{y(!1)}};return e.jsxs("div",{className:"mx-auto flex w-full flex-col bg-white px-4 pb-7",children:[R&&e.jsx(de,{}),e.jsxs("div",{className:"mx-auto flex w-full max-w-2xl flex-col justify-center",children:[e.jsx(Ie,{ref:F,onConnectionStatusChange:m,successMessage:"You can now pay staffs and coaches from your club.",noConnectionMessage:"Connect your Stripe account to pay your club staffs and coaches. This is required for processing payments on the platform."}),e.jsx("div",{className:"flex w-full flex-col self-center",children:e.jsx("p",{className:"mt-6 text-xs leading-4 text-neutral-400",children:"Note: Multiple accounts can be set up if you want to change accounts later on a particular month or year."})})]})]})}let oe=new pe,Ue=new be;const De=()=>{const u=$e({email:Re().email().required()}).required(),{dispatch:A}=Q.useContext(je),[p,R]=r.useState("");Q.useState({});const[y,F]=r.useState("");r.useState(!1);const[m,j]=r.useState(!1),[i,S]=r.useState({}),[k,M]=r.useState(!0),[ae,E]=r.useState(null),[B,O]=r.useState(""),{dispatch:P}=Q.useContext(me),[ne,q]=r.useState(!1),[x,J]=r.useState(null),[f,Z]=r.useState(!1),[K,Y]=r.useState(null),{register:L,handleSubmit:V,setError:s,setValue:d,formState:{errors:v}}=Ae({resolver:Le(u)}),$=localStorage.getItem("user");async function _(){var n;M(!0);try{const o=await Ue.getList("profile",{filter:[`user_id,eq,${$}`],join:["user|user_id"]}),l=(n=o==null?void 0:o.list)==null?void 0:n[0];if(l){const c=l.user||{},D=l.id,U={...l,...c,profile_id:D,user_id:c.id};S(U),d("email",c==null?void 0:c.email),d("first_name",c==null?void 0:c.first_name),d("last_name",c==null?void 0:c.last_name),d("phone",c==null?void 0:c.phone),d("bio",c==null?void 0:c.bio),R(c==null?void 0:c.email),F(c==null?void 0:c.photo),d("gender",l==null?void 0:l.gender),d("address",l==null?void 0:l.address),d("city",l==null?void 0:l.city),d("state",l==null?void 0:l.state),d("zip_code",l==null?void 0:l.zip_code),A({type:"UPDATE_PROFILE",payload:U}),M(!1)}}catch(o){xe(A,o.response.data.message?o.response.data.message:o.message)}}const X=["email","first_name","last_name","phone","bio","photo","alternative_phone","age_group","family_role","password","verify","status"],ee=["gender","address","city","state","zip_code","date_of_birth","country","house_no"];console.log("User Profile Data:",{user_id:i==null?void 0:i.user_id,profile_id:i==null?void 0:i.profile_id,defaultValues:i});const W=async(n,o)=>{try{j(!0);const l={[n]:o},c=X.includes(n),D=ee.includes(n);if(c){oe.setTable("user");const U=await oe.callRestAPI({id:i==null?void 0:i.user_id,...l},"PUT");U.error?N(U):(I(P,"Profile Updated",4e3),E(null),O(""),_())}else if(D){oe.setTable("profile");const U=await oe.callRestAPI({id:i==null?void 0:i.profile_id,...l},"PUT");U.error?N(U):(I(P,"Profile Updated",4e3),E(null),O(""),_())}else{I(P,"Unknown field type: "+n,4e3,"error"),j(!1);return}j(!1)}catch(l){j(!1),s(n,{type:"manual",message:l!=null&&l.message&&l==null?void 0:l.message}),xe(A,l!=null&&l.message&&l==null?void 0:l.message)}},N=n=>{if(n.validation){const o=Object.keys(n.validation);for(let l=0;l<o.length;l++){const c=o[l];s(c,{type:"manual",message:n.validation[c]})}}},g=n=>{try{if(n.size>2*1024*1024){I(P,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}Y(n.type);const o=new FileReader;o.onload=()=>{J(o.result),Z(!0)},o.readAsDataURL(n)}catch(o){I(P,o==null?void 0:o.message,3e3,"error"),console.log(o)}},G=async n=>{try{q(!0);const o=K==="image/png",l=new File([n],`cropped_profile.${o?"png":"jpg"}`,{type:o?"image/png":"image/jpeg"});let c=new FormData;c.append("file",l);let D=await oe.uploadImage(c);W("photo",D==null?void 0:D.url)}catch(o){I(P,o==null?void 0:o.message,3e3,"error"),console.log(o)}finally{q(!1)}},H=()=>{W("photo",null),S({...i,photo:null})};return Q.useEffect(()=>{_()},[]),e.jsxs("div",{className:"",children:[k||ne&&e.jsx(de,{}),e.jsx("div",{className:"",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile details"})}),e.jsx(Fe,{isOpen:f,onClose:()=>Z(!1),image:x,onCropComplete:G}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:y||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsx("div",{children:e.jsxs("div",{className:"flex flex-col items-start justify-between gap-2",children:[e.jsx("p",{className:" font-medium text-gray-700",children:"Profile Picture"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:H,disabled:!y,className:"rounded-xl border border-red-600 px-3 py-1.5  text-red-600 disabled:opacity-50",children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5  text-gray-700",children:["Change Photo",e.jsx("input",{type:"file",accept:"image/*",onChange:n=>g(n.target.files[0]),className:"hidden"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"})]})})]}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"gender",label:"Gender",type:"select",options:["male","female","other"]},{key:"email",label:"Email",note:"Your email is not shared with other users."},{key:"phone",label:"Phone"},{key:"address",label:"Address"},{key:"city",label:"City"},{key:"state",label:"State"},{key:"zip_code",label:"Zip Code"},{key:"bio",label:"Bio",type:"textarea"}].map(n=>e.jsx("div",{children:ae===n.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{className:" font-medium text-gray-700",children:n.label}),e.jsx("button",{onClick:()=>E(null),className:" text-primaryBlue underline hover:text-primaryBlue",children:"Cancel"})]}),n.type==="select"?e.jsxs("select",{value:B,onChange:o=>O(o.target.value),className:"mt-1 w-full rounded-md border border-gray-300 p-2",children:[e.jsxs("option",{value:"",children:["Select ",n.label.toLowerCase()]}),n.options.map(o=>e.jsx("option",{value:o,children:o.charAt(0).toUpperCase()+o.slice(1)},o))]}):n.type==="textarea"?e.jsx("textarea",{value:B,onChange:o=>O(o.target.value),rows:4,className:"mt-1 w-full rounded-md border border-gray-300 p-2"}):e.jsx("input",{type:"text",value:B,onChange:o=>O(o.target.value),className:"mt-1  w-full rounded-xl border border-gray-300 p-2"}),n.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:n.note}),e.jsx("div",{className:"mt-2",children:e.jsx(he,{loading:m,onClick:()=>W(n.key,B),className:"rounded-xl bg-primaryBlue px-4 py-2  font-medium text-white hover:bg-primaryBlue",children:"Save"})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:" font-medium text-gray-500",children:n.label}),e.jsx("button",{onClick:()=>{E(n.key),O((i==null?void 0:i[n.key])||"")},className:" text-primaryBlue hover:text-indigo-800",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:(i==null?void 0:i[n.key])||"--"}),n.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:n.note})]})},n.key))})]})]})})]})};let ze=new pe;const Ge=()=>{const[u,A]=r.useState({}),[p,R]=r.useState(""),[y,F]=r.useState(""),[m,j]=r.useState(""),[i,S]=r.useState("date"),[k,M]=r.useState("desc"),[ae,E]=r.useState([]),[B,O]=r.useState(!1),[P,ne]=r.useState(null),[q,x]=r.useState("all");ve();const J=s=>{A(d=>({...d,[s]:!d[s]}))},f=async()=>{O(!0);try{const s=await ze.callRawAPI("/v3/api/custom/courtmatchup/coach/bookings/billing/coach-invoices",{},"GET");ne(s),E(s.invoices||[])}catch(s){console.log(s)}finally{O(!1)}},K=[...ae.filter(s=>{var _,X,ee,W,N;const d=((_=s.user_first_name)==null?void 0:_.toLowerCase().includes(p.toLowerCase()))||((X=s.user_last_name)==null?void 0:X.toLowerCase().includes(p.toLowerCase()))||((ee=s.receipt_id)==null?void 0:ee.toLowerCase().includes(p.toLowerCase()))||((W=s.status)==null?void 0:W.toLowerCase().includes(p.toLowerCase())),v=q==="all"||((N=s.invoice_type)==null?void 0:N.toLowerCase())===q.toLowerCase(),$=(()=>{if(!y&&!m)return!0;if(!s.date)return!1;const g=new Date(s.date),G=y?new Date(y):null,H=m?new Date(m):null;return G&&H?g>=G&&g<=H:G?g>=G:H?g<=H:!0})();return d&&v&&$})].sort((s,d)=>{let v=0;if(i==="date"){const $=new Date(s.date||s.create_at),_=new Date(d.date||d.create_at);v=$.getTime()-_.getTime()}else if(i==="amount")v=(s.amount||0)-(d.amount||0);else if(i==="status")v=(s.status||"").localeCompare(d.status||"");else if(i==="customer"){const $=`${s.user_first_name||""} ${s.user_last_name||""}`.trim(),_=`${d.user_first_name||""} ${d.user_last_name||""}`.trim();v=$.localeCompare(_)}return k==="desc"?-v:v}),Y=()=>{i==="date"?(S("amount"),M("desc")):i==="amount"?(S("status"),M("asc")):i==="status"?(S("customer"),M("asc")):(S("date"),M(k==="desc"?"asc":"desc"))},L=(s,d="usd")=>new Intl.NumberFormat("en-US",{style:"currency",currency:d.toUpperCase()}).format(s),V=s=>s?new Date(s).toLocaleDateString("en-US",{month:"2-digit",day:"2-digit",year:"2-digit"}):"N/A";return r.useEffect(()=>{f()},[]),B?e.jsx("div",{className:"flex w-full items-center justify-center py-8",children:e.jsx("div",{className:"text-lg",children:"Loading invoices..."})}):e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"My invoices"}),P&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Total: ",P.total_invoices||0," invoices • Total Earnings: ",L(P.total_earnings||0)]})]})}),e.jsxs("div",{className:"mb-6 flex flex-col gap-3 sm:flex-row",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(_e,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("input",{type:"text",value:p,onChange:s=>R(s.target.value),placeholder:"Search invoices...",className:"w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex w-full gap-2 sm:w-auto",children:[e.jsx("input",{type:"date",value:y,onChange:s=>F(s.target.value),placeholder:"Start date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),e.jsx("input",{type:"date",value:m,onChange:s=>j(s.target.value),placeholder:"End date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),(y||m)&&e.jsx("button",{onClick:()=>{F(""),j("")},className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 hover:bg-gray-50 hover:text-gray-700",title:"Clear dates",children:"✕"})]}),e.jsxs("button",{onClick:Y,className:"flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:["By ",i," ",k==="desc"?"↓":"↑",e.jsx(Me,{className:"transform"})]})]}),e.jsx("div",{className:"space-y-4",children:K.length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"No invoices found."}):K.map(s=>{var d;return e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm",children:[e.jsxs("button",{onClick:()=>J(s.id),className:"flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ne,{className:`transform transition-transform ${u[s.id]?"":"-rotate-90"}`}),e.jsx("span",{className:"text-sm sm:text-base",children:s.invoice_type||s.type}),s.status&&e.jsx("span",{className:`rounded-full px-2 py-1 text-xs ${s.status==="completed"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:s.status})]}),e.jsxs("div",{className:"flex items-center justify-between gap-4 pl-6 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600",children:V(s.date)}),e.jsx("span",{className:"font-medium",children:L(s.amount,s.currency)})]})]}),u[s.id]&&e.jsxs("div",{className:"mt-4 space-y-3 border-t border-gray-200 p-4",children:[s.receipt_id&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Invoice Number"}),e.jsxs("span",{children:["#",s.receipt_id]})]}),s.user_first_name&&s.user_last_name&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Customer"}),e.jsxs("span",{children:[s.user_first_name," ",s.user_last_name]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Created on"}),e.jsx("span",{children:V(s.create_at)})]}),s.total_amount&&s.total_amount!==s.amount&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Total amount"}),e.jsx("span",{children:L(s.total_amount,s.currency)})]}),s.valid_until&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Period"}),e.jsxs("span",{children:[V(s.create_at)," -"," ",V(s.valid_until)]})]}),s.club_name&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Club"}),e.jsx("span",{children:s.club_name})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Payment method"}),e.jsx("span",{children:s.payment_method})]}),s.reservation_type&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Reservation type"}),e.jsx("span",{className:"capitalize",children:(d=we.find(v=>v.value===s.reservation_type))==null?void 0:d.label})]}),e.jsx("button",{className:"mt-2 rounded-lg border border-gray-200 px-4 py-2 text-sm hover:bg-gray-50",onClick:()=>{const v=`
                        <div style="padding: 20px; font-family: Arial, sans-serif;">
                          <h2>Invoice Receipt</h2>
                          <p><strong>Receipt ID:</strong> ${s.receipt_id||"N/A"}</p>
                          <p><strong>Amount:</strong> ${L(s.amount,s.currency)}</p>
                          <p><strong>Date:</strong> ${V(s.date)}</p>
                          <p><strong>Status:</strong> ${s.status}</p>
                          <p><strong>Customer:</strong> ${s.user_first_name} ${s.user_last_name}</p>
                          <p><strong>Payment Method:</strong> ${s.payment_method}</p>
                          ${s.club_name?`<p><strong>Club:</strong> ${s.club_name}</p>`:""}
                        </div>
                      `,$=window.open("","_blank");$.document.write(v),$.document.close(),$.print()},children:"Print receipt"})]})]},s.id)})})]})},T=new pe,He=()=>{var G,H,n,o,l,c,D,U,ue;const[u,A]=r.useState({sport_id:"",type:"",sub_type:"",price:""}),[p,R]=r.useState({}),[y,F]=r.useState(!1),[m,j]=r.useState(!1),[i,S]=r.useState(!0),{dispatch:k}=r.useContext(me),[M,ae]=r.useState([]),[E,B]=r.useState(null),[O,P]=r.useState(!1),[ne,q]=r.useState(null),[x,J]=r.useState({sport_id:"",type:"",sub_type:"",price:""}),[f,Z]=r.useState({}),[K,Y]=r.useState(!1),L=(M==null?void 0:M.filter(t=>t.status===1))||[];r.useEffect(()=>{(async()=>{S(!0);try{const a=await T.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");B(a);const z=await T.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${a.club_id}`,{},"GET");ae(z.sports)}catch(a){console.error("Error fetching data:",a),I(k,a.message,3e3,"error")}finally{S(!1)}})()},[k]);const V=t=>{const a={};return t.sport_id||(a.sport_id="Sport is required"),t.price||(a.price="Price is required"),t.price&&Number(t.price)<=0&&(a.price="Price must be greater than 0"),a},s=t=>{const{name:a,value:z}=t.target;A(w=>{const C={...w,[a]:z};return a==="sport_id"&&(C.type="",C.sub_type=""),a==="type"&&(C.sub_type=""),C}),R(w=>({...w,[a]:""}))},d=async()=>{var C,re,le;const t={...u},a=L.find(h=>h.id===parseInt(u.sport_id));if(!(((C=a==null?void 0:a.sport_types)==null?void 0:C.length)>0&&a.sport_types.some(h=>h.type&&h.type.trim()!=="")))t.type="",t.sub_type="";else if(t.type&&t.type!=="All"){const h=(re=a==null?void 0:a.sport_types)==null?void 0:re.find(te=>te.type===t.type);((le=h==null?void 0:h.subtype)==null?void 0:le.length)>0&&h.subtype.some(te=>te&&te.trim()!=="")||(t.sub_type="")}const w=V(t);if(Object.keys(w).length>0){R(w);return}j(!0);try{T.setTable("coach_sports"),await T.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",{sport_ids:[t]},"POST"),A({sport_id:"",type:"",sub_type:"",price:""});const h=await T.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");B(h),I(k,"Sport added successfully",3e3,"success")}catch(h){console.log(h),I(k,h.message,3e3,"error")}finally{j(!1)}},v=async t=>{F(!0);try{T.setTable("coach_sports"),await T.callRestAPI({id:t},"DELETE");const a=await T.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");B(a),I(k,"Sport deleted successfully",3e3,"success")}catch(a){console.log(a),I(k,a.message,3e3,"error")}finally{F(!1)}},$=t=>{q(t),J({id:t.id,sport_id:t.sport_id,type:t.type||"",sub_type:t.sub_type||"",price:t.price||""}),Z({}),P(!0)},_=t=>{const{name:a,value:z}=t.target;J(w=>{const C={...w,[a]:z};return a==="sport_id"&&(C.type="",C.sub_type=""),a==="type"&&(C.sub_type=""),C}),Z(w=>({...w,[a]:""}))},X=t=>{const a={};return t.sport_id||(a.sport_id="Sport is required"),t.price||(a.price="Price is required"),t.price&&Number(t.price)<=0&&(a.price="Price must be greater than 0"),a},ee=async()=>{var C,re,le,h;const t={...x},a=L.find(b=>b.id===parseInt(x.sport_id));if(!(((C=a==null?void 0:a.sport_types)==null?void 0:C.length)>0&&a.sport_types.some(b=>b.type&&b.type.trim()!=="")))t.type="",t.sub_type="";else if(t.type&&t.type!=="All"){const b=(re=a==null?void 0:a.sport_types)==null?void 0:re.find(se=>se.type===t.type);((le=b==null?void 0:b.subtype)==null?void 0:le.length)>0&&b.subtype.some(se=>se&&se.trim()!=="")||(t.sub_type="")}const w=X(t);if(Object.keys(w).length>0){Z(w);return}Y(!0);try{const b=(h=E==null?void 0:E.sports)==null?void 0:h.find(ye=>ye.id===x.id);b&&parseInt(x.sport_id)!==parseInt(b.sport_id)?(T.setTable("coach_sports"),await T.callRestAPI({id:x.id},"DELETE"),await T.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",{sport_ids:[{sport_id:t.sport_id,type:t.type,sub_type:t.sub_type,price:t.price}]},"POST")):(T.setTable("coach_sports"),await T.callRestAPI({id:x.id,sport_id:t.sport_id,type:t.type,sub_type:t.sub_type,price:t.price},"PUT"));const se=await T.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");B(se),P(!1),I(k,"Sport updated successfully",3e3,"success")}catch(b){console.log(b),I(k,b.message,3e3,"error")}finally{Y(!1)}},W=()=>{P(!1),q(null),J({sport_id:"",type:"",sub_type:"",price:""}),Z({})},N=L.find(t=>t.id===parseInt(u.sport_id)),g=L.find(t=>t.id===parseInt(x.sport_id));return i?e.jsx(de,{}):e.jsxs("div",{className:"w-full",children:[y&&e.jsx(de,{}),e.jsx("h1",{className:"mb-6 text-xl font-semibold text-gray-900",children:"Sports you offer"}),e.jsxs("div",{className:"space-y-6",children:[((G=E==null?void 0:E.sports)==null?void 0:G.length)>0?e.jsx("div",{className:"mt-6 rounded-xl border border-gray-200 p-4 shadow-sm",children:e.jsx("ul",{className:"divide-y divide-gray-200",children:E.sports.map((t,a)=>{var z;return e.jsxs("li",{className:"flex items-center justify-between py-3 transition-colors hover:bg-gray-50",children:[e.jsxs("div",{className:"grid grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Sport"}),e.jsx("span",{className:"font-medium",children:(z=L.find(w=>w.id===parseInt(t.sport_id)))==null?void 0:z.name})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Type"}),e.jsx("span",{className:"font-medium",children:t.type||"--"})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Sub-type"}),e.jsx("span",{className:"font-medium",children:t.sub_type||"--"})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Price"}),e.jsxs("span",{className:"font-medium",children:["$",t.price]})]})]}),e.jsxs("div",{className:"ml-4 flex gap-2",children:[e.jsx("button",{onClick:()=>$(t),className:"text-blue-500 hover:text-blue-700",title:"Edit sport",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.1667 2.5C14.3856 2.28113 14.6454 2.10752 14.9312 1.98906C15.2169 1.87061 15.5238 1.80957 15.8333 1.80957C16.1429 1.80957 16.4498 1.87061 16.7355 1.98906C17.0213 2.10752 17.2811 2.28113 17.5 2.5C17.7189 2.71887 17.8925 2.97869 18.0109 3.26445C18.1294 3.55021 18.1904 3.85714 18.1904 4.16667C18.1904 4.47619 18.1294 4.78312 18.0109 5.06888C17.8925 5.35464 17.7189 5.61446 17.5 5.83333L6.25 17.0833L1.66667 18.3333L2.91667 13.75L14.1667 2.5Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{onClick:()=>v(t.id),className:"text-red-500 hover:text-red-700",title:"Delete sport",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.33333 15C8.55435 15 8.76631 14.9122 8.92259 14.7559C9.07887 14.5996 9.16667 14.3877 9.16667 14.1667V9.16667C9.16667 8.94565 9.07887 8.73369 8.92259 8.57741C8.76631 8.42113 8.55435 8.33333 8.33333 8.33333C8.11232 8.33333 7.90036 8.42113 7.74408 8.57741C7.5878 8.73369 7.5 8.94565 7.5 9.16667V14.1667C7.5 14.3877 7.5878 14.5996 7.74408 14.7559C7.90036 14.9122 8.11232 15 8.33333 15ZM16.6667 4.16667H13.3333V3.33333C13.3333 2.8913 13.1577 2.46738 12.8452 2.15482C12.5326 1.84226 12.1087 1.66667 11.6667 1.66667H8.33333C7.8913 1.66667 7.46738 1.84226 7.15482 2.15482C6.84226 2.46738 6.66667 2.8913 6.66667 3.33333V4.16667H3.33333C3.11232 4.16667 2.90036 4.25446 2.74408 4.41074C2.5878 4.56702 2.5 4.77899 2.5 5C2.5 5.22101 2.5878 5.43298 2.74408 5.58926C2.90036 5.74554 3.11232 5.83333 3.33333 5.83333H4.16667V16.6667C4.16667 17.1087 4.34226 17.5326 4.65482 17.8452C4.96738 18.1577 5.3913 18.3333 5.83333 18.3333H14.1667C14.6087 18.3333 15.0326 18.1577 15.3452 17.8452C15.6577 17.5326 15.8333 17.1087 15.8333 16.6667V5.83333H16.6667C16.8877 5.83333 17.0996 5.74554 17.2559 5.58926C17.4122 5.43298 17.5 5.22101 17.5 5C17.5 4.77899 17.4122 4.56702 17.2559 4.41074C17.0996 4.25446 16.8877 4.16667 16.6667 4.16667ZM8.33333 3.33333H11.6667V4.16667H8.33333V3.33333ZM14.1667 16.6667H5.83333V5.83333H14.1667V16.6667ZM11.6667 15C11.8877 15 12.0996 14.9122 12.2559 14.7559C12.4122 14.5996 12.5 14.3877 12.5 14.1667V9.16667C12.5 8.94565 12.4122 8.73369 12.2559 8.57741C12.0996 8.42113 11.8877 8.33333 11.6667 8.33333C11.4457 8.33333 11.2337 8.42113 11.0774 8.57741C10.9211 8.73369 10.8333 8.94565 10.8333 9.16667V14.1667C10.8333 14.3877 10.9211 14.5996 11.0774 14.7559C11.2337 14.9122 11.4457 15 11.6667 15Z",fill:"currentColor"})})})]})]},a)})})}):e.jsx("div",{className:"mt-6 rounded-xl border border-gray-200 p-4 text-center shadow-sm",children:e.jsx("p",{className:"text-gray-500",children:"No sports added yet. Add your first sport below."})}),e.jsxs("div",{className:"rounded-xl border border-gray-200 p-4 shadow-sm",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold",children:"Add New Sport"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("select",{name:"sport_id",value:u.sport_id,onChange:s,className:`w-full rounded-lg border ${p.sport_id?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sport"}),L.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),p.sport_id&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:p.sport_id})]}),N&&e.jsxs(e.Fragment,{children:[((H=N.sport_types)==null?void 0:H.length)>0&&N.sport_types.some(t=>t.type&&t.type.trim()!=="")?e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Type (Optional)"}),e.jsx(ie,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"sport-type-tooltip",size:16}),e.jsx(ce,{id:"sport-type-tooltip",className:"z-50 max-w-xs rounded-xl bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Leave blank to set a general price for all types/subtypes of this sport. Select a specific type only if you want different pricing for different types. If you coach multiple types/subtypes, add them individually for better search visibility."})]}),e.jsxs("select",{name:"type",value:u.type,onChange:s,className:`w-full rounded-lg border ${p.type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"General Price (All Types)"}),N.sport_types.length>=2&&e.jsx("option",{value:"All",children:"All"}),N.sport_types.map((t,a)=>t.type&&t.type.trim()!==""&&e.jsx("option",{value:t.type,children:t.type},a))]}),p.type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:p.type})]}):null,u.type&&((n=N.sport_types)==null?void 0:n.length)>0&&N.sport_types.some(t=>t.type===u.type&&t.subtype&&t.subtype.length>0)&&e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Sub-type (Optional)"}),e.jsx(ie,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"sport-subtype-tooltip",size:16}),e.jsx(ce,{id:"sport-subtype-tooltip",className:"z-50 max-w-xs rounded-lg bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Leave blank to set a general price for the selected type. Select a specific subtype only if you want different pricing for different subtypes. If you coach multiple subtypes, add them individually for better search visibility."})]}),e.jsxs("select",{name:"sub_type",value:u.sub_type,onChange:s,className:`w-full rounded-lg border ${p.sub_type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sub-type"}),u.type==="All"?e.jsx("option",{value:"All",children:"All"}):e.jsxs(e.Fragment,{children:[((o=N.sport_types.find(t=>t.type===u.type))==null?void 0:o.subtype.length)>=2&&e.jsx("option",{value:"All",children:"All"}),(l=N.sport_types.find(t=>t.type===u.type))==null?void 0:l.subtype.filter(t=>t&&t.trim()!=="").map((t,a)=>e.jsx("option",{value:t,children:t},a))]})]}),p.sub_type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:p.sub_type})]}),e.jsxs("div",{children:[e.jsx("input",{type:"number",name:"price",value:u.price,onChange:s,placeholder:"Enter price",className:`w-full rounded-lg border ${p.price?"border-red-500":"border-gray-300"} p-2`}),p.price&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:p.price})]}),e.jsx(he,{type:"button",onClick:d,loading:m,className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80 disabled:opacity-50",children:m?"Adding Sport...":"Add Sport"})]})]})]})]}),e.jsx(Ce,{isOpen:O,onClose:W,title:"Edit Sport",onPrimaryAction:ee,submitting:K,primaryButtonText:K?"Updating...":"Update Sport",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Sport ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("select",{name:"sport_id",value:x.sport_id,onChange:_,className:`w-full rounded-lg border ${f.sport_id?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sport"}),L.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),f.sport_id&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:f.sport_id})]}),g&&e.jsxs(e.Fragment,{children:[((c=g.sport_types)==null?void 0:c.length)>0&&g.sport_types.some(t=>t.type&&t.type.trim()!=="")?e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Type (Optional)"}),e.jsx(ie,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"edit-sport-type-tooltip",size:16}),e.jsx(ce,{id:"edit-sport-type-tooltip",className:"z-50 max-w-xs rounded-xl bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Leave blank to set a general price for all types/subtypes of this sport. Select a specific type only if you want different pricing for different types. If you coach multiple types/subtypes, add them individually for better search visibility."})]}),e.jsxs("select",{name:"type",value:x.type,onChange:_,className:`w-full rounded-lg border ${f.type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"General Price (All Types)"}),g.sport_types.length>=2&&e.jsx("option",{value:"All",children:"All"}),g.sport_types.map((t,a)=>t.type&&t.type.trim()!==""&&e.jsx("option",{value:t.type,children:t.type},a))]}),f.type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:f.type})]}):null,x.type&&((D=g.sport_types)==null?void 0:D.length)>0&&g.sport_types.some(t=>t.type===x.type&&t.subtype&&t.subtype.length>0)&&e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Sub-type"}),e.jsx(ie,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"edit-sport-subtype-tooltip",size:16}),e.jsx(ce,{id:"edit-sport-subtype-tooltip",className:"z-50 max-w-xs rounded-lg bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Unless you intend to charge different rates for different sport types or subtypes, select 'All' for both. If you play a sport across multiple types/subtypes, ensure all are added individually to appear in relevant searches."})]}),e.jsxs("select",{name:"sub_type",value:x.sub_type,onChange:_,className:`w-full rounded-lg border ${f.sub_type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sub-type"}),x.type==="All"?e.jsx("option",{value:"All",children:"All"}):e.jsxs(e.Fragment,{children:[((U=g.sport_types.find(t=>t.type===x.type))==null?void 0:U.subtype.length)>=2&&e.jsx("option",{value:"All",children:"All"}),(ue=g.sport_types.find(t=>t.type===x.type))==null?void 0:ue.subtype.filter(t=>t&&t.trim()!=="").map((t,a)=>e.jsx("option",{value:t,children:t},a))]})]}),f.sub_type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:f.sub_type})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Price ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("input",{type:"number",name:"price",value:x.price,onChange:_,placeholder:"Enter price",className:`w-full rounded-lg border ${f.price?"border-red-500":"border-gray-300"} p-2`}),f.price&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:f.price})]})]})]})})]})},Rt=()=>{const{dispatch:u}=Q.useContext(me),[A]=fe(),[p,R]=r.useState("profile"),y=ge();r.useEffect(()=>{const m=A.get("tab");m&&R({"payment-methods":"payment-methods",profile:"profile",sports:"sports",membership:"membership",billing:"billing",invoices:"invoices"}[m]||"profile")},[A.get("tab")]);const F=[{label:"Profile details",value:"profile",icon:Se},{label:"Sports",value:"sports",icon:ke},{label:"Bank Accounts",value:"payment-methods",icon:Ee},{label:"Invoices",value:"invoices",icon:Pe}];return r.useEffect(()=>{u({type:"SETPATH",payload:{path:"profile"}})},[]),e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"mx-auto  max-w-6xl px-4",children:[e.jsx(Te,{onBack:()=>y("/coach/dashboard")}),e.jsxs("div",{className:"flex flex-col gap-8 md:flex-row",children:[e.jsx("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-sm md:max-w-xs",children:e.jsx("nav",{className:"no-scrollbar flex w-full flex-row overflow-x-auto p-4 md:flex-col",children:F.map(m=>{const j=m.icon;return e.jsxs("button",{onClick:()=>{R(m.value),y(`/coach/profile?tab=${m.value}`)},className:`mr-2 flex min-w-fit items-center whitespace-nowrap rounded-lg px-4 py-3 transition-colors md:mb-2 md:mr-0 ${p===m.value?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50"}`,children:[e.jsx(j,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{className:"text-sm",children:m.label})]},m.value)})})}),e.jsxs("div",{className:" w-full rounded-xl bg-white p-6 shadow-sm",children:[p==="profile"&&e.jsx(De,{}),p==="sports"&&e.jsx(He,{}),p==="payment-methods"&&e.jsx(Oe,{}),p==="invoices"&&e.jsx(Ge,{})]})]})]})})};export{Rt as default};
