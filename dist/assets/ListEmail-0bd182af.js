import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as a}from"./vendor-851db8c1.js";import{M as X,G as Y,A as ee,c as se,_ as te,R as ae,t as le,b as u}from"./index-13fd629e.js";import{P as re}from"./index-eb1bc208.js";import oe from"./Skeleton-1e8bf077.js";import{E as ne}from"./EmailTemplateDrawer-1af620d6.js";import{H as k}from"./HistoryComponent-0ba6cfc0.js";import{D as ie}from"./DataTable-8a547681.js";let r=new X;const ce=[{header:"Subject",accessor:"subject",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"",accessor:"actions"}],me=()=>{const{dispatch:m}=a.useContext(Y),{dispatch:D}=a.useContext(ee),[p,x]=a.useState([]),[n,v]=a.useState(10),[L,A]=a.useState(0),[g,I]=a.useState(0),[R,O]=a.useState(!1),[H,B]=a.useState(!1),[C,b]=a.useState(!0),[G,N]=a.useState(!1),[V,y]=a.useState(null),[_,q]=a.useState([]),[z,f]=a.useState(!1),[t,d]=a.useState(null),[U,E]=a.useState(!1),[W,j]=a.useState(!1),[$,P]=a.useState(!1);function F(){i(g-1,n)}function K(){i(g+1,n)}async function i(s,l,o={},h=[]){b(!0);try{r.setTable("email");const c=await r.callRestAPI({payload:{...o},page:s,limit:l,filter:h},"PAGINATE");c&&b(!1);const{list:w,limit:Q,num_pages:T,page:S}=c;x(w),v(Q),A(T),I(S),O(S>1),B(S+1<=T)}catch(c){b(!1),console.log("ERROR",c),le(D,c.message)}}const Z=async()=>{try{r.setTable("user");const s=await r.callRestAPI({filter:["role,cs,user"]},"GETALL");r.setTable("profile");const l=await r.callRestAPI({},"GETALL"),o=s.list.map(h=>{const c=l.list.find(w=>w.user_id===h.id);return{...h,...c}});q(o)}catch(s){return console.error("Error fetching users:",s),[]}finally{}},M=async s=>{s.preventDefault(),E(!0);try{if(t!=null&&t.id)r.setTable("email"),(await r.callRestAPI({subject:t.subject,html:t.html,id:t.id},"PUT")).error||(u(m,"Email template updated successfully!",3e3,"success"),x(p.map(o=>o.id===t.id?{...o,...t}:o)));else{r.setTable("email");const l=await r.callRestAPI({subject:t.subject,html:t.html},"POST");l.error||(u(m,"Email template created successfully!",3e3,"success"),x([{...t,id:l.data},...p]))}f(!1),d(null)}catch(l){console.error(l),u(m,"Error creating email template",3e3,"error")}finally{E(!1)}},J=async s=>{P(!0);try{r.setTable("email"),(await r.callRestAPI({id:s},"DELETE")).error||(u(m,"Email template deleted successfully!",3e3,"success"),x(p.filter(o=>o.id!==s)))}catch(l){console.error(l),u(m,"An error occurred while deleting the template.",3e3,"error")}finally{P(!1),setShowDeleteModal(!1),y(null),j(!1)}};return a.useEffect(()=>{m({type:"SETPATH",payload:{path:"email"}}),Z();const l=setTimeout(async()=>{await i(1,n)},700);return()=>{clearTimeout(l)}},[]),e.jsxs("div",{className:"h-screen px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"flex flex-col justify-between gap-4 py-3 sm:gap-6 md:flex-row md:items-center",children:[e.jsxs("div",{className:"relative flex w-full max-w-none flex-1 items-center justify-between sm:max-w-[300px] md:max-w-[350px] lg:max-w-[400px]",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(se,{className:"h-4 w-4 text-gray-500 sm:h-5 sm:w-5"})}),e.jsx("input",{type:"text",className:"block w-full rounded-xl border border-gray-200 py-2 pl-10 pr-4 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:py-2.5 sm:pl-11",placeholder:"Search email subject",onChange:s=>{s.target.value?i(0,n,{},[`subject,cs,${s.target.value}`]):i(0,n)}})]}),e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4",children:[e.jsxs("button",{onClick:()=>N(!0),className:"inline-flex items-center justify-center gap-2 rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 sm:px-4",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",children:e.jsx("path",{d:"M4.99919 10.0017H7.70752M4.99919 10.0017L2.81729 3.45596C2.6999 3.1038 3.06689 2.78551 3.39891 2.95152L16.7538 9.62898C17.0609 9.78253 17.0609 10.2208 16.7538 10.3743L3.39891 17.0518C3.06689 17.2178 2.6999 16.8995 2.81729 16.5474L4.99919 10.0017Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"hidden sm:inline",children:"Send custom email"}),e.jsx("span",{className:"sm:hidden",children:"Send email"})]}),e.jsx("div",{className:"hidden sm:block",children:e.jsx(k,{title:"Email History",emptyMessage:"No email history found"})}),e.jsxs("button",{onClick:()=>{d(null),f(!0)},className:"inline-flex items-center justify-center gap-2 rounded-xl bg-[#1D275F] px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 sm:px-4",children:[e.jsx("span",{className:"text-sm",children:"+"}),e.jsx("span",{className:"hidden sm:inline",children:"Add new"}),e.jsx("span",{className:"sm:hidden",children:"Add"})]}),e.jsx("div",{className:"sm:hidden",children:e.jsx(k,{title:"Email History",emptyMessage:"No email history found"})})]})]}),C?e.jsx(oe,{}):e.jsx(ie,{columns:ce,data:p,loading:C,rowClassName:"hover:bg-gray-50 bg-gray-100 px-4 py-3 cursor-pointer",onClick:s=>{d(s),f(!0)},renderCustomCell:{actions:s=>e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:l=>{l.stopPropagation(),y(s.id),j(!0)},className:"rounded-lg p-2 text-red-600 transition-colors duration-150 hover:bg-red-50 hover:text-red-800",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 sm:h-5 sm:w-5",children:e.jsx("path",{d:"M6.79167 6.79297V18.8763C6.79167 19.3365 7.16476 19.7096 7.625 19.7096H16.375C16.8352 19.7096 17.2083 19.3365 17.2083 18.8763V6.79297M6.79167 6.79297H17.2083M6.79167 6.79297H5.125M17.2083 6.79297H18.875M13.6667 10.9596V15.543M10.3333 10.9596V15.543M9.5 6.79297C9.5 5.41226 10.6193 4.29297 12 4.29297C13.3807 4.29297 14.5 5.41226 14.5 6.79297",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})})},emptyMessage:"No email templates available",loadingMessage:"Loading email templates..."}),e.jsx(re,{currentPage:g,pageCount:L,pageSize:n,canPreviousPage:R,canNextPage:H,updatePageSize:s=>{v(s),i(1,s)},previousPage:F,nextPage:K,gotoPage:s=>i(s,n)}),e.jsx(ne,{isOpen:G,onClose:()=>N(!1),members:_}),e.jsx(te,{isOpen:W,onClose:()=>{j(!1),y(null)},onDelete:()=>J(V),loading:$}),e.jsx(ae,{isOpen:z,onClose:()=>f(!1),title:t?"Edit Email Template":"Add New Email Template",primaryButtonText:t?"Update Template":"Create Template",onPrimaryAction:M,submitting:U,children:e.jsxs("form",{onSubmit:M,className:"space-y-4 py-4 sm:space-y-5",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Subject"}),e.jsx("input",{type:"text",name:"subject",value:(t==null?void 0:t.subject)||"",onChange:s=>d({...t,subject:s.target.value}),className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Message"}),e.jsx("textarea",{name:"message",value:(t==null?void 0:t.html)||"",onChange:s=>d({...t,html:s.target.value}),rows:6,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]})})]})},ye=me;export{ye as L};
