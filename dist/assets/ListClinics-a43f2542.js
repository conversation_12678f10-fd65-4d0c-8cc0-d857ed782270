import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as i,b as p,f as Es}from"./vendor-851db8c1.js";import{M as Ze,aC as Ms,d as Qe,az as As,G as hs,A as gs,u as Is,R as Ps,P as ls,af as Ie,ad as is,e as xs,b as C,aD as Ds,ao as Bs,T as $s,c as Ls,X as ds,_ as Os,t as cs}from"./index-13fd629e.js";import{c as Hs,a as Le}from"./yup-54691517.js";import{u as ys}from"./react-hook-form-687afde5.js";import{o as Rs}from"./yup-2824f222.js";import{P as Fs}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import zs from"./Skeleton-1e8bf077.js";import{S as Ae}from"./react-select-c8303602.js";import{C as Js}from"./CategoryTagManager-905b992b.js";import{H as us}from"./HistoryComponent-0ba6cfc0.js";import{D as Ws}from"./DataTable-8a547681.js";import{f as ms}from"./date-fns-cca0f4f7.js";const ps=new Ze,Us=({onClose:n,onConfirm:H,clinic:j,formData:R,eventCounts:w})=>{const[a,r]=i.useState(""),[d,g]=i.useState(w||{total:0,completed:0,upcoming:"No upcoming events",lastEvent:"No events scheduled"}),[Z,m]=i.useState(!1),[D,fe]=i.useState(!1),le={0:"no_changes",1:"cancel_future",2:"update_future_only",3:"update_all_events",4:"clone_clinic"},B=async()=>{if(!(!j||!j.sport_id||!j.type||!j.sub_type)){m(!0);try{const c=await ps.callRawAPI("/v3/api/custom/courtmatchup/club/courts/affected-reservations",{sport:j.sport_id,type:j.type,subtype:j.sub_type},"POST");c&&!c.error&&g({total:c.total_affected||0,completed:c.completed_events||0,upcoming:c.upcoming_events>0?`${c.upcoming_events} upcoming events`:"No upcoming events",lastEvent:c.last_event_date?new Date(c.last_event_date).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"}):"No events scheduled"})}catch(c){console.error("Error fetching affected reservations stats:",c),g({total:0,completed:0,upcoming:"No upcoming events",lastEvent:"No events scheduled"})}finally{m(!1)}}};i.useEffect(()=>{!w&&j&&B()},[j,w]);const be=async()=>{if(!(!a||!(j!=null&&j.id)||!R)){fe(!0);try{const c={...R,sport_change_option:parseInt(a),sport_change_action:le[parseInt(a)]};console.log("Updating clinic with payload:",c);const T=await ps.callRawAPI(`/v3/api/custom/courtmatchup/club/update-clinic-data/${j.id}`,c,"POST");if(T&&!T.error)console.log("Clinic updated successfully:",T),H&&H(a,T);else throw console.error("API returned error:",T),new Error((T==null?void 0:T.message)||"Failed to update clinic")}catch(c){console.error("Error updating clinic:",c)}finally{fe(!1)}}};return e.jsxs("div",{className:"fixed inset-0 z-[99999] flex items-center justify-center ",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-2xl rounded-lg bg-white p-6",children:[e.jsxs("div",{className:"flex items-center justify-between border-b pb-4",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Confirm Updates"}),e.jsx("button",{onClick:n,className:"text-gray-500 hover:text-gray-700",children:e.jsx(Ms,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"mb-4 text-base",children:"You're changing the Sport / Type / Subtype for this clinic. This affects how the clinic connects to scheduled events; time slots, availability, and visibility in the scheduler."}),e.jsx("p",{className:"mb-2 font-medium",children:"Below are the scheduled events currently tied to this clinic:"}),Z?e.jsxs("div",{className:"mb-6 ml-6 space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-4 w-32 animate-pulse rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-8 animate-pulse rounded bg-gray-200"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-4 w-28 animate-pulse rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-8 animate-pulse rounded bg-gray-200"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-4 w-36 animate-pulse rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-20 animate-pulse rounded bg-gray-200"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-4 w-32 animate-pulse rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-20 animate-pulse rounded bg-gray-200"})]})]}):e.jsxs("div",{className:"mb-6 ml-6",children:[e.jsxs("p",{children:["Total Scheduled Events: ",d.total]}),e.jsxs("p",{children:["Completed Events: ",d.completed]}),e.jsxs("p",{children:["Upcoming Events: ",d.upcoming]}),e.jsxs("p",{children:["Last Event Date: ",d.lastEvent]})]}),e.jsx("p",{className:"mb-4 font-medium",children:"Choose how to handle scheduled events:"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option1",name:"sportChangeOption",value:"1",checked:a==="1",onChange:c=>r(c.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option1",className:"font-medium",children:"Cancel All Future Events"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Delete all upcoming events for this clinic. Past events will remain unchanged."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option2",name:"sportChangeOption",value:"2",checked:a==="2",onChange:c=>r(c.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option2",className:"font-medium",children:"Apply Changes Only to Future Events"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Keep past events intact: Update sport/type/subtype on upcoming events only."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option3",name:"sportChangeOption",value:"3",checked:a==="3",onChange:c=>r(c.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option3",className:"font-medium",children:"Apply Changes to All Events (Past and Future)"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Retroactively apply sport/type/subtype changes to all events connected to this clinic, including completed ones."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option4",name:"sportChangeOption",value:"4",checked:a==="4",onChange:c=>r(c.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option4",className:"font-medium",children:"Clone This Clinic and Keep Existing Schedule"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Keep the current events tied to this clinic, and create a new clinic with your changes that you can schedule separately."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option0",name:"sportChangeOption",value:"0",checked:a==="0",onChange:c=>r(c.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option0",className:"font-medium",children:"Don't Apply Sport/Type/Subtype Changes"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Save all other updates to this clinic, but leave the original sport, type, and subtype unchanged."})]})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t pt-4",children:[e.jsx("button",{onClick:n,className:"rounded-lg border border-gray-300 bg-white px-6 py-2 text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx(Qe,{onClick:be,className:"rounded-lg bg-blue-500 px-6 py-2 text-white hover:bg-blue-600",disabled:!a||D,loading:D,children:D?"Saving Changes...":"Confirm and Save Changes"})]})]})]})},oe=As();let E=new Ze;const Vs=({clinic:n,onClose:H,getData:j,isOpen:R,sports:w})=>{var ts,as,rs;const a=n||{sport_id:"",type:"",sub_type:"",date:"",end_date:"",start_time:"",end_time:"",name:"",cost_per_head:"",description:"",cancellation_policy_days:1,recurring:0,id:null},{setValue:r,watch:d}=ys({defaultValues:{sport_id:a.sport_id,type:a.type,sub_type:a.sub_type,date:a.date,end_date:a.end_date,start_time:a.start_time,end_time:a.end_time,name:a.name,cost_per_head:a.cost_per_head,description:a.description,cancellation_policy_days:a.cancellation_policy_days,recurring:a.recurring}}),[g,Z]=i.useState(!1),[m,D]=i.useState(!1),[fe,le]=i.useState(!1),[B,be]=i.useState([]),[c,T]=i.useState([]),[es,Oe]=i.useState([]),[He,Re]=i.useState([]),[ie,U]=i.useState(!1),[ve,de]=i.useState(!1),[h,Fe]=i.useState(null),[Pe,je]=i.useState(!1),[Ne,_e]=i.useState([]),[F,Se]=i.useState(!1),[ze,De]=i.useState(!1),[k,ce]=i.useState([]),[M,Ce]=i.useState(0),[Je,we]=i.useState(!1),[V,Te]=i.useState({sport_id:"",type:"",sub_type:""}),[$,y]=i.useState(null),[ue,me]=i.useState([]),[ee,pe]=i.useState([]),[he,ge]=i.useState([]),[t,b]=i.useState([]),[We,ke]=i.useState([]),[se,Be]=i.useState(oe),te=d("sport_id"),ae=d("type"),Y=d("start_time"),{dispatch:v}=i.useContext(hs),{dispatch:fs}=i.useContext(gs),{club:_}=Is();i.useEffect(()=>{if(n){r("sport_id",n.sport_id||""),r("type",n.type||""),r("sub_type",n.sub_type||""),r("date",n.date||""),r("end_date",n.end_date||""),r("start_time",n.start_time||""),r("end_time",n.end_time||""),r("name",n.name||""),r("cost_per_head",n.cost_per_head||""),r("description",n.description||""),r("cancellation_policy_days",n.cancellation_policy_days||1),r("recurring",n.recurring||0),Te({sport_id:n.sport_id||"",type:n.type||"",sub_type:n.sub_type||""});try{if(n.categories){const s=JSON.parse(n.categories);me(Array.isArray(s)?s:[])}else me([]);if(n.subcategories){const s=JSON.parse(n.subcategories);pe(Array.isArray(s)?s:[])}else pe([]);if(n.tags){const s=JSON.parse(n.tags);ge(Array.isArray(s)?s:[])}else ge([])}catch(s){console.error("Error parsing clinic categories/tags:",s),me([]),pe([]),ge([])}}},[n,r]);const ss=async()=>{if(!a.id)return[];le(!0);try{E.setTable("clinic_coaches");const s=await E.callRestAPI({filter:[`clinic_id,eq,${a.id}`]},"GETALL"),l=(await Bs(v,fs,"coach",(s==null?void 0:s.list.map(f=>f.coach_id))||[],"user|user_id")).list.map(f=>{var re,q;const L=s.list.find(S=>S.coach_id===f.id),K=L?JSON.parse(L.data||"{}"):{},A=K.working_hours||[];console.log("Coach working hours data:",{coachId:f.id,coachName:`${(re=f.user)==null?void 0:re.first_name} ${(q=f.user)==null?void 0:q.last_name}`,workingHours:A,coachData:K});const xe=A.map(S=>{let z=S;typeof S!="string"&&(z=String(S)),z=z.split(":").slice(0,2).join(":");const J=z.split(":");if(J.length!==2)return console.warn("Invalid time format:",S),S;const W=parseInt(J[0])||0,G=parseInt(J[1])||0,u=W>=12?"PM":"AM";return`${W%12||12}:${G.toString().padStart(2,"0")} ${u}`});return{...f,hours:xe,clinicCoachId:L==null?void 0:L.id,clinicCoachData:K}});be(l),Oe(s.list||[]),Re(l)}catch(s){return console.log(s),[]}finally{le(!1)}},Ue=async()=>{if(console.log("getAllCoaches called, club:",_),!(_!=null&&_.id)){console.log("No club ID available");return}Se(!0);try{console.log("Fetching coaches for club ID:",_.id),E.setTable("coach");const s=await E.callRestAPI({filter:[`courtmatchup_coach.club_id,eq,${_.id}`],join:["user|user_id"]},"GETALL");console.log("Coaches API response:",s),T(s.list||[])}catch(s){console.log("Error fetching all coaches:",s)}finally{Se(!1)}};i.useEffect(()=>{n!=null&&n.id&&ss()},[n==null?void 0:n.id]),i.useEffect(()=>{_!=null&&_.id&&Ue()},[_==null?void 0:_.id]),i.useEffect(()=>{k.length>0&&!ve&&M<k.length&&(console.log("Starting hours selection for coaches:",k,"Current index:",M),js())},[k.length,M,ve]),i.useEffect(()=>{R||$e()},[R]),i.useEffect(()=>{var s;if(te){const o=w.find(l=>l.id.toString()===te.toString());if(o){const l=((s=o.sport_types)==null?void 0:s.filter(f=>f.type!==""))||[];b(l)}else b([])}else b([])},[te,w]),i.useEffect(()=>{if(ae){const s=t.find(o=>o.type===ae);if(s){const o=(s.subtype||[]).filter(l=>l!=="");ke(o)}else ke([])}else ke([])},[ae,t]);const bs=s=>{if(!s)return oe;const o=oe.findIndex(l=>l.value===s);return o===-1?oe:oe.filter((l,f)=>f>o)};i.useEffect(()=>{if(Y){const s=bs(Y);Be(s)}else Be(oe)},[Y]);const Ee=async()=>{await ss()},$e=()=>{ce([]),Ce(0),_e([]),U(!1),de(!1)},vs=async(s,o)=>{if(console.log("handleAddCoachWithHours called with:",s,o),!s||!s.id){console.error("Invalid coach object:",s),C(v,"Invalid coach data",3e3,"error");return}De(!0);try{E.setTable("clinic_coaches");const l={clinic_id:a.id,coach_id:s.id,data:JSON.stringify({working_hours:o,fees:a.cost_per_head||0,sport_id:a.sport_id,type:a.type||"",sub_type:a.sub_type||""})};return(await E.callRestAPI(l,"POST")).error?!1:(C(v,"Coach added successfully",3e3,"success"),await Ee(),!0)}catch(l){return C(v,l==null?void 0:l.message,3e3,"error"),console.log(l),!1}finally{De(!1)}},js=async()=>{if(k.length===0)return;const s=k[M];s&&(Fe({...s,hours:[]}),de(!0))},Ns=async s=>{const o=k[M];console.log("Completing hours for coach:",o,"Hours:",s),await vs(o,s)&&(M<k.length-1?(console.log("Moving to next coach, index:",M+1),Ce(M+1)):(console.log("All coaches processed successfully"),ce([]),Ce(0),C(v,"All coaches added successfully",3e3,"success")))},_s=async s=>{try{E.setTable("clinic_coaches"),await E.callRestAPI({id:s.clinicCoachId},"DELETE"),C(v,"Coach removed successfully",3e3,"success"),await Ee()}catch(o){C(v,o==null?void 0:o.message,3e3,"error"),console.log(o)}},Ss=async()=>{if(!a.id){C(v,"Cannot save: no clinic selected",3e3,"error");return}const s={id:a.id,name:d("name"),cost_per_head:parseFloat(d("cost_per_head")),description:d("description"),sport_id:d("sport_id"),type:d("type"),sub_type:d("sub_type"),date:d("date"),end_date:d("end_date")||null,start_time:d("start_time"),end_time:d("end_time"),cancellation_policy_days:d("cancellation_policy_days"),recurring:d("recurring")===1||d("recurring")===!0?1:0,categories:JSON.stringify(ue.map(l=>({id:l.id,name:l.name}))),subcategories:JSON.stringify(ee.map(l=>({id:l.id,name:l.name,category_id:l.category_id}))),tags:JSON.stringify(he.map(l=>({id:l.id,name:l.name})))};if(s.sport_id!==V.sport_id||s.type!==V.type||s.sub_type!==V.sub_type){we(!0),y(s);return}await Cs(s)},Cs=async s=>{Z(!0);try{console.log("API Payload being sent:",s),E.setTable("clinics");const o=await E.callRestAPI(s,"PUT");o!=null&&o.error||(C(v,"Clinic updated successfully",3e3,"success"),n&&Object.keys(s).forEach(l=>{l!=="id"&&(n[l]=s[l])}),Te({sport_id:s.sport_id,type:s.type,sub_type:s.sub_type}),D(!1),await Ee(),j())}catch(o){C(v,(o==null?void 0:o.message)||"An error occurred",3e3,"error"),console.log(o)}finally{Z(!1)}};i.useEffect(()=>{m&&(r("name",a.name||""),r("cost_per_head",a.cost_per_head||""),r("description",a.description||""),r("sport_id",a.sport_id||""),r("type",a.type||""),r("sub_type",a.sub_type||""),r("date",a.date||""),r("end_date",a.end_date||""),r("start_time",a.start_time||""),r("end_time",a.end_time||""),r("cancellation_policy_days",a.cancellation_policy_days||1),r("recurring",a.recurring))},[m,a,r]);const ws=()=>{var S,z,J,W,G;const[s,o]=i.useState((h==null?void 0:h.hours)||[]),l=u=>{if(!u)return 0;const[x,N]=u.split(":").map(Number);return x*60+N},f=u=>{if(!u)return"";const[x,N]=u.split(":").map(Number),I=x>=12?"PM":"AM";return`${x%12||12}:${N.toString().padStart(2,"0")} ${I}`},L=f(a.start_time),K=f(a.end_time),xe=((u,x)=>{const N=[],I=l(u),P=l(x);for(let O=I;O<P;O+=30){const X=Math.floor(O/60),ne=O%60,Q=`${X.toString().padStart(2,"0")}:${ne.toString().padStart(2,"0")}:00`;N.push({time24:Q,time12:f(Q)})}return N})(a.start_time,a.end_time),re=u=>{o(x=>x.includes(u.time12)?x.filter(N=>N!==u.time12):[...x,u.time12].sort((N,I)=>{const P=O=>{const[X,ne]=O.split(" "),[Q,Ve]=X.split(":").map(Number);let ye=Q;return ne==="PM"&&Q!==12&&(ye+=12),ne==="AM"&&Q===12&&(ye=0),ye*60+Ve};return P(N)-P(I)}))},q=async()=>{je(!0);try{const u=s.map(x=>{const[N,I]=x.split(" "),[P,O]=N.split(":").map(Number);let X=P;return I==="PM"&&P!==12&&(X+=12),I==="AM"&&P===12&&(X=0),`${X.toString().padStart(2,"0")}:${O.toString().padStart(2,"0")}`});if(h.clinicCoachId){const x=h.clinicCoachData||{};x.working_hours=u,E.setTable("clinic_coaches"),(await E.callRestAPI({id:h.clinicCoachId,data:JSON.stringify(x)},"PUT")).error||(C(v,"Coach hours updated successfully",3e3,"success"),await Ee(),de(!1))}else await Ns(u),de(!1)}catch(u){C(v,u==null?void 0:u.message,3e3,"error"),console.log(u)}finally{je(!1)}};return ve?e.jsx("div",{className:"fixed inset-0 z-[99999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-2xl rounded-lg bg-white shadow-xl",children:[e.jsxs("div",{className:"flex items-center justify-between border-b border-gray-200 p-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Set Coach Hours"}),k.length>1&&e.jsxs("p",{className:"text-sm text-gray-500",children:["Coach ",M+1," of"," ",k.length]})]}),e.jsx("button",{onClick:$e,className:"text-gray-400 hover:text-gray-600",children:e.jsx(is,{className:"h-6 w-6"})})]}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"flex flex-col space-y-6",children:[e.jsxs("div",{className:"flex items-center space-x-3 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 p-4",children:[e.jsx("div",{className:"h-12 w-12 overflow-hidden rounded-full shadow-sm ring-2 ring-white",children:e.jsx("img",{src:((S=h==null?void 0:h.user)==null?void 0:S.photo)||"/default-avatar.png",alt:`${(z=h==null?void 0:h.user)==null?void 0:z.first_name} ${(J=h==null?void 0:h.user)==null?void 0:J.last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-semibold text-gray-900",children:[(W=h==null?void 0:h.user)==null?void 0:W.first_name," ",(G=h==null?void 0:h.user)==null?void 0:G.last_name]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Clinic: ",L," - ",K]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Available Time Slots"}),e.jsx("div",{className:"grid grid-cols-3 gap-3",children:xe.map(u=>e.jsx("button",{onClick:()=>re(u),className:`w-full rounded-lg border-2 p-3 text-center font-medium transition-all duration-200 hover:scale-105
                        ${s.includes(u.time12)?"border-primaryBlue bg-primaryBlue text-white shadow-md":"border-gray-200 text-gray-700 hover:border-primaryBlue hover:bg-blue-50 hover:text-primaryBlue"}
                      `,children:u.time12},u.time12))})]})]})}),e.jsxs("div",{className:"flex justify-end space-x-3 border-t border-gray-200 p-6",children:[e.jsx("button",{onClick:$e,className:"rounded-lg border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 transition-colors hover:border-gray-400 hover:bg-gray-50",children:"Cancel"}),e.jsx(Qe,{loading:Pe,onClick:q,className:"rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md",children:Pe?"Saving...":"Save Hours"})]})]})}):null};if(!R)return null;const Me={control:s=>({...s,borderRadius:"0.5rem",border:"none",backgroundColor:"#f9fafb","&:hover":{border:"none",backgroundColor:"#f3f4f6"},"&:focus-within":{border:"none",boxShadow:"none",backgroundColor:"#f3f4f6"}}),option:(s,o)=>({...s,backgroundColor:o.isSelected?"#3b82f6":o.isFocused?"#f3f4f6":"white",color:o.isSelected?"white":"#374151","&:hover":{backgroundColor:o.isSelected?"#3b82f6":"#f3f4f6"}}),menu:s=>({...s,borderRadius:"0.5rem",boxShadow:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)"}),multiValue:s=>({...s,backgroundColor:"#e5e7eb",borderRadius:"0.375rem"}),multiValueLabel:s=>({...s,color:"#374151",padding:"0.25rem 0.5rem"}),multiValueRemove:s=>({...s,color:"#6b7280",borderRadius:"0 0.375rem 0.375rem 0","&:hover":{backgroundColor:"#d1d5db",color:"#374151"}})};return e.jsxs(e.Fragment,{children:[e.jsxs(Ps,{isOpen:R,onClose:H,title:a.name||"Clinic details",showFooter:!1,children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"rounded-lg border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:a.name||"Clinic Details"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[((ts=w.find(s=>s.id.toString()==a.sport_id))==null?void 0:ts.name)||"No sport selected",a.type&&` • ${a.type}`,a.sub_type&&` • ${a.sub_type}`]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-bold text-primaryBlue",children:ls(a.cost_per_head)}),e.jsx("div",{className:"text-sm text-gray-500",children:"per person"})]})]})}),e.jsx("div",{className:"flex justify-end border-b border-gray-200 pb-4",children:m?e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>D(!1),className:"rounded-lg border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 transition-colors hover:border-gray-400 hover:bg-gray-50",children:"Cancel"}),e.jsx(Qe,{loading:g,onClick:Ss,className:"rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md",children:g?"Saving...":"Save All Changes"})]}):e.jsx("button",{onClick:()=>D(!0),className:"rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),e.jsx("span",{children:"Edit Details"})]})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Basic Information"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Clinic Name"}),m?e.jsx("input",{type:"text",value:d("name")||"",onChange:s=>r("name",s.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",placeholder:"Enter clinic name"}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.name||"No name provided"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Cost per Person"}),m?e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("span",{className:"font-medium text-gray-500",children:"$"})}),e.jsx("input",{type:"number",value:d("cost_per_head")||"",onChange:s=>r("cost_per_head",s.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-8 pr-3 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",placeholder:"0.00",min:"0",step:"0.01"})]}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:ls(a.cost_per_head)})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Description"}),m?e.jsx("textarea",{value:d("description")||"",onChange:s=>r("description",s.target.value),className:"w-full resize-none rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",rows:3,placeholder:"Enter clinic description"}):e.jsx("div",{className:"min-h-[80px] rounded-lg bg-gray-50 px-3 py-2 text-gray-900",children:a.description||"No description provided"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Sport Configuration"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Sport"}),m?e.jsx(Ae,{className:"w-full text-sm",options:w.filter(s=>s.status===1).map(s=>({value:s.id.toString(),label:s.name})),value:{value:te,label:((as=w.find(s=>s.id.toString()==te))==null?void 0:as.name)||"Select sport"},onChange:s=>{r("sport_id",s.value),r("type",""),r("sub_type","")},styles:Me}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:((rs=w.find(s=>s.id.toString()==a.sport_id))==null?void 0:rs.name)||"No sport selected"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Type"}),m?e.jsx(e.Fragment,{children:t.length>0?e.jsx(Ae,{className:"w-full text-sm",options:t.map(s=>({value:s.type,label:s.type})),value:{value:ae,label:ae||"Select type"},onChange:s=>{r("type",s.value),r("sub_type","")},styles:Me}):e.jsx("div",{className:"rounded-lg bg-gray-100 px-3 py-2 text-sm italic text-gray-500",children:"This sport has no types"})}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.type||"No type selected"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Sub-type"}),m?e.jsx(e.Fragment,{children:We.length>0?e.jsx(Ae,{className:"w-full text-sm",options:We.map(s=>({value:s,label:s})),value:{value:d("sub_type"),label:d("sub_type")||"Select sub-type"},onChange:s=>{r("sub_type",s.value)},styles:Me}):e.jsx("div",{className:"rounded-lg bg-gray-100 px-3 py-2 text-sm italic text-gray-500",children:"This type has no sub-types"})}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.sub_type||"No sub-type selected"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Scheduling"})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Start Date"}),m?e.jsx("input",{type:"date",value:d("date")||"",onChange:s=>r("date",s.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.date?new Date(a.date).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):"No start date set"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"End Date"}),m?e.jsx("input",{type:"date",value:d("end_date")||"",onChange:s=>r("end_date",s.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",min:d("date")||void 0}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.end_date?new Date(a.end_date).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):"No end date set"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Start Time"}),m?e.jsx(Ae,{className:"w-full text-sm",options:oe,value:{value:d("start_time"),label:Ie(d("start_time"))||"Select time"},onChange:s=>{r("start_time",s.value)},placeholder:"Select start time",styles:Me}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:Ie(a.start_time)||"Not set"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"End Time"}),m?e.jsx(Ae,{className:"w-full text-sm",options:se,value:{value:d("end_time"),label:Ie(d("end_time"))||"Select time"},onChange:s=>{r("end_time",s.value)},placeholder:Y?"Select end time":"Select start time first",isDisabled:!Y,styles:Me}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:Ie(a.end_time)||"Not set"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Cancellation Policy (Days)"}),m?e.jsx("input",{type:"number",value:d("cancellation_policy_days")||"",onChange:s=>r("cancellation_policy_days",parseInt(s.target.value)||1),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",min:"1",max:"30"}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.cancellation_policy_days||"Not set"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Recurring Event"}),m?e.jsxs("select",{value:d("recurring")===1||d("recurring")===!0?"Yes":"No",onChange:s=>r("recurring",s.target.value==="Yes"?1:0),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",children:[e.jsx("option",{value:"No",children:"No"}),e.jsx("option",{value:"Yes",children:"Yes"})]}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:`h-2 w-2 rounded-full ${a.recurring===1?"bg-green-400":"bg-gray-400"}`}),e.jsx("span",{children:a.recurring===1?"Yes":"No"})]})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Coaches"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("span",{className:"rounded-full bg-primaryBlue/10 px-2 py-1 text-xs font-semibold text-primaryBlue",children:[B.length," assigned"]}),m&&e.jsxs("button",{onClick:async()=>{await Ue(),U(!0)},disabled:F,className:"inline-flex items-center rounded-lg bg-primaryBlue px-3 py-1 text-xs text-white transition-colors hover:bg-primaryBlue/90 disabled:cursor-not-allowed disabled:opacity-50",children:[F?e.jsxs("svg",{className:"mr-1 h-3 w-3 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):e.jsx("svg",{className:"mr-1 h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),F?"Loading...":"Add Coach"]})]})]})}),B.length>0?e.jsx("div",{className:"space-y-3",children:B.map(s=>{var o,l,f,L,K;return e.jsxs("div",{className:"flex items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-4 transition-all hover:shadow-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full shadow-sm ring-2 ring-white",children:e.jsx("img",{src:((o=s.user)==null?void 0:o.photo)||s.photo||"/default-avatar.png",alt:`${((l=s.user)==null?void 0:l.first_name)||""} ${((f=s.user)==null?void 0:f.last_name)||""}`,className:"h-full w-full object-cover",onError:A=>{A.target.src="/default-avatar.png"}})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"font-semibold text-gray-900",children:[(L=s.user)==null?void 0:L.first_name," ",(K=s.user)==null?void 0:K.last_name]}),e.jsx("div",{className:"text-sm text-gray-600",children:s.hours.length>0?(()=>{const A=[...s.hours].sort((Q,Ve)=>{const ye=Ye=>{if(!Ye||!Ye.includes(" "))return 0;const ns=Ye.split(" ");if(ns.length!==2)return 0;const[Ts,os]=ns,Ke=Ts.split(":");if(Ke.length!==2)return 0;const qe=parseInt(Ke[0])||0,ks=parseInt(Ke[1])||0;let Ge=qe;return os==="PM"&&qe!==12&&(Ge+=12),os==="AM"&&qe===12&&(Ge=0),Ge*60+ks};return ye(Q)-ye(Ve)}),xe=A.length*.5,re=A[0],q=A[A.length-1];if(!q||!q.includes(" "))return"Invalid time format";const S=q.split(" ");if(S.length!==2)return"Invalid time format";const[z,J]=S,W=z.split(":");if(W.length!==2)return"Invalid time format";const G=parseInt(W[0])||0,u=parseInt(W[1])||0;let x=G;J==="PM"&&G!==12&&(x+=12),J==="AM"&&G===12&&(x=0);const N=x*60+u+30,I=Math.floor(N/60),P=N%60,O=I>=12?"PM":"AM",ne=`${I%12||12}:${P.toString().padStart(2,"0")} ${O}`;return A.length===1?`${re} - ${ne} (0.5 hours)`:`${re} - ${ne} (${xe} ${xe===1?"hour":"hours"})`})():"No hours set"})]})]}),m&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{className:"rounded-lg border border-primaryBlue bg-white px-2 py-1 text-xs text-primaryBlue transition-colors hover:bg-blue-50",onClick:()=>{Fe(s),de(!0)},children:s.hours.length>0?"Edit hours":"Set hours"}),e.jsx("button",{onClick:()=>_s(s),className:"rounded-lg border border-red-200 p-2 text-red-400 transition-colors hover:bg-red-50 hover:text-red-600",title:"Remove coach",children:e.jsx(is,{className:"h-3 w-3"})})]})]},s.id)})}):e.jsxs("div",{className:"rounded-lg border-2 border-dashed border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100",children:e.jsx("svg",{className:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"})})}),e.jsx("p",{className:"font-medium text-gray-500",children:"No coaches assigned"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:m?"Click 'Add Coach' to assign coaches to this clinic":"Coaches will appear here when assigned to this clinic"}),m&&e.jsxs("button",{onClick:async()=>{await Ue(),U(!0)},disabled:F,className:"mt-4 inline-flex items-center rounded-lg bg-primaryBlue px-4 py-2 text-white transition-colors hover:bg-primaryBlue/90 disabled:cursor-not-allowed disabled:opacity-50",children:[F?e.jsxs("svg",{className:"mr-2 h-4 w-4 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):e.jsx("svg",{className:"mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),F?"Loading coaches...":"Add coaches"]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Categories & Tags"})}),m?e.jsx(Js,{club:_,selectedCategories:ue,selectedSubcategories:ee,selectedTags:he,onCategoriesChange:me,onSubcategoriesChange:pe,onTagsChange:ge}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Categories"}),e.jsx("div",{className:"mt-2",children:ue.length>0?e.jsx("div",{className:"flex flex-wrap gap-2",children:ue.map(s=>e.jsx("span",{className:"inline-flex items-center rounded-lg border border-primaryBlue bg-primaryBlue px-3 py-1 text-sm text-white",children:s.name},s.id))}):e.jsx("p",{className:"text-sm text-gray-500",children:"No categories selected"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Subcategories"}),e.jsx("div",{className:"mt-2",children:ee.length>0?e.jsx("div",{className:"flex flex-wrap gap-2",children:ee.map(s=>e.jsx("span",{className:"inline-flex items-center rounded-lg border border-green-500 bg-green-500 px-3 py-1 text-sm text-white",children:s.name},s.id))}):e.jsx("p",{className:"text-sm text-gray-500",children:"No subcategories selected"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Tags"}),e.jsx("div",{className:"mt-2",children:he.length>0?e.jsx("div",{className:"flex flex-wrap gap-2",children:he.map(s=>e.jsx("span",{className:"inline-flex items-center rounded-full border border-green-500 bg-green-500 px-3 py-1 text-sm text-white",children:s.name},s.id))}):e.jsx("p",{className:"text-sm text-gray-500",children:"No tags selected"})})]})]})]})]}),fe&&e.jsx(xs,{})]})," ",Je&&e.jsx(Us,{onClose:()=>{r("sport_id",V.sport_id),r("type",V.type),r("sub_type",V.sub_type),y(null),we(!1)},onConfirm:async(s,o)=>{console.log("Sport change completed:",{option:s,response:o}),C(v,"Clinic updated successfully",3e3,"success"),n&&$&&Object.keys($).forEach(l=>{l!=="id"&&(n[l]=$[l])}),$&&Te({sport_id:$.sport_id,type:$.type,sub_type:$.sub_type}),D(!1),y(null),we(!1),await Ee(),j()},clinic:a,formData:$}),ie&&(()=>{const s=c.filter(o=>!He.some(l=>l.id===o.id));return console.log("CoachSelectionModal props:",{allCoaches:c,selectedCoaches:He,availableCoaches:s}),e.jsx(Ds,{coaches:s,selectedCoaches:Ne,setSelectedCoaches:_e,isOpen:ie,loading:ze,onClose:()=>{$e()},onSave:async()=>{if(Ne.length===0){C(v,"Please select at least one coach",3e3,"warning");return}console.log("Setting pending coaches for hours:",Ne),ce(Ne),Ce(0),_e([]),U(!1)}})})(),e.jsx(ws,{})]})};let Xe=new Ze,Ys=new $s;const Ks=[{header:"Clinic ID",accessor:"id"},{header:"Name",accessor:"name"},{header:"Max participants",accessor:"max_participants"},{header:"Start Date",accessor:"date"},{header:"End date",accessor:"end_date"},{header:"Time",accessor:"start_time"},{header:"Sport",accessor:"sport"},{header:"Fee",accessor:"cost_per_head"}],qs=({club:n,sports:H,courts:j})=>{const R=localStorage.getItem("role"),{dispatch:w}=p.useContext(hs),{dispatch:a}=p.useContext(gs),[r,d]=p.useState([]),[g,Z]=p.useState(10),[m,D]=p.useState(0),[fe,le]=p.useState(0),[B,be]=p.useState(0),[c,T]=p.useState(!1),[es,Oe]=p.useState(!1),[He,Re]=p.useState(!1);p.useState([]),p.useState([]);const[ie,U]=p.useState(!0),[ve,de]=p.useState(!1),[h,Fe]=p.useState(!1),Pe=Es(),je=p.useRef(null),[Ne,_e]=p.useState(!1),[F,Se]=p.useState(null),[ze,De]=p.useState(!1),[k,ce]=p.useState(!1),[M,Ce]=p.useState(!1);p.useState([]);const Je=Hs({id:Le(),email:Le(),role:Le(),status:Le()});ys({resolver:Rs(Je)});const we=t=>{t===""?y(1,g):y(1,g,{},[`courtmatchup_clinics.name,cs,${t}`])},V=t=>{console.log("date search",t),t===""?y(1,g):y(1,g,{},[`date,cs,${t}`])};function Te(){y(B-1,g)}function $(){y(B+1,g)}async function y(t,b,We={},ke=[]){U(!(h||ve));try{Xe.setTable("clinics");const se=await Ys.getPaginate("clinics",{page:t,limit:b,filter:[...ke,`courtmatchup_clinics.club_id,eq,${n==null?void 0:n.id}`],join:["sports|sport_id"]});se&&U(!1);const{list:Be,total:te,limit:ae,num_pages:Y,page:v}=se;d(Be),Z(ae),D(Y),be(v),le(te),T(v>1),Oe(v+1<=Y)}catch(se){U(!1),console.log("ERROR",se),cs(a,se.message)}}const ue=t=>{t.target.value===""?y(1,g):y(1,g,{},[`status,cs,${t.target.value}`])},me=t=>{const b=t.target.value;b===""?y(1,g):y(1,g,{},[`sport_id,eq,${b}`])};p.useEffect(()=>{if(w({type:"SETPATH",payload:{path:"program-clinics"}}),!(n!=null&&n.id))return;const b=setTimeout(async()=>{await y(1,g)},700);return()=>{clearTimeout(b)}},[n]);const ee=t=>{je.current&&!je.current.contains(t.target)&&Re(!1)};p.useEffect(()=>(document.addEventListener("mousedown",ee),()=>{document.removeEventListener("mousedown",ee)}),[]);const pe=async t=>{ce(!0);try{Xe.setTable("clinics"),await Xe.callRestAPI({id:t},"DELETE"),y(B,g)}catch(b){console.error("Error deleting clinic:",b),cs(a,b.message)}finally{ce(!1)}},he=t=>{const b={...t,id:t==null?void 0:t.id,date:t==null?void 0:t.date,startTime:t==null?void 0:t.start_time,endTime:t==null?void 0:t.end_time,sport_id:t==null?void 0:t.sport_id,type:t==null?void 0:t.type,sub_type:t==null?void 0:t.sub_type,reservation_type:3,price:t==null?void 0:t.price,status:t==null?void 0:t.status,player_ids:t==null?void 0:t.player_ids,coach_ids:t==null?void 0:t.coach_ids};Se(b),_e(!0)},ge={status:t=>e.jsx("span",{className:`rounded-lg px-3 py-1 text-sm ${t.status===1?"bg-[#D1FAE5] text-[#065F46]":"bg-[#F4F4F4] text-[#393939]"}`,children:t.status===1?"Active":"Inactive"}),start_time:t=>Ie(t==null?void 0:t.start_time),date:t=>t!=null&&t.date?ms(new Date((t==null?void 0:t.date)+"T00:00:00"),"MMMM d, yyyy"):"",end_date:t=>t!=null&&t.end_date?ms(new Date((t==null?void 0:t.end_date)+"T00:00:00"),"MMMM d, yyyy"):"",players:t=>t!=null&&t.player_ids?`${JSON.parse(t==null?void 0:t.player_ids).length} players`:"0 players",sport:t=>{var b;return(b=t==null?void 0:t.sports)==null?void 0:b.name}};return e.jsxs("div",{className:"h-screen px-4 sm:px-6 lg:px-8",children:[k||ie&&e.jsx(xs,{}),e.jsx("div",{className:"flex flex-col gap-4 py-3",children:e.jsxs("div",{className:"flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between",children:[e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center",children:[e.jsxs("div",{className:"relative flex w-full max-w-xs flex-1 items-center",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(Ls,{className:"h-4 w-4 text-gray-500 sm:h-5 sm:w-5"})}),e.jsx("input",{type:"text",className:"block w-full rounded-md border border-gray-200 py-2 pl-10 pr-4 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:py-2.5 sm:pl-11",placeholder:"Search clinics",onChange:t=>we(t.target.value)})]}),e.jsx("input",{type:"date",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:w-auto",onChange:t=>V(t.target.value)})]}),e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:flex-wrap sm:items-center",children:[e.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:w-auto",defaultValue:"All",onChange:me,children:[e.jsx("option",{value:"",children:"Sport: All"}),H==null?void 0:H.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),e.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:w-auto",defaultValue:"All",onChange:ue,children:[e.jsx("option",{value:"",children:"Status: All"}),e.jsx("option",{value:"0",children:"Inactive"}),e.jsx("option",{value:"1",children:"Active"})]}),e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4",children:[e.jsxs("button",{onClick:()=>Pe(`/${R}/program-clinics/add`),className:"inline-flex items-center justify-center gap-2 rounded-md bg-[#1D275F] px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 sm:px-4",children:[e.jsx("span",{className:"text-sm",children:"+"}),e.jsx("span",{className:"hidden sm:inline",children:"Add new"}),e.jsx("span",{className:"sm:hidden",children:"Add"})]}),e.jsx("div",{className:"hidden sm:block",children:e.jsx(us,{title:"Clinic History",emptyMessage:"No clinic history found",activityType:ds.clinic})})]}),e.jsx("div",{className:"sm:hidden",children:e.jsx(us,{title:"Clinic History",emptyMessage:"No clinic history found",activityType:ds.clinic})})]})]})}),ie?e.jsx(zs,{}):e.jsx(Ws,{columns:Ks,data:r,loading:ie,renderCustomCell:ge,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer",emptyMessage:"No clinics available",loadingMessage:"Loading clinics...",onClick:t=>he(t)}),e.jsx(Fs,{currentPage:B,pageCount:m,pageSize:g,canPreviousPage:c,canNextPage:es,updatePageSize:t=>{Z(t),y(1,t)},previousPage:Te,nextPage:$,gotoPage:t=>y(t,g)}),e.jsx(Os,{isOpen:ze,onClose:()=>De(!1),onDelete:pe,loading:M,title:"Delete",message:"Are you sure you want to delete this clinic?"}),e.jsx(Vs,{getData:y,onClose:()=>Se(null),clinic:F,isOpen:F!==null,sports:H})]})},ct=qs;export{ct as L};
