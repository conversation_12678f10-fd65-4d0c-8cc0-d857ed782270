import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as i,u as m,f as h}from"./vendor-851db8c1.js";import{ba as l,p as d,bf as f}from"./index-13fd629e.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const U=()=>{const[n,r]=i.useState(!1),[s,c]=i.useState(!1),x=m(),u=h(),e=o=>{r(!1);const a=document.getElementById(o);a?a.scrollIntoView({behavior:"smooth"}):x.pathname!=="/"&&u("/#"+o)};return i.useEffect(()=>{const o=()=>{c(window.scrollY>20)};return window.addEventListener("scroll",o),()=>window.removeEventListener("scroll",o)},[]),t.jsxs(t.Fragment,{children:[t.jsxs("header",{className:`fixed left-0 right-0 top-0 z-50 transition-all duration-300 ${s?"bg-white shadow-lg":"bg-[#005954]"}`,children:[t.jsx("div",{className:"container mx-auto max-w-7xl px-6",children:t.jsxs("div",{className:"flex items-center justify-between py-4",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx("div",{className:`mr-2 flex h-10 w-10 items-center justify-center rounded-full p-2 ${s?"bg-[#005954]":"bg-[#F2F2F2]"}`,children:t.jsx("span",{className:`font-ubuntu text-sm font-bold ${s?"text-white":"text-[#005954]"}`,children:"CM"})}),t.jsx("span",{className:`font-ubuntu text-lg font-medium ${s?"text-[#005954]":"text-white"}`,children:"Court Matchup"})]}),t.jsxs("nav",{className:"hidden items-center space-x-8 md:flex",children:[t.jsx("button",{onClick:()=>e("hero"),className:`font-ubuntu transition-colors hover:text-[#005954] ${s?"text-gray-700":"text-white"}`,children:"Home"}),t.jsx("button",{onClick:()=>e("features"),className:`font-ubuntu transition-colors hover:text-[#005954] ${s?"text-gray-700":"text-white"}`,children:"Features"}),t.jsx("button",{onClick:()=>e("pricing"),className:`font-ubuntu transition-colors hover:text-[#005954] ${s?"text-gray-700":"text-white"}`,children:"Pricing"}),t.jsx("button",{onClick:()=>e("benefits"),className:`font-ubuntu transition-colors hover:text-[#005954] ${s?"text-gray-700":"text-white"}`,children:"Benefits"}),t.jsx("button",{onClick:()=>e("contact"),className:`font-ubuntu transition-colors hover:text-[#005954] ${s?"text-gray-700":"text-white"}`,children:"Contact"}),t.jsx("button",{onClick:()=>e("faq"),className:`font-ubuntu transition-colors hover:text-[#005954] ${s?"text-gray-700":"text-white"}`,children:"FAQ"})]}),t.jsxs("div",{className:"hidden items-center space-x-4 md:flex",children:[t.jsx("button",{className:"text-gray-600 hover:text-[#005954]",children:t.jsx(l,{size:20})}),t.jsx("button",{onClick:()=>e("contact"),className:"font-ubuntu rounded-full bg-[#005954] px-6 py-2 text-white transition-all hover:bg-[#004a45]",children:"Book Demo"})]}),t.jsx("button",{className:`md:hidden ${s?"text-[#005954]":"text-white"}`,onClick:()=>r(!n),children:n?t.jsx(d,{size:24}):t.jsx(f,{size:24})})]})}),n&&t.jsx("div",{className:"absolute left-0 right-0 top-full bg-white shadow-lg md:hidden",children:t.jsx("div",{className:"container mx-auto max-w-7xl px-6 py-4",children:t.jsxs("nav",{className:"flex flex-col space-y-4",children:[t.jsx("button",{onClick:()=>e("hero"),className:"font-ubuntu text-left text-gray-700 hover:text-[#005954]",children:"Home"}),t.jsx("button",{onClick:()=>e("features"),className:"font-ubuntu text-left text-gray-700 hover:text-[#005954]",children:"Features"}),t.jsx("button",{onClick:()=>e("pricing"),className:"font-ubuntu text-left text-gray-700 hover:text-[#005954]",children:"Pricing"}),t.jsx("button",{onClick:()=>e("benefits"),className:"font-ubuntu text-left text-gray-700 hover:text-[#005954]",children:"Benefits"}),t.jsx("button",{onClick:()=>e("contact"),className:"font-ubuntu text-left text-gray-700 hover:text-[#005954]",children:"Contact"}),t.jsx("button",{onClick:()=>e("faq"),className:"font-ubuntu text-left text-gray-700 hover:text-[#005954]",children:"FAQ"}),t.jsxs("div",{className:"flex items-center space-x-4 pt-4",children:[t.jsx("button",{className:"text-gray-600 hover:text-[#005954]",children:t.jsx(l,{size:20})}),t.jsx("button",{onClick:()=>e("contact"),className:"font-ubuntu w-full rounded-full bg-[#005954] px-6 py-2 text-white transition-all hover:bg-[#004a45]",children:"Book Demo"})]})]})})})]}),t.jsx("div",{className:"h-16"})]})};export{U as PublicHeader,U as default};
