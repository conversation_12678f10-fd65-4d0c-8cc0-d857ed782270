import{j as a}from"./@nivo/heatmap-ba1ecfff.js";import{b as f}from"./vendor-851db8c1.js";import{S as t}from"./SelectionOption-658322e6.js";const j=f.memo(({title:c,options:s,selectedOption:r,onOptionSelect:l,emptyMessage:m="No options available",placeholder:d="Please select a sport first",showPlaceholder:i=!1,optionType:x="default"})=>{const n=()=>{if(i)return a.jsx("p",{className:"text-center text-sm text-gray-500",children:d});if(!s||s.length===0)return a.jsx("p",{className:"text-center text-sm text-gray-500",children:m});switch(x){case"sport":return s.filter(e=>e.status===1).map(e=>a.jsx(t,{label:e.name,selected:r===e.id,onClick:()=>l(e.id)},e.id));case"type":return s.filter(e=>e.type).map(e=>a.jsx(t,{label:e.type,selected:r===e.type,onClick:()=>l(e.type)},e.type));case"subtype":return s.map(e=>a.jsx(t,{label:e,selected:r===e,onClick:()=>l(e)},e));default:return s.map(e=>a.jsx(t,{label:e.name||e,selected:r===(e.id||e),onClick:()=>l(e.id||e)},e.id||e))}};return a.jsxs("div",{className:"space-y-2",children:[a.jsx("p",{className:"text-sm font-medium text-gray-500",children:c}),a.jsx("div",{className:"space-y-2",children:n()})]})});j.displayName="SelectionOptionsCard";export{j as S};
