import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{e as Ce,d as se,M as ge,G as Ne,b as V,a as qe,ad as Ze,ar as Ye,as as We,af as je,R as Ke,A as Qe,u as Xe,E as Ve,t as Te}from"./index-13fd629e.js";import{r as a,b as we,f as es}from"./vendor-851db8c1.js";import{A as ss}from"./AuthLayout-ceb43654.js";import{u as ts,b as rs}from"./react-hook-form-687afde5.js";import{u as ls,G as as,M as ns}from"./@react-google-maps/api-bec1613d.js";import{u as is,g as os,a as cs}from"./use-places-autocomplete-4cb4aca6.js";import{a as ye,q as ds}from"./@headlessui/react-a5400090.js";import{S as ps}from"./SplashScreenPagePreview-f6f878ef.js";import{B as ms}from"./BottomDrawer-3018f655.js";import{I as xs}from"./ImageCropModal-9d3311e2.js";import{I as hs}from"./InformationCircleIcon-d35f3488.js";import{P as us}from"./PencilIcon-35185602.js";import{T as gs}from"./TrashIcon-aaaccaf2.js";import{P as Ae}from"./PlusIcon-7e8d14d7.js";import"./lodash-91d5d207.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./ContactModal-541f1b35.js";import"./react-image-crop-1f5038af.js";const fs=["places"],ys="AIzaSyC6zItKyKbnIcdpgwNoRIByQEvezUbdFAA",bs=({onNext:R,register:h,errors:S,setValue:v,defaultValues:l,isSubmitting:t})=>{const{isLoaded:b,loadError:j}=ls({googleMapsApiKey:ys,libraries:fs}),[y,C]=a.useState(()=>{if(l!=null&&l.club_location)try{const U=JSON.parse(l.club_location);return{lat:U.lat,lng:U.lng}}catch{return null}return null}),[m,T]=a.useState(null),D=a.useCallback(U=>{T(U)},[]),I=y||{lat:51.5074,lng:-.1278};return j?e.jsx("div",{children:"Error loading maps"}):b?e.jsxs("div",{className:"w-full max-w-xl",children:[e.jsxs("div",{className:"mb-6 flex flex-col items-center justify-center gap-4",children:[e.jsx("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg"}),e.jsx("h2",{className:"text-2xl font-bold",children:"Location"})]}),e.jsx(js,{setSelectedLocation:C,selectedLocation:y,map:m,onLoad:D,defaultCenter:I,onNext:R,setValueProp:v,isLoaded:b,defaultValues:l,isSubmitting:t})]}):e.jsx(Ce,{})},js=({setSelectedLocation:R,selectedLocation:h,map:S,onLoad:v,defaultCenter:l,onNext:t,setValueProp:b,isLoaded:j,defaultValues:y,isSubmitting:C})=>{const m=we.useMemo(()=>{try{if(y!=null&&y.club_location)return JSON.parse(y.club_location).address||""}catch(A){console.error("Error parsing club_location:",A)}return""},[y]),{ready:T,value:D,suggestions:{status:I,data:U},setValue:z,clearSuggestions:ee}=is({debounce:300,initOnMount:!0,cache:!1,googleMaps:j?window.google.maps:void 0,defaultValue:m});a.useEffect(()=>{m&&z(m,!1)},[m,z]);const Y=async A=>{z(A,!1),ee();try{const w=await os({address:A}),{lat:u,lng:c}=await cs(w[0]);R({lat:u,lng:c}),b("club_location",JSON.stringify({lat:u,lng:c,address:w[0].formatted_address})),S==null||S.panTo({lat:u,lng:c})}catch(w){console.error("Error: ",w)}},X=A=>{z(A.target.value)};return e.jsxs("form",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Club location"}),e.jsx(ye,{onChange:Y,children:e.jsxs("div",{className:"relative mt-1",children:[e.jsx("div",{className:"relative w-full cursor-default overflow-hidden rounded bg-white text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 sm:text-sm",children:e.jsx(ye.Input,{className:"mb-3 w-full rounded border-[1px] border-[#C6C6C6] bg-[#F8F8F8] p-2 px-4 py-2 leading-tight text-gray-700 focus:border-blue-500 focus:ring-1 focus:ring-blue-500",displayValue:A=>A||D,onChange:X,value:D,disabled:!T,placeholder:T?"Search location":"Loading..."})}),e.jsx(ds,{as:a.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",afterLeave:()=>z(null),children:e.jsx(ye.Options,{className:"absolute z-[999] mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base ring-1 ring-black/5 focus:outline-none sm:text-sm",children:I==="OK"&&e.jsx(e.Fragment,{children:U.length===0&&D!==""?e.jsx("div",{className:"relative cursor-default select-none px-4 py-2 text-gray-700",children:"Nothing found."}):U.map(({place_id:A,description:w})=>e.jsx(ye.Option,{className:({active:u})=>`relative cursor-default select-none py-2 pl-10 pr-4 ${u?"bg-blue-600 text-white":"text-gray-900"}`,value:w,children:({selected:u,active:c})=>e.jsxs(e.Fragment,{children:[e.jsx("span",{className:`block truncate ${u?"font-medium":"font-normal"}`,children:w}),u?e.jsx("span",{className:`absolute inset-y-0 left-0 flex items-center pl-3 ${c?"text-white":"text-blue-600"}`,children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",viewBox:"0 0 50 50",children:[e.jsx("path",{fill:"currentColor",d:"M25 42c-9.4 0-17-7.6-17-17S15.6 8 25 8s17 7.6 17 17s-7.6 17-17 17m0-32c-8.3 0-15 6.7-15 15s6.7 15 15 15s15-6.7 15-15s-6.7-15-15-15"}),e.jsx("path",{fill:"currentColor",d:"m23 32.4l-8.7-8.7l1.4-1.4l7.3 7.3l11.3-11.3l1.4 1.4z"})]})}):null]})},A))})})})]})}),e.jsxs("div",{className:"mt-3 flex items-center gap-2",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("span",{className:"text-xs text-gray-500",children:"Search and drop pin to the location"})]})]}),e.jsx("div",{className:"mt-4 h-[300px] w-full",children:e.jsx(as,{mapContainerClassName:"w-full h-full rounded-xl",center:h||l,zoom:13,onLoad:v,children:h&&e.jsx(ns,{position:h})})}),e.jsx(se,{onClick:t,loading:C,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Continue"})]})};let ws=new ge;function vs({onNext:R,register:h,errors:S,setValue:v,clubProfile:l,defaultValues:t}){var ke,Me,Ee,Le,Ie,He,Be;const[b,j]=a.useState(0),[y,C]=a.useState([]),[m,T]=a.useState(null),[D,I]=a.useState(null),[U,z]=a.useState(t==null?void 0:t.club_logo),[ee,Y]=a.useState(!1),[X,A]=a.useState(!1),[w,u]=a.useState(null),[c,N]=a.useState(null),[H,P]=a.useState(!1),[r,M]=a.useState(!1),[k,$]=a.useState(t==null?void 0:t.name),[W,Q]=a.useState(k),[ie,oe]=a.useState(!1),[d,s]=a.useState(((ke=t==null?void 0:t.splash_screen)==null?void 0:ke.slideshow_delay)||6e3),[o,p]=a.useState(()=>{var i;if((i=t==null?void 0:t.splash_screen)!=null&&i.images){const g=new Array(9).fill(null);return t.splash_screen.images.forEach((f,Z)=>{f&&f.url&&(g[Z]={url:f.url,isDefault:!0,id:f.id||`default-${Z}`,type:f.type||"image"})}),g}return new Array(9).fill(null)}),[_,F]=a.useState(!1),E=a.useRef(),J=a.useRef(),q=a.useRef(),le=a.useRef(),{dispatch:n}=a.useContext(Ne),[x,O]=a.useState(!1);a.useEffect(()=>{if(t!=null&&t.splash_screen)try{const i=t==null?void 0:t.splash_screen;E.current&&i.bio&&(E.current.value=i.bio)}catch(i){console.error("Error parsing splash screen data:",i)}},[t]),a.useEffect(()=>{const i=b*3,g=o.slice(i,i+3);C(g)},[b,o]);const B=async i=>{try{let g=new FormData;g.append("file",i);const f=i.type.startsWith("video/");let Z;return Z=await ws.uploadImage(g),Z.url}catch(g){return console.error("Upload error:",g),V(n,"Failed to upload file. Please try again.",3e3,"error"),null}},G=()=>o.slice(0,3).filter(g=>g!==null).length,te=i=>{const g=Math.floor(i/3),f=i%3;if(g===0)return!0;if(!o.slice(0,3)[f])return!1;const ne=G(),ae=g*3;return o.slice(ae,ae+3).filter(fe=>fe!==null).length<ne},de=(i,g)=>{const f=i.target.files[0];if(!f)return;const Z=b*3+g;if(!te(Z)){const ce=G();V(n,`You can only upload up to ${ce} image${ce!==1?"s":""} per slide based on your Slide 1 pattern`,3e3,"error");return}if(!["image/jpeg","image/png","image/gif","video/mp4","video/quicktime","video/x-m4v","application/pdf"].includes(f.type)){V(n,"Please upload a valid file type (JPEG, PNG, GIF, MP4, or PDF)",3e3,"error");return}if(f.size>50*1024*1024){V(n,"File size must be less than 50MB",3e3,"error");return}const ne=[...o],ae=URL.createObjectURL(f);let re="image";f.type.startsWith("video/")?re="video":f.type==="application/pdf"&&(re="pdf"),ne[Z]={file:f,url:ae,id:Date.now(),isDefault:!1,type:re,previewUrl:ae},p(ne)},me=i=>{i.preventDefault(),i.stopPropagation()},he=i=>{i.preventDefault(),i.stopPropagation()},ue=i=>{i.preventDefault(),i.stopPropagation()},L=(i,g)=>{i.preventDefault(),i.stopPropagation();const f=i.dataTransfer.files;if(f.length>0){const Z=b*3+g;if(!te(Z)){const ne=G();V(n,`You can only upload up to ${ne} image${ne!==1?"s":""} per slide based on your Slide 1 pattern`,3e3,"error");return}const K={target:{files:[f[0]]}};de(K,g)}},pe=i=>{const g=b*3+i,f=[...o],Z=f[g];if(Z){!Z.isDefault&&Z.url&&URL.revokeObjectURL(Z.url),f[g]=null;const K=Math.floor(g/3),ne=g%3;if(K===0)for(let ae=1;ae<3;ae++){const re=ae*3+ne;f[re]&&(!f[re].isDefault&&f[re].url&&URL.revokeObjectURL(f[re].url),f[re]=null)}else K===1&&ne===2&&f[8]&&(!f[8].isDefault&&f[8].url&&URL.revokeObjectURL(f[8].url),f[8]=null);p(f)}},_e=()=>{j(i=>i+1)},Re=()=>{j(i=>Math.max(0,i-1))},Se=()=>y,Ge=async()=>{var i,g,f,Z;try{F(!0);const K=(i=E.current)==null?void 0:i.value;if(!(K!=null&&K.trim())){V(n,"Please enter a bio",3e3,"error");return}const ne=await Promise.all(o.map(async ce=>{if(!ce)return null;if(ce.isDefault)return ce;{const fe=await B(ce.file);return fe?(ce.previewUrl&&URL.revokeObjectURL(ce.previewUrl),{url:fe,isDefault:!0,id:`default-${Date.now()}-${Math.random()}`,type:ce.file.type.startsWith("video/")?"video":"image"}):null}})),ae={bio:K.trim(),images:ne,slideshow_delay:d,button_text:((g=J.current)==null?void 0:g.value)||"Let the club know you're interested",phone:((f=q.current)==null?void 0:f.value)||"",email:((Z=le.current)==null?void 0:Z.value)||""};v("splash_screen",JSON.stringify(ae)),v("club_logo",U);const re={splash_screen:JSON.stringify(ae),club_logo:U};ie&&(re.name=k),console.log("Final submission data:",re),await R(re),p(new Array(9).fill(null)),E.current.value="",j(0)}catch(K){console.error("Submission failed:",K),V(n,"Failed to submit. Please try again.",3e3,"error")}finally{F(!1)}},$e=i=>{const g=i.target.files[0];if(!g)return;if(g.size>2*1024*1024){alert("File size exceeds 2MB limit. Please choose a smaller file.");return}N(g.type);const f=new FileReader;f.onload=()=>{u(f.result),A(!0)},f.readAsDataURL(g)},De=async i=>{Y(!0),T(URL.createObjectURL(i));const g=c==="image/png",f=await B(i);z(f),I(new File([i],`cropped_logo.${g?"png":"jpg"}`,{type:g?"image/png":"image/jpeg"})),qe(URL.createObjectURL(i)),Y(!1)};a.useEffect(()=>()=>{m&&URL.revokeObjectURL(m)},[m]);const ze=()=>{Q((t==null?void 0:t.name)||""),P(!0)},Pe=()=>{W!==(t==null?void 0:t.name)&&oe(!0),$(W),Q(W),P(!1)},Je=()=>{Q(k),P(!1)};return e.jsxs("div",{className:"flex max-w-xl flex-col gap-4 rounded-lg bg-white",children:[ee&&e.jsx(Ce,{}),e.jsxs("div",{className:"flex flex-col items-center justify-center text-center",children:[e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_507_13438)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_507_13438)"}),e.jsxs("g",{filter:"url(#filter0_d_507_13438)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24.5 29.1875C24.5 27.9794 25.4794 27 26.6875 27H47.3125C48.5206 27 49.5 27.9794 49.5 29.1875V44.8125C49.5 46.0206 48.5206 47 47.3125 47H26.6875C25.4794 47 24.5 46.0206 24.5 44.8125V29.1875ZM39.1875 33.25C37.9794 33.25 37 34.2294 37 35.4375V38.5625C37 39.7706 37.9794 40.75 39.1875 40.75H42.3125C43.5206 40.75 44.5 39.7706 44.5 38.5625V35.4375C44.5 34.2294 43.5206 33.25 42.3125 33.25H39.1875ZM30.4375 33.5625C29.9197 33.5625 29.5 33.9822 29.5 34.5C29.5 35.0178 29.9197 35.4375 30.4375 35.4375H33.5625C34.0803 35.4375 34.5 35.0178 34.5 34.5C34.5 33.9822 34.0803 33.5625 33.5625 33.5625H30.4375ZM30.4375 38.5625C29.9197 38.5625 29.5 38.9822 29.5 39.5C29.5 40.0178 29.9197 40.4375 30.4375 40.4375H33.5625C34.0803 40.4375 34.5 40.0178 34.5 39.5C34.5 38.9822 34.0803 38.5625 33.5625 38.5625H30.4375Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_507_13438",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_507_13438"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_507_13438",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_507_13438",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_507_13438",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]}),e.jsx("p",{className:"text-2xl font-medium",children:"Description"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"mb-2 text-lg font-semibold",children:"Tell us about your club"}),e.jsxs("p",{className:"flex items-start gap-2 text-sm text-gray-500",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("span",{children:"This info will be visible to users who want to book your club. You can edit this content into your Club Panel later."})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"relative h-[100px] w-[100px] overflow-hidden rounded-lg bg-gray-100",children:e.jsx("img",{src:m||U||((Me=l==null?void 0:l.club)==null?void 0:Me.club_logo)||"/logo.png",alt:"Club logo",className:"h-full w-full object-cover"})}),e.jsxs("div",{className:"w-full space-y-1",children:[e.jsx("span",{children:"Club logo"}),e.jsx("div",{className:"flex justify-between py-1 text-xs text-gray-500",children:e.jsx("span",{children:"Min 400x400px, PNG or JPEG"})}),e.jsx("p",{className:"mb-2 text-xs text-gray-500",children:"This logo will be displayed on your club portal and as a favicon in browser tabs. For best results, use a square image with a clear, recognizable design."}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(se,{onClick:()=>{T(null),I(null)},className:"rounded-md border border-red-500 bg-white px-2 py-1 text-xs text-red-500",children:"Remove"}),e.jsx("input",{type:"file",id:"logo-upload",className:"hidden",accept:"image/**",onChange:$e}),e.jsx(se,{onClick:()=>document.getElementById("logo-upload").click(),className:"rounded-md border border-gray-400 bg-white px-2 py-1 text-xs text-gray-600",children:"Change Logo"})]})]})]}),e.jsx("div",{className:"mt-4 flex items-center gap-4 border-y border-gray-200 py-3",children:e.jsxs("div",{className:" w-full",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Club name"}),H?e.jsxs("div",{className:"space-y-2",children:[e.jsx("input",{type:"text",value:W,onChange:i=>Q(i.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-lg font-medium focus:border-blue-500 focus:outline-none",placeholder:"Enter club name"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(se,{onClick:Pe,loading:r,className:"rounded-lg bg-primaryBlue px-4 py-1 text-sm text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:Je,className:"text-sm text-primaryBlue hover:underline",children:"Cancel"})]})]}):e.jsxs("div",{children:[e.jsx("p",{className:"text-lg font-medium",children:k||"Club name"}),e.jsx("button",{onClick:ze,className:"text-sm text-primaryBlue hover:underline",children:"Edit"})]})]})}),e.jsx("div",{children:e.jsx("button",{className:"underline",onClick:()=>O(!0),children:"Page preview"})}),e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Upload images"}),e.jsx("p",{className:"mb-2 text-sm text-gray-500",children:"JPEG, PNG, PDF, and MP4 formats, up to 50 MB. Drag and drop files or click to browse."}),b>0&&e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2",children:e.jsxs("p",{className:"text-xs text-blue-700",children:[e.jsx("strong",{children:"Note:"})," You can upload up to"," ",G()," image",G()!==1?"s":""," per slide based on your Slide 1 pattern."]})}),e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"grid grid-rows-[1fr_1fr] gap-4",children:[e.jsx("div",{className:"h-[100px] max-h-[100px]",children:(()=>{const i=Se()[0],g=b*3+0,f=!te(g);return e.jsxs("div",{className:`relative h-full w-full rounded-xl border-2 border-dashed transition-colors ${f?"cursor-not-allowed border-gray-200 bg-gray-100":"border-gray-300 bg-white hover:border-gray-400"}`,onDragOver:f?void 0:me,onDragEnter:f?void 0:he,onDragLeave:f?void 0:ue,onDrop:f?void 0:Z=>L(Z,0),children:[e.jsx("input",{type:"file",className:"hidden",accept:"image/*,video/*,application/pdf",onChange:Z=>de(Z,0),id:"file-input-0",disabled:f}),e.jsx("label",{htmlFor:f?void 0:"file-input-0",className:`absolute inset-0 ${f?"cursor-not-allowed":"cursor-pointer"}`,children:i&&i.url?e.jsxs("div",{className:"relative h-full w-full",children:[i.type==="video"?e.jsxs("video",{src:i.url,className:"h-full w-full rounded-lg object-cover",controls:!0,controlsList:"nodownload",preload:"metadata",playsInline:!0,children:[e.jsx("source",{src:i.url,type:"video/mp4"}),"Your browser does not support the video tag."]}):i.type==="pdf"?e.jsx("div",{className:"flex h-full w-full items-center justify-center rounded-lg bg-red-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-2",children:e.jsx("path",{d:"M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z",fill:"#DC2626"})}),e.jsx("p",{className:"text-xs font-medium text-red-600",children:"PDF"})]})}):e.jsx("img",{src:i.url,alt:"Upload 1",className:"h-full w-full rounded-lg object-cover"}),e.jsx("button",{onClick:Z=>{Z.preventDefault(),Z.stopPropagation(),pe(0)},className:"absolute right-2 top-2 rounded-full bg-white p-1.5 shadow-md hover:bg-gray-100",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627ZM9 10.75V16.25H10.5V10.75H9ZM13.5 10.75V16.25H15V10.75H13.5ZM12 3.5C13.5134 3.5 14.7868 4.53504 15.1477 5.93694L16.6003 5.56306C16.0731 3.51451 14.2144 2 12 2V3.5ZM8.85237 5.93694C9.21319 4.53504 10.4867 3.5 12 3.5V2C9.78568 2 7.92697 3.51451 7.39971 5.56306L8.85237 5.93694Z",fill:"black"})})}),i.type==="video"&&e.jsx("div",{className:"absolute left-2 top-2 rounded-full bg-white/80 p-1.5",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16.5V7.5L16 12L10 16.5Z",fill:"#176448"})})})]}):e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:f?e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-1",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z",fill:"#9CA3AF"})}),e.jsx("p",{className:"text-xs text-gray-400",children:"Disabled"})]}):e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.9998 12.5274L15.8185 16.3452L14.545 17.6187L12.8998 15.9735V21H11.0998V15.9717L9.45461 17.6187L8.18111 16.3452L11.9998 12.5274ZM11.9998 3C13.5451 3.00007 15.0365 3.568 16.1904 4.59581C17.3443 5.62361 18.0803 7.03962 18.2584 8.5746C19.3782 8.87998 20.3552 9.56919 21.0184 10.5218C21.6816 11.4744 21.989 12.6297 21.8869 13.786C21.7847 14.9422 21.2794 16.0257 20.4594 16.8472C19.6394 17.6687 18.5567 18.1759 17.4007 18.2802V16.4676C17.8149 16.4085 18.2131 16.2674 18.5721 16.0527C18.9312 15.8379 19.2439 15.5539 19.4919 15.217C19.74 14.8801 19.9184 14.4972 20.0169 14.0906C20.1153 13.6839 20.1318 13.2618 20.0653 12.8488C19.9989 12.4357 19.8508 12.0401 19.6298 11.6849C19.4087 11.3297 19.1191 11.0221 18.7779 10.78C18.4367 10.538 18.0506 10.3663 17.6424 10.2751C17.2341 10.1838 16.8117 10.1748 16.3999 10.2486C16.5409 9.5924 16.5332 8.91297 16.3776 8.2601C16.222 7.60722 15.9223 6.99743 15.5004 6.47538C15.0786 5.95333 14.5454 5.53225 13.9397 5.24298C13.3341 4.9537 12.6714 4.80357 12.0003 4.80357C11.3291 4.80357 10.6664 4.9537 10.0608 5.24298C9.45515 5.53225 8.92189 5.95333 8.50007 6.47538C8.07825 6.99743 7.77854 7.60722 7.62291 8.2601C7.46728 8.91297 7.45966 9.5924 7.60061 10.2486C6.7795 10.0944 5.93076 10.2727 5.24112 10.7443C4.55147 11.2159 4.0774 11.9421 3.92321 12.7632C3.76901 13.5843 3.94731 14.433 4.41889 15.1227C4.89047 15.8123 5.6167 16.2864 6.43781 16.4406L6.59981 16.4676V18.2802C5.44371 18.1761 4.36097 17.669 3.54083 16.8476C2.72068 16.0261 2.2153 14.9426 2.11301 13.7863C2.01073 12.6301 2.31804 11.4747 2.98124 10.522C3.64444 9.56934 4.62134 8.88005 5.74121 8.5746C5.91914 7.03954 6.65507 5.62342 7.80903 4.59558C8.96298 3.56774 10.4545 2.99988 11.9998 3Z",fill:"#525866"})})})})]},`${b}-0`)})()}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:[1,2].map(i=>{const g=Se()[i],f=b*3+i,Z=!te(f);return e.jsxs("div",{className:`relative h-[100px] max-h-[100px] w-full rounded-xl border-2 border-dashed transition-colors ${Z?"cursor-not-allowed border-gray-200 bg-gray-100":"border-gray-300 bg-white hover:border-gray-400"}`,onDragOver:Z?void 0:me,onDragEnter:Z?void 0:he,onDragLeave:Z?void 0:ue,onDrop:Z?void 0:K=>L(K,i),children:[e.jsx("input",{type:"file",className:"hidden",accept:"image/*,video/*,application/pdf",onChange:K=>de(K,i),id:`file-input-${i}`,disabled:Z}),e.jsx("label",{htmlFor:Z?void 0:`file-input-${i}`,className:`absolute inset-0 ${Z?"cursor-not-allowed":"cursor-pointer"}`,children:g&&g.url?e.jsxs("div",{className:"relative h-full w-full",children:[g.type==="video"?e.jsxs("video",{src:g.url,className:"h-full w-full rounded-lg object-cover",controls:!0,controlsList:"nodownload",preload:"metadata",playsInline:!0,children:[e.jsx("source",{src:g.url,type:"video/mp4"}),"Your browser does not support the video tag."]}):g.type==="pdf"?e.jsx("div",{className:"flex h-full w-full items-center justify-center rounded-lg bg-red-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-2",children:e.jsx("path",{d:"M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z",fill:"#DC2626"})}),e.jsx("p",{className:"text-xs font-medium text-red-600",children:"PDF"})]})}):e.jsx("img",{src:g.url,alt:`Upload ${i+1}`,className:"h-full w-full rounded-lg object-cover"}),e.jsx("button",{onClick:K=>{K.preventDefault(),K.stopPropagation(),pe(i)},className:"absolute right-2 top-2 rounded-full bg-white p-1.5 shadow-md hover:bg-gray-100",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627ZM9 10.75V16.25H10.5V10.75H9ZM13.5 10.75V16.25H15V10.75H13.5ZM12 3.5C13.5134 3.5 14.7868 4.53504 15.1477 5.93694L16.6003 5.56306C16.0731 3.51451 14.2144 2 12 2V3.5ZM8.85237 5.93694C9.21319 4.53504 10.4867 3.5 12 3.5V2C9.78568 2 7.92697 3.51451 7.39971 5.56306L8.85237 5.93694Z",fill:"black"})})}),g.type==="video"&&e.jsx("div",{className:"absolute left-2 top-2 rounded-full bg-white/80 p-1.5",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16.5V7.5L16 12L10 16.5Z",fill:"#176448"})})})]}):e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:Z?e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-1",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z",fill:"#9CA3AF"})}),e.jsx("p",{className:"text-xs text-gray-400",children:"Disabled"})]}):e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.9998 12.5274L15.8185 16.3452L14.545 17.6187L12.8998 15.9735V21H11.0998V15.9717L9.45461 17.6187L8.18111 16.3452L11.9998 12.5274ZM11.9998 3C13.5451 3.00007 15.0365 3.568 16.1904 4.59581C17.3443 5.62361 18.0803 7.03962 18.2584 8.5746C19.3782 8.87998 20.3552 9.56919 21.0184 10.5218C21.6816 11.4744 21.989 12.6297 21.8869 13.786C21.7847 14.9422 21.2794 16.0257 20.4594 16.8472C19.6394 17.6687 18.5567 18.1759 17.4007 18.2802V16.4676C17.8149 16.4085 18.2131 16.2674 18.5721 16.0527C18.9312 15.8379 19.2439 15.5539 19.4919 15.217C19.74 14.8801 19.9184 14.4972 20.0169 14.0906C20.1153 13.6839 20.1318 13.2618 20.0653 12.8488C19.9989 12.4357 19.8508 12.0401 19.6298 11.6849C19.4087 11.3297 19.1191 11.0221 18.7779 10.78C18.4367 10.538 18.0506 10.3663 17.6424 10.2751C17.2341 10.1838 16.8117 10.1748 16.3999 10.2486C16.5409 9.5924 16.5332 8.91297 16.3776 8.2601C16.222 7.60722 15.9223 6.99743 15.5004 6.47538C15.0786 5.95333 14.5454 5.53225 13.9397 5.24298C13.3341 4.9537 12.6714 4.80357 12.0003 4.80357C11.3291 4.80357 10.6664 4.9537 10.0608 5.24298C9.45515 5.53225 8.92189 5.95333 8.50007 6.47538C8.07825 6.99743 7.77854 7.60722 7.62291 8.2601C7.46728 8.91297 7.45966 9.5924 7.60061 10.2486C6.7795 10.0944 5.93076 10.2727 5.24112 10.7443C4.55147 11.2159 4.0774 11.9421 3.92321 12.7632C3.76901 13.5843 3.94731 14.433 4.41889 15.1227C4.89047 15.8123 5.6167 16.2864 6.43781 16.4406L6.59981 16.4676V18.2802C5.44371 18.1761 4.36097 17.669 3.54083 16.8476C2.72068 16.0261 2.2153 14.9426 2.11301 13.7863C2.01073 12.6301 2.31804 11.4747 2.98124 10.522C3.64444 9.56934 4.62134 8.88005 5.74121 8.5746C5.91914 7.03954 6.65507 5.62342 7.80903 4.59558C8.96298 3.56774 10.4545 2.99988 11.9998 3Z",fill:"#525866"})})})})]},`${b}-${i}`)})})]},b),e.jsxs("div",{className:"mt-4 flex items-center justify-center gap-4",children:[e.jsx("button",{onClick:Re,disabled:b===0,className:`rounded-full bg-white p-2 shadow-md ${b===0?"cursor-not-allowed opacity-50":"hover:bg-gray-100"}`,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})}),e.jsxs("span",{className:"text-sm",children:["Slide ",b+1]}),e.jsx("button",{onClick:_e,disabled:b===2,className:`rounded-full bg-white p-2 shadow-md ${b===2?"cursor-not-allowed opacity-50":"hover:bg-gray-100"}`,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})})]})]})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Slideshow Delay (seconds)"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"number",min:"1",max:"60",value:d/1e3,onChange:i=>s(Math.max(1e3,Math.min(6e4,i.target.value*1e3))),className:"w-24 rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"}),e.jsx("span",{className:"text-sm text-gray-500",children:"seconds"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose between 1 and 60 seconds (default: 6 seconds)"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Bio"}),e.jsx("textarea",{ref:E,rows:4,className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Button Text"}),e.jsx("input",{type:"text",ref:J,defaultValue:(t==null?void 0:t.button_text)||"Let the club know you're interested",onChange:i=>{const g={...JSON.parse((t==null?void 0:t.splash_screen)||"{}"),button_text:i.target.value};v("splash_screen",JSON.stringify(g))},className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter button text"})]}),e.jsxs("div",{className:"mb-4 space-y-4",children:[e.jsx("h3",{className:"text-sm font-medium",children:"Contact Information"}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm text-gray-600",children:"Phone Number"}),e.jsx("input",{type:"tel",ref:q,defaultValue:(t==null?void 0:t.phone)||"",onChange:i=>{const g={...JSON.parse((t==null?void 0:t.splash_screen)||"{}"),phone:i.target.value};v("splash_screen",JSON.stringify(g))},className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter phone number"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm text-gray-600",children:"Email Address"}),e.jsx("input",{type:"email",ref:le,defaultValue:(t==null?void 0:t.email)||"",onChange:i=>{const g={...JSON.parse((t==null?void 0:t.splash_screen)||"{}"),email:i.target.value};v("splash_screen",JSON.stringify(g))},className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter email address"})]})]}),e.jsx(se,{onClick:Ge,loading:_,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Continue"}),e.jsx(ms,{isOpen:x,onClose:()=>O(!1),title:"Preview",children:e.jsx(ps,{clubName:(Ee=l==null?void 0:l.club)==null?void 0:Ee.name,image:l==null?void 0:l.name,description:(Le=E.current)==null?void 0:Le.value,imageList:o,clubLogo:U,slideshowDelay:d,buttonText:((Ie=J.current)==null?void 0:Ie.value)||"Let the club know you're interested",phone:((He=q.current)==null?void 0:He.value)||"",email:((Be=le.current)==null?void 0:Be.value)||""})}),e.jsx(xs,{isOpen:X,onClose:()=>A(!1),image:w,onCropComplete:De})]})}const xe=new ge;function Cs({onNext:R,register:h,errors:S,setValue:v,clubProfile:l,defaultValues:t,defaultSports:b,fetchClubProfile:j}){const[y,C]=a.useState(!1),[m,T]=a.useState(!1),[D,I]=a.useState(!1),[U,z]=a.useState("add"),[ee,Y]=a.useState(null),[X,A]=a.useState(!1),[w,u]=a.useState(!1),[c,N]=a.useState(!1),[H,P]=a.useState({name:"",types:[],sport_id:null}),{dispatch:r}=a.useContext(Ne),[M,k]=a.useState([]);a.useEffect(()=>{var n,x;if(((n=t==null?void 0:t.sports)==null?void 0:n.length)>0){const O=(x=t==null?void 0:t.sports)==null?void 0:x.map(B=>{var G;return{sport_id:B.id,name:B.name,club_id:B.club_id,types:((G=B.sport_types)==null?void 0:G.map(te=>({name:te.type,sub_type:te.subtype||[],club_sport_type_id:te.club_sport_type_id})))||[],status:B.status||0}});k(O)}else k([])},[t==null?void 0:t.sports]);const $=async n=>{A(!0);try{xe.setTable("sports");const x=await xe.callRestAPI({id:n.sport_id,status:n.status===1?0:1},"PUT");await j()}catch(x){console.error(x)}finally{A(!1)}},W=async n=>{A(!0);try{xe.setTable("sports");const x=await xe.callRestAPI({id:n.sport_id},"DELETE");k(O=>O.filter(B=>B.sport_id!==n.sport_id))}catch(x){console.error(x)}finally{A(!1)}},Q=n=>{z("edit"),Y(n),P({name:n.name,sport_id:n.sport_id,types:n.types||[]}),I(!0)},ie=()=>{z("add"),Y(null),P({name:"",types:[],sport_id:null}),I(!0)},oe=()=>{I(!1),Y(null),P({name:"",types:[],sport_id:null})},d=()=>{P(n=>({...n,types:[...n.types,{name:"",sub_type:[]}]}))},s=async(n,x)=>{x&&await le(x),P(O=>({...O,types:O.types.filter((B,G)=>G!==n)}))},o=(n,x)=>{P(O=>({...O,types:O.types.map((B,G)=>G===n?{...B,name:x}:B)}))},p=n=>{P(x=>({...x,types:x.types.map((O,B)=>B===n?{...O,sub_type:[...O.sub_type,""]}:O)}))},_=(n,x)=>{P(O=>({...O,types:O.types.map((B,G)=>G===n?{...B,sub_type:B.sub_type.filter((te,de)=>de!==x)}:B)}))},F=(n,x,O)=>{P(B=>({...B,types:B.types.map((G,te)=>te===n?{...G,sub_type:G.sub_type.map((de,me)=>me===x?O:de)}:G)}))},E=async()=>{var n;if(H.name.trim())try{u(!0);const x={name:H.name,club_id:(n=l==null?void 0:l.club)==null?void 0:n.id,types:H.types.map(B=>({name:B.name,sub_type:B.sub_type,...B.club_sport_type_id&&{club_sport_type_id:B.club_sport_type_id}}))};U==="edit"&&(x.sport_id=ee.sport_id);const O=await xe.callRawAPI("/v3/api/custom/courtmatchup/club/profile-edit",{sports:[x]},"POST");U==="edit"?(await j(),V(r,"Sport updated successfully",3e3,"success")):(await j(),V(r,"Sport added successfully",3e3,"success")),oe()}catch(x){console.error(x),V(r,x.message,3e3,"error")}finally{u(!1)}},J=async()=>{try{C(!0),await R()}catch(n){console.error(n),V(r,"Failed to save sports. Please try again.",3e3,"error")}finally{C(!1)}},q=M.some(n=>n.status===1),le=async n=>{N(!0);try{xe.setTable("club_sport_type");const x=await xe.callRestAPI({id:n},"DELETE");console.log("response",x)}catch(x){console.error(x)}finally{N(!1)}};return e.jsxs("div",{className:"w-full max-w-xl",children:[(X||c)&&e.jsx(Ce,{}),e.jsxs("div",{className:"mb-6 flex flex-col items-center justify-center gap-4",children:[e.jsx("div",{children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_25940)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_25940)"}),e.jsxs("g",{filter:"url(#filter0_d_397_25940)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M48.25 27.9375C48.25 26.7294 47.2706 25.75 46.0625 25.75H27.9375C26.7294 25.75 25.75 26.7294 25.75 27.9375V46.0625C25.75 47.2706 26.7294 48.25 27.9375 48.25H46.0625C47.2706 48.25 48.25 47.2706 48.25 46.0625V27.9375ZM33.25 32C33.25 32.8629 32.5504 33.5625 31.6875 33.5625C30.8246 33.5625 30.125 32.8629 30.125 32C30.125 31.1371 30.8246 30.4375 31.6875 30.4375C32.5504 30.4375 33.25 31.1371 33.25 32ZM33.25 37C33.25 37.8629 32.5504 38.5625 31.6875 38.5625C30.8246 38.5625 30.125 37.8629 30.125 37C30.125 36.1371 30.8246 35.4375 31.6875 35.4375C32.5504 35.4375 33.25 36.1371 33.25 37ZM31.6875 43.5625C32.5504 43.5625 33.25 42.8629 33.25 42C33.25 41.1371 32.5504 40.4375 31.6875 40.4375C30.8246 40.4375 30.125 41.1371 30.125 42C30.125 42.8629 30.8246 43.5625 31.6875 43.5625Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_25940",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_25940"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_25940",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_25940",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_25940",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"text-2xl font-medium",children:"Select the sports offered at your club"}),e.jsxs("div",{className:"relative",children:[e.jsx(hs,{className:"h-6 w-6 cursor-pointer text-gray-400 hover:text-gray-600",onMouseEnter:()=>T(!0),onMouseLeave:()=>T(!1)}),m&&e.jsxs("div",{className:"absolute left-1/2 top-8 z-50 w-64 -translate-x-1/2 transform rounded-xl bg-white p-4 shadow-lg",children:[e.jsx("p",{className:"mb-2 font-medium",children:"An example of Sport, Type, and Sub-Type:"}),e.jsx("p",{children:"Sport: Tennis"}),e.jsx("p",{children:"Type: Indoor (optional)"}),e.jsx("p",{children:"Sub-type: Grass court (optional)"})]})]})]})]}),e.jsx("div",{className:"mx-auto mb-6 flex max-w-fit flex-col justify-center gap-6",children:M.map(n=>{var x;return e.jsxs("div",{className:"flex w-full flex-col gap-3",children:[e.jsxs("div",{className:"flex w-fit items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",className:"h-5 w-5 rounded-md border-gray-300 text-primaryGreen focus:ring-primaryGreen",value:n.name,checked:n.status==1,onChange:()=>$(n)}),e.jsx("label",{className:"font-medium capitalize",children:n.name})]}),e.jsxs("button",{onClick:()=>Q(n),className:"flex items-center gap-1 rounded-lg px-2 py-1 text-sm text-gray-600 hover:bg-gray-100",children:[e.jsx(us,{className:"h-4 w-4"}),"Edit"]}),e.jsxs("button",{onClick:()=>W(n),className:"flex items-center gap-1 rounded-lg px-2 py-1 text-sm text-gray-600 hover:bg-gray-100",children:[e.jsx(gs,{className:"h-4 w-4"}),"Delete"]})]}),n.status===1&&e.jsx("div",{className:"ml-7 flex flex-col gap-2",children:(x=n.types)==null?void 0:x.map((O,B)=>{var G;return e.jsxs("div",{className:"flex flex-col gap-1",children:[O.name&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Type:"," ",e.jsx("span",{className:"capitalize text-gray-900",children:O.name})]}),((G=O.sub_type)==null?void 0:G.length)>0&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Sub-types:"," ",e.jsx("span",{className:"capitalize text-gray-900",children:O.sub_type.join(", ")})]})]},O.club_sport_type_id||B)})})]},n.sport_id||n.name)})}),e.jsx("button",{className:"mb-5 text-primaryBlue underline",onClick:ie,children:"+Add sport"}),D&&e.jsx("div",{className:"fixed inset-0 z-50 flex min-h-screen items-center justify-center bg-black/30 p-4",children:e.jsxs("div",{className:"w-full max-w-lg rounded-3xl bg-white p-8",children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:U==="add"?"Add New Sport":"Edit Sport"}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-base",children:"Sport name"}),e.jsx("input",{type:"text",className:"w-full rounded-xl border border-gray-200 px-4 py-3 text-base focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:H.name,onChange:n=>P(x=>({...x,name:n.target.value})),placeholder:"Enter sport name"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("label",{className:"mb-2 block text-base",children:["Types ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsxs("button",{onClick:d,className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-primaryBlue/80",children:[e.jsx(Ae,{className:"h-4 w-4"}),"Add Type"]})]}),e.jsx("div",{className:"space-y-4",children:H.types.map((n,x)=>e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center gap-2",children:[e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-base focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:n.name,onChange:O=>o(x,O.target.value),placeholder:"Enter type name"}),e.jsx("button",{onClick:()=>s(x,n==null?void 0:n.club_sport_type_id),className:"text-gray-400 hover:text-gray-600",children:e.jsx(Ze,{className:"h-5 w-5"})})]}),e.jsxs("div",{className:"ml-4",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Sub-types"," ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsxs("button",{onClick:()=>p(x),className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-primaryBlue/80",children:[e.jsx(Ae,{className:"h-4 w-4"}),"Add Sub-type"]})]}),e.jsx("div",{className:"space-y-2",children:n.sub_type.map((O,B)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:O,onChange:G=>F(x,B,G.target.value),placeholder:"Enter sub-type name"}),e.jsx("button",{onClick:()=>_(x,B),className:"text-gray-400 hover:text-gray-600",children:e.jsx(Ze,{className:"h-5 w-5"})})]},B))})]})]},n.club_sport_type_id||x))})]})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(se,{onClick:oe,className:"w-full rounded-xl border border-gray-200 bg-white py-3 text-base font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx(se,{onClick:E,loading:w,className:"w-full rounded-xl bg-primaryGreen py-3 text-base font-medium text-white hover:bg-primaryGreen/90",children:U==="add"?"Add Sport":"Save Changes"})]})]})}),e.jsx(se,{className:`w-full rounded-xl py-3 text-white ${q?"bg-primaryGreen hover:bg-primaryGreen/90":"cursor-not-allowed bg-gray-400"}`,onClick:J,loading:y,disabled:!q,children:"Continue"})]})}const Ns=["00:00:00","01:00:00","02:00:00","03:00:00","04:00:00","05:00:00","06:00:00","07:00:00","08:00:00","09:00:00","10:00:00","11:00:00","12:00:00","13:00:00","14:00:00","15:00:00","16:00:00","17:00:00","18:00:00","19:00:00","20:00:00","21:00:00","22:00:00","23:00:00"],_s=R=>{const[h,S]=R.split(":"),v=parseInt(h,10),l=v>=12?"PM":"AM";return`${v%12||12}:${S} ${l}`},ve=({label:R,value:h,onChange:S,timeOptions:v=Ns,minTime:l=null})=>{const t=l?v.filter(b=>{const[j]=l.split(":"),[y]=b.split(":");return parseInt(y)>parseInt(j)}):v;return e.jsxs("div",{className:"flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm",children:[e.jsx("div",{className:"gap-2 self-stretch whitespace-nowrap bg-slate-50 px-2 py-2.5 text-neutral-400",children:R}),e.jsxs("select",{className:"flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0",value:h,onChange:S,children:[e.jsx("option",{value:"",children:"Select time"}),t.map(b=>e.jsx("option",{value:b,children:_s(b)},b))]})]})};function Ss({onNext:R,register:h,errors:S,setValue:v,defaultValues:l,isSubmitting:t}){const[b,j]=a.useState(()=>l!=null&&l.days_off?Array.isArray(l.days_off)?l.days_off:[]:[]),[y,C]=a.useState(()=>(l==null?void 0:l.times.length)>0?l.times:[{from:"",until:""}]),[m,T]=a.useState(()=>{var w;return((w=l==null?void 0:l.max_players)==null?void 0:w.toString())||""});console.log("time slots",y),a.useEffect(()=>{l&&(l.days_off&&j(Array.isArray(l.days_off)?l.days_off:[]),l.opening_time&&l.closing_time&&C([{from:l.opening_time,until:l.closing_time}]),l.max_players&&T(l.max_players.toString()))},[l]);const[D,I]=a.useState(!1),U=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],z=(w,u)=>{j(u?c=>c.filter(H=>H!==w):c=>[...c,w])},ee=()=>{v("days_off",b),v("times",y),v("max_players",Number(m)),R()},Y=()=>{C(w=>[...w,{from:"",until:""}])},X=w=>{C(u=>u.filter((c,N)=>N!==w))},A=(w,u,c)=>{C(N=>{const H=[...N];return H[w]={...H[w],[u]:c},H})};return e.jsx("div",{className:"flex min-h-screen items-center justify-center",children:e.jsxs("div",{className:"w-full max-w-xl rounded-2xl bg-white p-8 shadow-lg",children:[e.jsxs("div",{className:"mb-6 flex flex-col items-center justify-center gap-4",children:[e.jsx("div",{children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_25940)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_25940)"}),e.jsxs("g",{filter:"url(#filter0_d_397_25940)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M48.25 27.9375C48.25 26.7294 47.2706 25.75 46.0625 25.75H27.9375C26.7294 25.75 25.75 26.7294 25.75 27.9375V46.0625C25.75 47.2706 26.7294 48.25 27.9375 48.25H46.0625C47.2706 48.25 48.25 47.2706 48.25 46.0625V27.9375ZM33.25 32C33.25 32.8629 32.5504 33.5625 31.6875 33.5625C30.8246 33.5625 30.125 32.8629 30.125 32C30.125 31.1371 30.8246 30.4375 31.6875 30.4375C32.5504 30.4375 33.25 31.1371 33.25 32ZM33.25 37C33.25 37.8629 32.5504 38.5625 31.6875 38.5625C30.8246 38.5625 30.125 37.8629 30.125 37C30.125 36.1371 30.8246 35.4375 31.6875 35.4375C32.5504 35.4375 33.25 36.1371 33.25 37ZM31.6875 43.5625C32.5504 43.5625 33.25 42.8629 33.25 42C33.25 41.1371 32.5504 40.4375 31.6875 40.4375C30.8246 40.4375 30.125 41.1371 30.125 42C30.125 42.8629 30.8246 43.5625 31.6875 43.5625Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_25940",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_25940"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_25940",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_25940",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_25940",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsx("p",{className:"text-2xl font-medium",children:"Other details"})]}),e.jsx("p",{className:"mb-2",children:"Opening hours for:"}),e.jsx("div",{className:"mb-6 flex flex-wrap justify-start gap-4",children:U.map(w=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",className:"h-5 w-5 rounded-md border-gray-300 text-primaryGreen focus:ring-primaryGreen",checked:!b.includes(w),onChange:u=>z(w,u.target.checked)}),e.jsx("label",{className:"",children:w})]},w))}),y.map((w,u)=>e.jsxs("div",{className:"mb-5 flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 gap-4",children:[e.jsx(ve,{label:"From",timeOptions:Ye,value:w.from,onChange:c=>A(u,"from",c.target.value)}),e.jsx(ve,{label:"Until",timeOptions:We(w.from),value:w.until,onChange:c=>A(u,"until",c.target.value),disabled:!w.from})]}),y.length>1&&e.jsx("button",{onClick:()=>X(u),className:" text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]},u)),e.jsx("button",{onClick:Y,className:"mb-5 text-primaryBlue underline hover:text-primaryBlue/80",children:"+Add another time slot"}),e.jsxs("div",{className:"mb-5",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("p",{children:"Max number of players for a normal lesson:"}),e.jsxs("div",{className:"relative",onMouseEnter:()=>I(!0),onMouseLeave:()=>I(!1),children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10 16.25C13.4518 16.25 16.25 13.4518 16.25 10C16.25 6.54822 13.4518 3.75 10 3.75C6.54822 3.75 3.75 6.54822 3.75 10C3.75 13.4518 6.54822 16.25 10 16.25ZM11.1158 13.2086L11.2156 12.8006C11.164 12.8249 11.0807 12.8526 10.9665 12.8841C10.852 12.9157 10.7489 12.9318 10.6583 12.9318C10.4654 12.9318 10.3295 12.9001 10.2507 12.8366C10.1724 12.773 10.1333 12.6534 10.1333 12.4783C10.1333 12.4089 10.1451 12.3054 10.1697 12.17C10.1936 12.0337 10.2211 11.9126 10.2516 11.8067L10.6242 10.4876C10.6607 10.3665 10.6857 10.2334 10.6992 10.0882C10.7129 9.94325 10.7193 9.84185 10.7193 9.78429C10.7193 9.50614 10.6218 9.28041 10.4268 9.10629C10.2317 8.93229 9.95393 8.84529 9.59396 8.84529C9.39365 8.84529 9.18188 8.88088 8.95776 8.952C8.73363 9.02294 8.49933 9.1084 8.25421 9.2082L8.15415 9.6165C8.22719 9.58949 8.31419 9.56043 8.41598 9.53034C8.51732 9.50038 8.61674 9.48489 8.71347 9.48489C8.91096 9.48489 9.04399 9.51856 9.1137 9.58488C9.18342 9.65139 9.21844 9.7697 9.21844 9.93883C9.21844 10.0324 9.20736 10.1363 9.18438 10.2492C9.16172 10.3628 9.13342 10.483 9.10013 10.6098L8.72595 11.9342C8.69266 12.0734 8.66834 12.1979 8.65304 12.3084C8.63786 12.4189 8.63057 12.5272 8.63057 12.6326C8.63057 12.9048 8.73114 13.1292 8.93222 13.3063C9.13329 13.4826 9.41523 13.5714 9.77769 13.5714C10.0137 13.5714 10.2209 13.5406 10.3992 13.4785C10.5773 13.4167 10.8164 13.3268 11.1158 13.2086ZM11.0495 7.8502C11.2235 7.68882 11.3101 7.49254 11.3101 7.26272C11.3101 7.03341 11.2236 6.83675 11.0495 6.67331C10.8758 6.51032 10.6666 6.42857 10.4219 6.42857C10.1765 6.42857 9.96635 6.51013 9.79107 6.67331C9.61579 6.83675 9.52796 7.03334 9.52796 7.26272C9.52796 7.49254 9.61579 7.68875 9.79107 7.8502C9.96667 8.01217 10.1764 8.09321 10.4219 8.09321C10.6666 8.09321 10.8758 8.01217 11.0495 7.8502Z",fill:"#CDD0D5"})}),D&&e.jsx("div",{className:"absolute z-10 -translate-x-1/2 transform",children:e.jsx("div",{className:"mt-2 w-[400px] rounded-lg bg-white p-4 shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsx("p",{className:"text-sm text-gray-600",children:"This is the maximum number of players that can be booked with the “find by coach” and “find by time” options in the lesson section. If the booking party exceeds this number, they cannot outright book the lesson, and will need to submit a request through the “custom request” section. This ensures that coaches can effectively manage group sizes, while also allowing flexibility for larger parties through the “custom request” process."})})})]})]}),e.jsx("input",{type:"text",className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2",value:m,onChange:w=>T(w.target.value)})]}),e.jsx(se,{className:"w-full rounded-xl bg-primaryGreen py-3 text-white",onClick:ee,loading:t,children:"Continue"})]})})}let Fe=new ge;const ks=R=>{const h={};return R&&R.forEach(S=>{S.sport_id&&(h[S.sport_id]||(h[S.sport_id]={total:0,types:{}}),h[S.sport_id].total+=1,S.type&&(h[S.sport_id].types[S.type]||(h[S.sport_id].types[S.type]=0),h[S.sport_id].types[S.type]+=1))}),h};function Ms({onNext:R,register:h,errors:S,values:v,setValue:l,watch:t,defaultValues:b,isSubmitting:j}){var oe;console.log("default values",b);const[y,C]=a.useState(!1),m=a.useMemo(()=>{var d;return(d=b==null?void 0:b.sports)==null?void 0:d.map(s=>({...s,sport_types:s.sport_types||[]}))},[b==null?void 0:b.sports]),[T,D]=a.useState(()=>{var d;return((d=v==null?void 0:v.courts)==null?void 0:d.length)>0?v.courts.map(s=>({...s})):[]}),[I,U]=a.useState(null),z=a.useRef(),[ee,Y]=a.useState(!((oe=v==null?void 0:v.courts)!=null&&oe.length)),[X,A]=a.useState(""),[w,u]=a.useState({}),c=t("courts"),[N,H]=a.useState({}),P=()=>{const d=parseInt(X);if(d>0){const s=Array.from({length:d},(o,p)=>({sport_id:null,name:`Court ${p+1}`,type:null,sub_type:null}));D(s),l("courts",s),Y(!1)}};a.useEffect(()=>{var d;if(((d=v==null?void 0:v.courts)==null?void 0:d.length)>0&&!T.length){const s=v.courts.map(o=>({...o}));D(s),l("courts",s),Y(!1)}},[v]),a.useEffect(()=>{const d=s=>{z.current&&!z.current.contains(s.target)&&U(null)};return document.addEventListener("mousedown",d),()=>document.removeEventListener("mousedown",d)},[]);const r=()=>{const d={sport_id:null,name:`Court ${((c==null?void 0:c.length)||0)+1}`,type:null,sub_type:null};l("courts",[...c||[],d])},M=async(d,s)=>{C(!0);try{if(s!=null&&s.id){Fe.setTable("club_court");const o=await Fe.callRestAPI({id:s==null?void 0:s.id},"DELETE");l("courts",c.filter((p,_)=>_!==d))}else l("courts",c.filter((o,p)=>p!==d))}catch(o){console.log(o)}finally{C(!1)}},k=d=>{U(I===d?null:d)},$=(d,s)=>{var F,E;const o=parseInt(s),p=(F=m==null?void 0:m.filter(J=>J.status!==0))==null?void 0:F.find(J=>J.id===o),_=(E=p==null?void 0:p.sport_types)==null?void 0:E.some(J=>J.type);l("courts",c.map((J,q)=>q===d?{...J,sport_id:o,type:null,sub_type:null,hasTypes:_}:J))},W=(d,s)=>{l("courts",c.map((o,p)=>p===d?{...o,type:s,sub_type:null}:o))},Q=(d,s)=>{l("courts",c.map((o,p)=>p===d?{...o,sub_type:s}:o))};a.useEffect(()=>{T.length>0&&!c&&l("courts",T)},[T,l,c]),a.useEffect(()=>{if((c==null?void 0:c.length)>=0){const d=ks(c);u(d)}},[c]);const ie=d=>{d.preventDefault();const s={};let o=!1;c==null||c.forEach((p,_)=>{var F,E,J;if(p.sport_id||(s[`courts.${_}.sport_id`]="Sport is required",o=!0),p.sport_id){const q=m==null?void 0:m.find(n=>n.id===p.sport_id);if(((F=q==null?void 0:q.sport_types)==null?void 0:F.some(n=>n.type))&&!p.type&&(s[`courts.${_}.type`]="Type is required for this sport",o=!0),p.type){const n=(E=q==null?void 0:q.sport_types)==null?void 0:E.find(x=>x.type===p.type);((J=n==null?void 0:n.subtype)==null?void 0:J.length)>0&&!p.sub_type&&(s[`courts.${_}.sub_type`]="Sub-type is required for this type",o=!0)}}}),H(s),o||R()};return e.jsxs("div",{className:"w-full max-w-xl",children:[y&&e.jsx(Ce,{}),ee&&e.jsx("div",{className:"fixed inset-0 z-50 flex min-h-screen items-center justify-center bg-black/30 p-4",children:e.jsxs("div",{className:"w-full max-w-lg rounded-3xl bg-white p-8",children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:"Number of courts/spaces"}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("label",{className:"mb-4  flex items-center  text-base",children:[e.jsx("span",{children:"How many courts/spaces does your club have"}),e.jsx("span",{className:"inline-block",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10 16.25C13.4518 16.25 16.25 13.4518 16.25 10C16.25 6.54822 13.4518 3.75 10 3.75C6.54822 3.75 3.75 6.54822 3.75 10C3.75 13.4518 6.54822 16.25 10 16.25ZM11.1158 13.2086L11.2156 12.8006C11.164 12.8249 11.0807 12.8526 10.9665 12.8841C10.852 12.9157 10.7489 12.9318 10.6583 12.9318C10.4654 12.9318 10.3295 12.9001 10.2507 12.8366C10.1724 12.773 10.1333 12.6534 10.1333 12.4783C10.1333 12.4089 10.1451 12.3054 10.1697 12.17C10.1936 12.0337 10.2211 11.9126 10.2516 11.8067L10.6242 10.4876C10.6607 10.3665 10.6857 10.2334 10.6992 10.0882C10.7129 9.94325 10.7193 9.84185 10.7193 9.78429C10.7193 9.50614 10.6218 9.28041 10.4268 9.10629C10.2317 8.93229 9.95393 8.84529 9.59396 8.84529C9.39365 8.84529 9.18188 8.88088 8.95776 8.952C8.73363 9.02294 8.49933 9.1084 8.25421 9.2082L8.15415 9.6165C8.22719 9.58949 8.31419 9.56043 8.41598 9.53034C8.51732 9.50038 8.61674 9.48489 8.71347 9.48489C8.91096 9.48489 9.04399 9.51856 9.1137 9.58488C9.18342 9.65139 9.21844 9.7697 9.21844 9.93883C9.21844 10.0324 9.20736 10.1363 9.18438 10.2492C9.16172 10.3628 9.13342 10.483 9.10013 10.6098L8.72595 11.9342C8.69266 12.0734 8.66834 12.1979 8.65304 12.3084C8.63786 12.4189 8.63057 12.5272 8.63057 12.6326C8.63057 12.9048 8.73114 13.1292 8.93222 13.3063C9.13329 13.4826 9.41523 13.5714 9.77769 13.5714C10.0137 13.5714 10.2209 13.5406 10.3992 13.4785C10.5773 13.4167 10.8164 13.3268 11.1158 13.2086ZM11.0495 7.8502C11.2235 7.68882 11.3101 7.49254 11.3101 7.26272C11.3101 7.03341 11.2236 6.83675 11.0495 6.67331C10.8758 6.51032 10.6666 6.42857 10.4219 6.42857C10.1765 6.42857 9.96635 6.51013 9.79107 6.67331C9.61579 6.83675 9.52796 7.03334 9.52796 7.26272C9.52796 7.49254 9.61579 7.68875 9.79107 7.8502C9.96667 8.01217 10.1764 8.09321 10.4219 8.09321C10.6666 8.09321 10.8758 8.01217 11.0495 7.8502Z",fill:"#CDD0D5"})})})]}),e.jsx("input",{type:"number",className:"mb-4 w-full rounded-xl border border-gray-200 px-4 py-3 text-base focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:X,onChange:d=>A(d.target.value),placeholder:"Enter number of courts"})]}),e.jsx(se,{onClick:P,className:"w-full rounded-xl bg-primaryGreen py-3 text-base font-medium text-white hover:bg-primaryGreen/90",children:"Continue"})]})}),c==null?void 0:c.map((d,s)=>{var o,p,_,F,E,J,q,le,n,x,O,B,G,te,de,me,he,ue;return e.jsxs("div",{className:"mb-4 rounded-xl bg-[#F6F8FA] p-5",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsxs("span",{className:"text-base font-medium",children:["Space ",s+1]}),e.jsxs("div",{className:"relative",ref:z,children:[e.jsx("button",{className:"text-sm text-gray-500",onClick:()=>k(s),children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.0013 10.8337C10.4615 10.8337 10.8346 10.4606 10.8346 10.0003C10.8346 9.54009 10.4615 9.16699 10.0013 9.16699C9.54107 9.16699 9.16797 9.54009 9.16797 10.0003C9.16797 10.4606 9.54107 10.8337 10.0013 10.8337Z",fill:"#868C98"}),e.jsx("path",{d:"M16.8763 10.8337C17.3365 10.8337 17.7096 10.4606 17.7096 10.0003C17.7096 9.54009 17.3365 9.16699 16.8763 9.16699C16.4161 9.16699 16.043 9.54009 16.043 10.0003C16.043 10.4606 16.4161 10.8337 16.8763 10.8337Z",fill:"#868C98"}),e.jsx("path",{d:"M3.1263 10.8337C3.58654 10.8337 3.95964 10.4606 3.95964 10.0003C3.95964 9.54009 3.58654 9.16699 3.1263 9.16699C2.66606 9.16699 2.29297 9.54009 2.29297 10.0003C2.29297 10.4606 2.66606 10.8337 3.1263 10.8337Z",fill:"#868C98"}),e.jsx("path",{d:"M10.0013 10.8337C10.4615 10.8337 10.8346 10.4606 10.8346 10.0003C10.8346 9.54009 10.4615 9.16699 10.0013 9.16699C9.54107 9.16699 9.16797 9.54009 9.16797 10.0003C9.16797 10.4606 9.54107 10.8337 10.0013 10.8337Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M16.8763 10.8337C17.3365 10.8337 17.7096 10.4606 17.7096 10.0003C17.7096 9.54009 17.3365 9.16699 16.8763 9.16699C16.4161 9.16699 16.043 9.54009 16.043 10.0003C16.043 10.4606 16.4161 10.8337 16.8763 10.8337Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M3.1263 10.8337C3.58654 10.8337 3.95964 10.4606 3.95964 10.0003C3.95964 9.54009 3.58654 9.16699 3.1263 9.16699C2.66606 9.16699 2.29297 9.54009 2.29297 10.0003C2.29297 10.4606 2.66606 10.8337 3.1263 10.8337Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}),I===s&&e.jsx("div",{className:"absolute right-0 z-10 mt-2 w-48 rounded-lg bg-white py-2 shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsxs("button",{className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-50",onClick:()=>{M(s,d),U(null)},children:[e.jsxs("svg",{className:"mr-2 h-4 w-4",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M3 6H5H21",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Delete court"]})})]})]}),e.jsxs("div",{className:"space-y-4 rounded-xl bg-white p-4 ",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"w-full",children:[e.jsx("label",{className:"mb-2 block",children:"Court name"}),e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-300 px-3 py-2",...h(`courts.${s}.name`),defaultValue:d.name})]})}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block",children:["Sport ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"flex flex-wrap gap-4",children:(o=m==null?void 0:m.filter(L=>L.status!==0))==null?void 0:o.map(L=>e.jsxs("label",{className:"flex items-center gap-1 capitalize",children:[e.jsx("input",{type:"radio",name:`courts.${s}.sport_id`,value:L.id,checked:d.sport_id===L.id,className:"h-5 w-5",onChange:pe=>$(s,pe.target.value),required:!0}),L.name," "]},L.id))}),N[`courts.${s}.sport_id`]&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:N[`courts.${s}.sport_id`]})]}),d.sport_id&&((F=(_=(p=m==null?void 0:m.filter(L=>L.status!==0))==null?void 0:p.find(L=>L.id===d.sport_id))==null?void 0:_.sport_types)==null?void 0:F.length)>0&&e.jsxs("div",{children:[((q=(J=(E=m==null?void 0:m.find(L=>L.id===d.sport_id))==null?void 0:E.sport_types)==null?void 0:J.filter(L=>L.type))==null?void 0:q.length)>0&&e.jsxs("label",{className:"mb-2 block",children:["Type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"flex flex-wrap gap-4",children:(x=(n=(le=m==null?void 0:m.find(L=>L.id===d.sport_id))==null?void 0:le.sport_types)==null?void 0:n.filter(L=>L.type))==null?void 0:x.map((L,pe)=>e.jsxs("label",{className:"flex items-center gap-2 capitalize",children:[e.jsx("input",{type:"radio",name:`courts.${s}.type`,value:L.type,checked:d.type===L.type,className:"h-5 w-5",onChange:_e=>W(s,_e.target.value),required:!0}),L.type]},`${s}-${pe}`))}),N[`courts.${s}.type`]&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:N[`courts.${s}.type`]})]}),d.sport_id&&d.type&&e.jsx("div",{children:((te=(G=(B=(O=m==null?void 0:m.find(L=>L.id===d.sport_id))==null?void 0:O.sport_types)==null?void 0:B.find(L=>L.type===d.type))==null?void 0:G.subtype)==null?void 0:te.length)>0&&e.jsxs(e.Fragment,{children:[e.jsxs("label",{className:"mb-2 block",children:["Sub-type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"flex flex-wrap gap-4",children:(ue=(he=(me=(de=m==null?void 0:m.find(L=>L.id==d.sport_id))==null?void 0:de.sport_types)==null?void 0:me.find(L=>L.type===d.type))==null?void 0:he.subtype)==null?void 0:ue.map(L=>e.jsxs("label",{className:"flex items-center gap-2 capitalize",children:[e.jsx("input",{type:"radio",name:`courts.${s}.sub_type`,value:L,checked:d.sub_type===L,className:"h-5 w-5",onChange:pe=>Q(s,pe.target.value),required:!0}),L]},`${s}-${L}`))}),N[`courts.${s}.sub_type`]&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:N[`courts.${s}.sub_type`]})]})})]})]},s)}),e.jsx("button",{className:"mb-6 text-blue-600 underline",onClick:r,type:"button",children:"+ Add another court/space"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("p",{className:"mb-4 text-lg font-medium",children:"Summary:"}),e.jsxs("div",{className:"mb-4 space-y-2 rounded-xl bg-gray-50 p-4",children:[Object.entries(w).map(([d,s])=>{const o=m==null?void 0:m.find(F=>F.id===parseInt(d)),p=c==null?void 0:c.filter(F=>F.sport_id===parseInt(d)),_=[...new Set(p==null?void 0:p.map(F=>F.sub_type).filter(Boolean))];return e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("span",{children:[(o==null?void 0:o.name)||"Unknown Sport"," ",e.jsx("span",{className:"text-sm capitalize text-gray-500",children:_.length>0?`(${_.join(", ")})`:""})]}),e.jsxs("div",{className:"flex gap-4",children:[Object.entries(s.types).map(([F,E])=>e.jsxs("span",{className:"flex items-center gap-2 capitalize",children:[F," ",e.jsx("span",{className:"rounded-full bg-blue-100 px-2 py-1",children:E})]},F)),Object.keys(s.types).length===0&&e.jsx("span",{className:"rounded-full bg-blue-100 px-2 py-1",children:s.total})]})]},d)}),e.jsxs("div",{className:"mt-4 flex justify-between border-t border-gray-200 pt-4",children:[e.jsx("span",{children:"Courts/spaces total"}),e.jsx("span",{className:"rounded-full bg-blue-100 px-2 py-1",children:(c==null?void 0:c.length)||0})]})]})]}),e.jsx(se,{className:"w-full rounded-xl bg-[#176448] py-3 text-white disabled:opacity-50",onClick:ie,loading:j,type:"button",children:"Continue"})]})}const Oe=new ge;function Es({onNext:R,stripeConnectionData:h,isSubmitting:S,setStripeConnectionData:v}){const[l]=we.useState(S||!1),[t,b]=a.useState(!1),[j,y]=a.useState(!1),C=localStorage.getItem("role"),m=async()=>{try{const I=await Oe.callRawAPI(`/v3/api/custom/courtmatchup/${C}/stripe/account/verify`,{},"POST");return v&&v(I),I}catch(I){return console.error("Error checking Stripe connection:",I),!1}},T=async()=>{b(!0);try{const I=await Oe.callRawAPI(`/v3/api/custom/courtmatchup/${C}/stripe/onboarding`,{},"POST");I&&I.url&&window.open(I.url,"_blank")}catch(I){console.error("Error connecting to Stripe:",I)}b(!1)};a.useEffect(()=>{if(t===!1){const I=setTimeout(()=>{m()},2e3);return()=>clearTimeout(I)}},[t]);const D=async()=>{y(!0),await R(),y(!1)};return e.jsx("div",{className:"flex flex-col bg-white pb-7",children:e.jsxs("section",{className:"flex w-[432px] max-w-full flex-col justify-center",children:[e.jsx("div",{className:"flex w-full max-w-[432px] flex-col self-center max-md:max-w-full",children:e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3",children:[e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_26852)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_26852)"}),e.jsxs("g",{filter:"url(#filter0_d_397_26852)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M47.3125 34.4999H47V42.7054C47.6877 42.8975 48.2531 43.4195 48.4868 44.1207L48.9035 45.3707C49.3756 46.7872 48.3213 48.2499 46.8282 48.2499H27.1718C25.6787 48.2499 24.6244 46.7872 25.0965 45.3707L25.5132 44.1207C25.7469 43.4195 26.3123 42.8975 27 42.7054V34.4999H26.6875C25.4794 34.4999 24.5 33.5206 24.5 32.3124V31.7352C24.5 30.9099 24.9645 30.1549 25.7011 29.7828L36.0136 24.5729C36.6339 24.2596 37.3661 24.2596 37.9864 24.5729L48.2989 29.7828C49.0355 30.1549 49.5 30.9099 49.5 31.7352V32.3124C49.5 33.5206 48.5206 34.4999 47.3125 34.4999ZM42 34.4999H45.125V42.6249H42V34.4999ZM32 42.6249H28.875V34.4999H32V42.6249ZM33.875 42.6249V34.4999H40.125V42.6249H33.875Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_26852",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_26852"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_26852",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_26852",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_26852",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]}),e.jsx("p",{className:"w-full text-center text-2xl font-medium leading-none text-gray-950 max-md:max-w-full",children:"Connect stripe"})]})}),e.jsxs("div",{className:"mt-10 flex w-full flex-col gap-4 self-center max-md:max-w-full",children:[((h==null?void 0:h.complete)||(h==null?void 0:h.details_submitted))&&e.jsxs("div",{className:"flex flex-col gap-4 rounded-lg border border-green-200 bg-green-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-lg font-medium text-gray-900",children:h!=null&&h.complete?"Stripe account connected":"Stripe account details submitted"})]}),e.jsxs("div",{className:"mt-2 grid grid-cols-1 gap-3",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-xs text-gray-500",children:"Account ID"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:h.account_id})]}),e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${h.complete?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Complete"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${h.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${h.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]})]}),e.jsx("div",{className:"mt-2 text-sm text-gray-600",children:h!=null&&h.complete?"You can now receive payments from your club members.":"Your Stripe account details have been submitted and are pending approval. You can continue with the setup process."}),e.jsx(se,{onClick:D,className:"mt-4 w-full rounded-xl bg-primaryGreen px-4 py-3 text-sm font-medium text-white",loading:l||j,children:l?"Processing...":"Continue"})]}),!(h!=null&&h.complete||h!=null&&h.details_submitted)&&e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"rounded-lg border border-yellow-200 bg-yellow-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-yellow-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"No Stripe account connected"})]}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Connect your Stripe account to pay your club staffs and coaches. This is required for processing payments on the platform."}),h&&e.jsxs("div",{className:"mt-3 grid grid-cols-1 gap-2",children:[e.jsx("div",{className:"text-xs text-gray-500",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${h.complete?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Complete"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${h.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${h.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]})]}),e.jsx("button",{onClick:T,className:"w-full rounded-xl bg-primaryBlue px-4 py-3 text-sm font-medium text-white",disabled:t,children:t?"Connecting...":"Connect Stripe Account"})]})]})]})})}const Ue=({label:R,value:h,onChange:S})=>e.jsxs("div",{className:"flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm",children:[e.jsx("div",{className:"flex w-10 items-center justify-center gap-2 self-stretch whitespace-nowrap bg-slate-50 px-2 py-2.5 text-neutral-400",children:"$"}),e.jsx("input",{className:"flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0",value:h,type:"number",onChange:S})]});function Ls({handleSave:R,setValue:h,setPricingRows:S,pricingRows:v,editingPricing:l=null,values:t,isSubmitting:b,sportsOffered:j}){var c,N,H,P;const[y,C]=a.useState(()=>{var r;return l?{sport_id:l.sport_id,type:l.type,sub_type:l.sub_type||"",is_general:l.is_general||!1,general_rate:l.general_rate||"",price_by_hours:(r=l==null?void 0:l.price_by_hours)==null?void 0:r.map(M=>({start_time:M.start_time,end_time:M.end_time,rate:M.rate,showInput:!1})),showSportInput:!1,showTypeInput:!1}:{sport_id:"",type:"",sub_type:"",is_general:!1,general_rate:"",price_by_hours:[{start_time:"",end_time:"",rate:"",showInput:!0}],showSportInput:!0,showTypeInput:!0}}),{dispatch:m}=a.useContext(Ne),T=j==null?void 0:j.find(r=>r.id==y.sport_id),D=((c=T==null?void 0:T.sport_types)==null?void 0:c.filter(r=>r.type))||[],I=((H=(N=T==null?void 0:T.sport_types)==null?void 0:N.find(r=>r.type===y.type))==null?void 0:H.subtype)||[],U=()=>{const r=y;if(r.is_general){if(!r.general_rate){V(m,"Please enter a general rate",3e3,"warning");return}}else{if(r.price_by_hours.length===0){V(m,"Please add pricing details",3e3,"warning");return}if(r.price_by_hours.some($=>!$.start_time||!$.end_time||!$.rate)){V(m,"Please fill all time slot details",3e3,"warning");return}}const M={sport_id:r.sport_id,type:r.type,sub_type:r.sub_type,is_general:r.is_general,general_rate:r.is_general?r.general_rate:null,price_by_hours:r.is_general?[{rate:r.general_rate}]:r.price_by_hours.map(k=>({start_time:k.start_time,end_time:k.end_time,rate:k.rate}))};S(l?k=>k.map(($,W)=>W===l.index?M:$):k=>[...k,M]),C({sport_id:"",type:"",sub_type:"",is_general:!1,general_rate:"",price_by_hours:[{start_time:"",end_time:"",rate:"",showInput:!0}],showSportInput:!0,showTypeInput:!0})},z=(r,M)=>{var $;const k=(($=M==null?void 0:M.target)==null?void 0:$.value)||M;C(W=>({...W,[r]:k}))},ee=()=>{C(r=>({...r,showSportInput:!1}))},Y=()=>{C(r=>({...r,showSportInput:!0}))},X=(r,M,k)=>{var W;const $=((W=k==null?void 0:k.target)==null?void 0:W.value)||k;C(Q=>({...Q,price_by_hours:Q.price_by_hours.map((ie,oe)=>oe===r?{...ie,[M]:$}:ie)}))},A=()=>{C(r=>({...r,price_by_hours:[...r.price_by_hours,{start_time:"",end_time:"",rate:"",showInput:!0}]}))};console.log({sportsOffered:j});const w=r=>{const M=y.price_by_hours[r];if(!M.start_time||!M.end_time||!M.rate){V(m,"Please fill all hours and rate fields",3e3,"warning");return}C(k=>({...k,price_by_hours:k.price_by_hours.map(($,W)=>W===r?{...$,showInput:!1}:$)}))},u=r=>{C(M=>({...M,price_by_hours:M.price_by_hours.map((k,$)=>$===r?{...k,showInput:!0}:k)}))};return e.jsx("div",{className:"space-y-5",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"mb-6 divide-y",children:[e.jsx("div",{className:"py-4",children:y.showSportInput?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("label",{className:"text-base font-medium text-gray-900",children:["Sport"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsx("button",{onClick:()=>C(r=>({...r,showSportInput:!1})),className:"text-sm text-primaryBlue hover:text-blue-700",children:"Cancel"})]}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-300 px-3 py-2 capitalize",value:y.sport_id,onChange:r=>{C(M=>({...M,sport_id:r.target.value,type:"",sub_type:""}))},children:[e.jsx("option",{value:"",children:"-All sports-"}),(P=j==null?void 0:j.filter(r=>r.status===1))==null?void 0:P.map(r=>e.jsx("option",{value:r.id,children:r.name},r.id))]}),y.sport_id&&D.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Type"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-300 px-3 py-2 capitalize",value:y.type,onChange:r=>{z("type",r.target.value),C(M=>({...M,type:r.target.value,sub_type:""}))},children:[e.jsx("option",{value:"",children:"-Select Type-"}),D.map(r=>e.jsx("option",{value:r.type,children:r.type},r.type))]})]}),y.sport_id&&y.type&&I.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Sub-type"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-300 px-3 py-2 capitalize",value:y.sub_type,onChange:r=>z("sub_type",r.target.value),children:[e.jsx("option",{value:"",children:"-Select Sub-type-"}),I.map(r=>e.jsx("option",{value:r,children:r},r))]})]}),e.jsx("button",{onClick:ee,className:"mt-4 w-full rounded-lg bg-primaryBlue px-4 py-2 text-sm text-white hover:bg-blue-700",children:"Save"})]}):e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("h3",{className:"text-base font-medium text-gray-900",children:["Sport"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsx("p",{className:"text-sm capitalize text-gray-500",children:(T==null?void 0:T.name)||"All sports"})]}),e.jsx("button",{onClick:Y,className:"text-sm text-primaryBlue hover:text-blue-700",children:"Edit"})]})}),D.length>0&&e.jsx("div",{className:"py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("h3",{className:"text-base font-medium capitalize text-gray-900",children:["Type"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsx("p",{className:"text-sm capitalize text-gray-500",children:y.type||"Not set"})]}),e.jsx("button",{onClick:Y,className:"text-sm text-primaryBlue hover:text-blue-700",children:"Edit"})]})}),I.length>0&&e.jsx("div",{className:"py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("h3",{className:"text-base font-medium text-gray-900",children:["Sub type"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsx("p",{className:"text-sm capitalize capitalize text-gray-500",children:y.sub_type||"Not set"})]}),e.jsx("button",{onClick:Y,className:"text-sm text-primaryBlue hover:text-blue-700",children:"Edit"})]})})]}),e.jsxs("div",{className:"mt-6 border-t pt-4",children:[e.jsxs("div",{className:"mb-4 flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",id:"is_general",name:"is_general",checked:y.is_general,onChange:r=>{C({...y,is_general:r.target.checked})},className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("label",{htmlFor:"is_general",className:"text-sm font-medium",children:"General price"}),e.jsxs("div",{className:"group relative",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-4 w-4 text-gray-500",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z",clipRule:"evenodd"})}),e.jsx("div",{className:"absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100",children:"This price will be applied to all sports/types/sub-types for all times of day that are not otherwise defined."})]})]})]}),y.is_general?e.jsx("div",{className:"mb-6 space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsx("div",{className:"flex w-full max-w-[300px]",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"General rate per hour"}),e.jsx("div",{className:"relative",children:e.jsx(Ue,{value:y.general_rate,onChange:r=>C({...y,general_rate:r.target.value})})})]})})}):e.jsxs("div",{className:"space-y-2",children:[y.price_by_hours.map((r,M)=>e.jsx("div",{className:"space-y-4",children:r.showInput?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Hours"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx(ve,{label:"Start time",value:r.start_time,onChange:k=>X(M,"start_time",k.target.value)})}),e.jsx("div",{className:"flex-1",children:e.jsx(ve,{label:"End time",value:r.end_time,onChange:k=>X(M,"end_time",k.target.value),minTime:r.start_time})})]})]}),e.jsx("div",{className:"flex w-full max-w-[300px]",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Rate per hour"}),e.jsx("div",{className:"relative",children:e.jsx(Ue,{value:r.rate,onChange:k=>X(M,"rate",k.target.value)})})]})}),e.jsx("div",{className:"flex gap-4",children:e.jsx("button",{onClick:()=>w(M),className:"rounded-lg bg-primaryBlue px-4 py-2 text-sm text-white",children:"Save"})})]}):e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Hours"}),e.jsxs("div",{className:"text-sm text-gray-900",children:[je(r.start_time)," -"," ",je(r.end_time)]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Rate"}),e.jsxs("div",{className:"text-sm text-gray-900",children:["$",r.rate,"/hour"]})]})]}),e.jsx("button",{onClick:()=>u(M),className:"text-sm text-primaryBlue underline",children:"Edit"})]})})},M)),e.jsx("button",{onClick:A,className:"text-primaryBlue underline underline-offset-2",children:"+ Add pricing"})]})]}),e.jsxs("div",{className:"mb-3 flex gap-4 border-b pb-4",children:[e.jsx("div",{className:"rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500",children:"Cancel"}),e.jsx(se,{className:"rounded-lg bg-primaryBlue px-4 py-2 text-sm text-white",onClick:U,loading:b,children:l?"Update":"Save"})]})]})})}function Is({onNext:R,setValue:h,defaultValues:S,onSubmit:v,sportsOffered:l}){const[t,b]=a.useState([]),[j,y]=a.useState(null),[C,m]=a.useState(!1),[T,D]=a.useState(null),[I,U]=we.useState(!1);we.useEffect(()=>{const u=c=>{j!==null&&!c.target.closest(".dropdown-container")&&y(null)};return document.addEventListener("mousedown",u),()=>{document.removeEventListener("mousedown",u)}},[j]);const z=(u,c)=>{c.stopPropagation(),y(j===u?null:u)},ee=()=>{m(!0)},Y=u=>{m(!1)},X=async()=>{h("pricing",t),U(!0),await v(),U(!1)},A=u=>{b(t.filter((c,N)=>N!==u)),y(null)},w=(u,c)=>{D({...u,index:c}),m(!0),y(null)};return console.log("pricing rows",t),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"w-full max-w-xl p-4",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3",children:[e.jsx("div",{children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_27001)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_27001)"}),e.jsxs("g",{filter:"url(#filter0_d_397_27001)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24.5 37C24.5 30.0964 30.0964 24.5 37 24.5C43.9036 24.5 49.5 30.0964 49.5 37C49.5 43.9036 43.9036 49.5 37 49.5C30.0964 49.5 24.5 43.9036 24.5 37ZM37 28.9965C37.5178 28.9965 37.9375 29.4163 37.9375 29.934V31.0132C39.0083 31.2211 39.9523 31.7812 40.5418 32.5965C40.8452 33.0161 40.751 33.6021 40.3315 33.9055C39.9119 34.2089 39.3258 34.1147 39.0224 33.6952C38.6686 33.2059 37.9339 32.7986 37 32.7986H36.6431C35.3781 32.7986 34.7257 33.5858 34.7257 34.1451V34.2431C34.7257 34.6719 35.0377 35.2054 35.7823 35.5032L38.9141 36.7559C40.1869 37.2651 41.1493 38.3812 41.1493 39.7569C41.1493 41.5317 39.6337 42.7526 37.9375 43.0208V44.066C37.9375 44.5837 37.5178 45.0035 37 45.0035C36.4822 45.0035 36.0625 44.5837 36.0625 44.066V42.9868C34.9917 42.7789 34.0477 42.2188 33.4582 41.4035C33.1548 40.9839 33.249 40.3979 33.6685 40.0945C34.0881 39.7911 34.6742 39.8853 34.9776 40.3048C35.3314 40.7941 36.0661 41.2014 37 41.2014H37.2343C38.567 41.2014 39.2743 40.3703 39.2743 39.7569C39.2743 39.3281 38.9623 38.7946 38.2177 38.4968L35.0859 37.2441C33.8131 36.7349 32.8507 35.6188 32.8507 34.2431V34.1451C32.8507 32.3833 34.3834 31.1896 36.0625 30.9629V29.934C36.0625 29.4163 36.4822 28.9965 37 28.9965Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_27001",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_27001"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_27001",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_27001",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_27001",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsx("p",{className:"mb-6 text-2xl font-semibold",children:"Pricing"})]}),e.jsxs("div",{className:"space-y-4",children:[t==null?void 0:t.map((u,c)=>{var N;return e.jsxs("div",{className:"relative rounded-xl bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[u.sport_id?e.jsx("span",{className:"capitalize",children:((N=l==null?void 0:l.find(H=>H.id==u.sport_id&&H.status===1))==null?void 0:N.name)||""}):e.jsx("span",{className:"text-gray-500",children:"All sports"}),u.type&&u.type!==""&&e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"•"}),e.jsx("span",{className:"capitalize",children:u.type})]}),u.sub_type&&u.sub_type!==""&&e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"•"}),e.jsx("span",{className:"capitalize",children:u.sub_type})]})]}),e.jsxs("div",{className:"dropdown-container relative",children:[e.jsx("button",{className:"text-gray-400 hover:text-gray-600",onClick:H=>z(c,H),children:e.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 110-2 1 1 0 010 2zm7 0a1 1 0 110-2 1 1 0 010 2zm7 0a1 1 0 110-2 1 1 0 010 2z"})})}),j===c&&e.jsx("div",{className:"absolute right-0 top-8 z-10 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",onClick:H=>H.stopPropagation(),children:e.jsxs("div",{className:"py-1",children:[e.jsxs("button",{onClick:()=>w(u,c),className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})}),"Edit"]}),e.jsxs("button",{onClick:()=>A(c),className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})]})})]})]}),e.jsx("div",{className:"mt-2 space-y-2",children:u.is_general?e.jsxs("div",{className:"flex items-center gap-5",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"font-medium text-gray-600",children:"General price"}),e.jsxs("div",{className:"group relative",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-4 w-4 text-gray-500",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z",clipRule:"evenodd"})}),e.jsx("div",{className:"absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100",children:"This price will be applied to all sports/types/sub-types for all times of day that are not otherwise defined."})]})]}),e.jsxs("span",{className:"rounded-full bg-[#CBF5E5] px-3 py-1 font-medium text-primaryGreen",children:["$",u.general_rate,"/H"]})]}):u.price_by_hours.map((H,P)=>e.jsxs("div",{className:"flex items-center gap-5",children:[H.start_time&&H.end_time?e.jsxs("span",{className:"text-gray-600",children:[je(H.start_time)," -"," ",je(H.end_time)]}):e.jsx("span",{className:"text-gray-600",children:"All times"}),e.jsxs("span",{className:"rounded-full bg-[#CBF5E5] px-3 py-1 font-medium text-primaryGreen",children:["$",H.rate,"/H"]})]},P))})]},c)}),(t==null?void 0:t.length)===0&&e.jsxs("button",{onClick:ee,className:"flex w-fit items-center justify-center space-x-2 border-b border-black",children:[e.jsx("span",{className:"text-xl",children:"+"}),e.jsx("span",{children:"Add pricing"})]}),(t==null?void 0:t.length)>0&&e.jsxs("button",{onClick:ee,className:"flex w-fit items-center justify-center space-x-2 border-b border-black",children:[e.jsx("span",{className:"text-xl",children:"+"}),e.jsx("span",{children:"Add another"})]}),e.jsx(se,{onClick:X,loading:I,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Continue"})]})]}),e.jsx(Ke,{isOpen:C,onClose:()=>{m(!1),D(null)},title:T?"Edit pricing":"Add pricing",showFooter:!1,children:e.jsx(Ls,{handleSave:Y,setValue:h,setPricingRows:b,pricingRows:t,editingPricing:T,defaultValues:S,sportsOffered:l})})]})}const be=new ge;function Ct(){const R=es(),{dispatch:h}=a.useContext(Qe),[S,v]=a.useState(0),[l,t]=a.useState(!1),{dispatch:b}=a.useContext(Ne),[j,y]=a.useState(null),[C,m]=a.useState(!1),[T,D]=a.useState([]),[I]=a.useState([]),[U,z]=a.useState(null),ee=localStorage.getItem("role"),{triggerRefetch:Y}=Xe(),X=s=>{var p,_,F;if(!(s!=null&&s.club))return 0;const o=s.sports.some(E=>E.status===1);return s.club.club_location?s.club.splash_screen?o?(p=s.courts)!=null&&p.length?!s.club.account_details||s.club.account_details==='"[]"'?4:(_=s.club.days_off)!=null&&_.length?((F=s.pricing)!=null&&F.length,6):5:3:2:1:0},{register:A,handleSubmit:w,control:u,getValues:c,setValue:N,formState:{errors:H},watch:P}=ts({defaultValues:{courts:[]}}),{fields:r,append:M,remove:k}=rs({control:u,name:"courts"}),$=async()=>{var s;try{const o=await be.callRawAPI("/v3/api/custom/courtmatchup/club/profile",{},"GET");if(((s=o==null?void 0:o.model)==null?void 0:s.club.completed)==1)return R("/club/dashboard"),o.model;if(y(o==null?void 0:o.model),o!=null&&o.model){const{club:p,courts:_=[]}=o.model;let F=null;try{F=p.club_location&&p.club_location.trim()!==""?JSON.parse(p.club_location):null}catch(x){console.error("Error parsing club_location:",x),F=null}let E=[];try{E=p.account_details&&p.account_details!=='"[]"'&&p.account_details.trim()!==""?JSON.parse(p.account_details):[]}catch(x){console.error("Error parsing account_details:",x),E=[]}const J=F?JSON.stringify({lat:F.lat,lng:F.lng,address:F.address||""}):null;D(o.model.sports),N("club_location",J);let q=[];try{q=p.splash_screen&&p.splash_screen.trim()!==""?JSON.parse(p.splash_screen):[]}catch(x){console.error("Error parsing splash_screen:",x),q=[]}N("splash_screen",q),N("sports",o.model.sports);let le=[];try{le=p.times&&p.times.trim()!==""?JSON.parse(p.times):[]}catch(x){console.error("Error parsing times:",x),le=[]}N("times",le),N("max_players",p.max_players),N("account_details",E);let n=[];try{n=p.days_off&&p.days_off.trim()!==""?JSON.parse(p.days_off):[]}catch(x){console.error("Error parsing days_off:",x),n=[]}N("days_off",n),N("courts",_.length?_:[]),N("pricing",o.model.pricing||[]),N("club_logo",p.club_logo),N("name",p.name)}return o.model}catch(o){return Te(h,o.code),V(b,o.message,3e3,"error"),null}},W=async()=>{try{const s=await be.callRawAPI(`/v3/api/custom/courtmatchup/${ee}/stripe/account/verify`,{},"POST");z(s)}catch(s){return console.error("Error checking Stripe connection:",s),!1}},Q=async(s=null)=>{var p;const o=c();m(!0);try{let _={};switch(S){case 0:_.club_location=o.club_location;break;case 1:s?_=s:(_.splash_screen=o.splash_screen,_.club_logo=o.club_logo);break;case 2:break;case 3:_.courts=(p=o.courts)==null?void 0:p.map(E=>({type:E.type,sub_type:E.sub_type,sport_id:E.sport_id,club_sport_id:E==null?void 0:E.sport_id,name:E.name,...(E==null?void 0:E.id)&&{court_id:E.id}}));break;case 4:_.account_details=o.account_details;break;case 5:_.times=o.times,_.days_off=o.days_off,_.max_players=o.max_players;break;case 6:_.pricing=o.pricing,_.completed=1;break}await be.callRawAPI("/v3/api/custom/courtmatchup/club/profile-edit",_,"POST");const F=await $();Y(),F&&y(F),v(E=>E+1)}catch(_){console.error(_),V(b,_.message,3e3,"error"),Te(h,_.code)}finally{m(!1)}},ie=()=>{v(Math.max(S-1,0))},oe=async s=>{m(!0);try{await be.callRawAPI("/v3/api/custom/courtmatchup/club/profile-edit",{pricing:s.pricing,completed:1},"POST"),t(!0)}catch(o){console.error(o),V(b,"Error saving data",3e3,"error")}finally{m(!1)}};a.useEffect(()=>{var s,o,p;(async()=>{const _=await $();v(X(_))})(),W(),Ve({title:(s=j==null?void 0:j.club)==null?void 0:s.name,path:"/club/profile-setup",clubName:(o=j==null?void 0:j.club)==null?void 0:o.name,favicon:(p=j==null?void 0:j.club)==null?void 0:p.club_logo,description:"Club Profile Setup"})},[]),a.useEffect(()=>{if(S===4){const s=setInterval(()=>{W()},5e3);return()=>clearInterval(s)}},[S]);const d=()=>{const s=P();switch(S){case 0:return e.jsx(bs,{onNext:Q,register:A,setValue:N,errors:H,defaultValues:s,isSubmitting:C});case 1:return e.jsx(vs,{onNext:Q,onBack:ie,register:A,fields:r,append:M,remove:k,setValue:N,clubProfile:j,defaultValues:s,isSubmitting:C});case 2:return e.jsx(Cs,{onNext:Q,register:A,errors:H,setValue:N,clubProfile:j,defaultValues:s,isSubmitting:C,defaultSports:I,fetchClubProfile:$});case 3:return e.jsx(Ms,{onNext:Q,register:A,errors:H,values:s,setValue:N,watch:P,defaultValues:s,isSubmitting:C});case 4:return e.jsx(Es,{onNext:Q,isSubmitting:C,stripeConnectionData:U,setStripeConnectionData:z});case 5:return e.jsx(Ss,{onNext:Q,register:A,errors:H,setValue:N,defaultValues:s,isSubmitting:C});case 6:return e.jsx(Is,{onNext:Q,register:A,onSubmit:w(oe),errors:H,setValue:N,defaultValues:s,isSubmitting:C,sportsOffered:T});default:return null}};return e.jsx(ss,{children:e.jsxs("div",{className:"flex flex-col bg-white pb-7",children:[e.jsxs("div",{className:"flex w-full flex-col px-20 pt-4 max-md:max-w-full max-md:px-5",children:[e.jsx("div",{className:"mb-10",children:S!==0&&e.jsxs("button",{className:"mt-5 flex items-center gap-2 text-[#525866]",onClick:ie,children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.33333 4.79199L3.125 10.0003L8.33333 15.2087M3.75 10.0003H16.875",stroke:"#525866","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),e.jsx("span",{children:"Back"})]})}),e.jsx("div",{className:"flex flex-1 items-center justify-center",children:d()})]}),l&&e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsx("div",{className:"w-full max-w-xl rounded-2xl bg-white ",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"flex items-start gap-4 p-5",children:[e.jsx("div",{className:"",children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#EFFAF6"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z",fill:"#38C793"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:"Sign up complete!"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Congratulations! You have successfully signed up for Court Matchup. You can now access your club portal, manage your club, and start creating courts."})]})]}),e.jsx("div",{className:"flex w-full justify-end border-t border-gray-200 p-5",children:e.jsx(se,{onClick:()=>R("/club/dashboard"),className:"w-fit rounded-xl bg-primaryGreen px-5 py-3 text-white",children:"Continue to Club portal!"})})]})})})]})})}export{Ct as default};
