import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import{P as e}from"./@fortawesome/react-fontawesome-13437837.js";const y=({columns:l,data:c,loading:n,renderCustomCell:p,tableClassName:o="w-full min-w-full table-auto border-separate border-spacing-y-1 sm:border-spacing-y-2",rowClassName:g="cursor-pointer bg-gray-100 hover:bg-gray-50 transition-colors duration-150",cellClassName:d="px-3 py-3 sm:px-4 sm:py-4 lg:px-6",headerClassName:m="whitespace-nowrap px-3 py-3 sm:px-4 sm:py-4 lg:px-6 text-left text-sm font-medium text-gray-500",emptyMessage:h="No data available",loadingMessage:j="Loading...",onClick:x})=>s.jsxs("div",{className:"w-full overflow-x-auto",children:[s.jsx("div",{className:"min-w-full",children:s.jsxs("table",{className:o,children:[s.jsx("thead",{className:"!border-none",children:s.jsx("tr",{children:l.map((a,t)=>a.accessor===""?s.jsx("th",{scope:"col",className:m},t):s.jsx("th",{scope:"col",className:m,children:a.header},t))})}),s.jsx("tbody",{children:c.map((a,t)=>s.jsx("tr",{className:g,onClick:()=>x&&x(a),children:l.map((r,i)=>p&&p[r.accessor]?s.jsx("td",{className:`${d} ${i===0?"rounded-l-xl":""} ${i===l.length-1?"!rounded-r-xl":""}`,children:p[r.accessor](a)},`${t}-${i}`):s.jsx("td",{className:`${d} ${i===0?"rounded-l-xl":""} ${i===l.length-1?"!rounded-r-xl":""}`,children:s.jsx("div",{className:"flex items-center",children:s.jsx("span",{className:"break-words text-sm font-medium text-gray-900",children:r.mappingExist&&r.mappings?r.mappings[a[r.accessor]]:a[r.accessor]||"--"})})},`${t}-${i}`))},t))})]})}),n&&s.jsx("div",{className:"px-3 py-4 sm:px-6",children:s.jsx("p",{className:"text-sm text-gray-500",children:j})}),!n&&c.length===0&&s.jsx("div",{className:"w-full px-3 py-8 text-center sm:px-6",children:s.jsx("p",{className:"text-sm text-gray-500",children:h})})]});y.propTypes={columns:e.arrayOf(e.shape({header:e.string.isRequired,accessor:e.string.isRequired,mappingExist:e.bool,mappings:e.object})).isRequired,data:e.array.isRequired,loading:e.bool,renderCustomCell:e.objectOf(e.func),tableClassName:e.string,rowClassName:e.string,cellClassName:e.string,headerClassName:e.string,emptyMessage:e.string,loadingMessage:e.string,onClick:e.func};export{y as D};
