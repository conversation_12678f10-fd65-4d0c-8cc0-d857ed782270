import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as i,f as Rt,b as ce,L as Ye}from"./vendor-851db8c1.js";import{M as Et,T as At,G as Lt,A as qt,u as Ft,E as Yt,b,ab as O,aO as Ot,aP as ee,aQ as Ht,aB as Vt,e as Gt,P as H,aR as Ut,d as Jt,aG as Dt,Y as Zt,X as Qt,Z as Wt}from"./index-13fd629e.js";import{B as Kt}from"./BackButton-11ba52b2.js";import{T as Xt}from"./TimeSlots-933ca6d9.js";import{A as zt}from"./AddPlayers-6917a66e.js";import{C as es}from"./Calendar-35bce269.js";import{S as ts}from"./SportTypeSelection-1b7f983d.js";import{C as Oe}from"./ReservationSummary-5582c1fb.js";import{h as V}from"./moment-a9aaa855.js";import{S as ss}from"./react-select-c8303602.js";import{g as ns}from"./customThresholdUtils-f40b07d5.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./react-tooltip-7a26650a.js";import"./index.esm-09a3a6b8.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./SelectionOptionsCard-30c39f7f.js";import"./SelectionOption-658322e6.js";let G=new Et,He=new At;const zs=({})=>{var Me,$e,Pe,Be,Ie,Re,Ee,Ae,Le,qe,Fe;const[p,Ve]=i.useState(null),[j,Ge]=i.useState(null),[U,de]=i.useState(new Date),[Ue,Je]=i.useState([]),[De,Ze]=i.useState([]),[J,Qe]=i.useState(0),[Z,We]=i.useState(0),[is,ue]=i.useState(!1),[R,E]=i.useState("main"),[x,Q]=i.useState([]),[me,Ke]=i.useState(!1),[te,se]=i.useState(1),[ge,Xe]=i.useState(!1),[pe,ze]=i.useState(3.5),[fe,et]=i.useState(3.5),[he,tt]=i.useState(""),[st,nt]=i.useState([]),[_,ye]=i.useState(null),[it,xe]=i.useState(!1),[g,at]=i.useState(null),[f,rt]=i.useState(null),[h,ve]=i.useState([]),[ne,_e]=i.useState([]),[M,we]=i.useState([]),[v,ie]=i.useState(null),[$,be]=i.useState(30),[ot,lt]=i.useState(null),[ct,dt]=i.useState(null),[ut,je]=i.useState(!1),[mt,gt]=i.useState(null),[Ne,pt]=i.useState(null),[ft,q]=i.useState(!1),[A,P]=i.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),[ht,W]=i.useState(null),[yt,Se]=i.useState(!1),{state:as,dispatch:w}=i.useContext(Lt);i.useContext(qt);const{club:s,pricing:Ce,sports:L,loading:rs,user_subscription:B,user_permissions:C,user_profile:N,club_membership:F,courts:D}=Ft(),xt=Rt();console.log("club",s);const K=localStorage.getItem("user"),c=ce.useMemo(()=>!(B!=null&&B.planId)||!(F!=null&&F.length)?null:F.find(t=>t.plan_id===B.planId),[B,F]);console.log("userMembershipPlan",c),console.log("user_subscription",B),console.log("club_membership",F);const X=ce.useMemo(()=>{var l,a;if(((l=c==null?void 0:c.advance_booking_enabled)==null?void 0:l.court)===!1){const d=new Date;return d.setFullYear(d.getFullYear()+10),d}const t=((a=c==null?void 0:c.advance_booking_days)==null?void 0:a.court)||10,n=new Date,r=new Date;return r.setDate(n.getDate()+t),r},[c]),vt=async()=>{try{const t=await He.getList("user",{filter:["role,cs,user",`club_id,cs,${s==null?void 0:s.id}`]});Je(t.list)}catch(t){console.error(t)}},_t=async()=>{try{const t=parseInt(K);if(!K||isNaN(t)){console.error("Invalid user_id for fetching family members:",K);return}const n=await He.getList("user",{filter:[`guardian,eq,${t}`,"role,cs,user"]});nt(n.list)}catch(t){console.error("Error fetching family members:",t)}},wt=async()=>{try{const t=await G.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");Ze(t.groups)}catch(t){console.error(t)}};i.useEffect(()=>{(async()=>(xe(!0),await wt(),await _t(),xe(!1)))()},[K]),i.useEffect(()=>{s!=null&&s.id&&vt()},[s==null?void 0:s.id]),i.useEffect(()=>{N&&!_&&ye(N)},[N,_]),ce.useEffect(()=>{Yt({path:"/user/reserve-court",clubName:s==null?void 0:s.name,favicon:s==null?void 0:s.club_logo,description:"Reserve a court"})},[s==null?void 0:s.club_logo]),i.useEffect(()=>{},[R]);const bt=()=>{de(new Date(U.setMonth(U.getMonth()-1)))},jt=()=>{de(new Date(U.setMonth(U.getMonth()+1)))},u=L==null?void 0:L.find(t=>t.id===p),k=ns(s==null?void 0:s.custom_request_threshold,p,g,f,4,L);i.useEffect(()=>{p&&(console.log("Selected Sport:",p),console.log("Selected Type:",g),console.log("Selected SubType:",f),console.log("Max Players Allowed:",k))},[p,g,f,k]),i.useEffect(()=>{x.length>k&&(console.log(`Clearing selected players: current ${x.length} exceeds new threshold ${k}`),Q([]),se(1),b(w,`Player selection cleared. New maximum is ${k} players. Please select players again.`,4e3,"warning"))},[k]),i.useEffect(()=>{te>k-x.length&&se(Math.max(0,k-x.length))},[k,x.length]);const{start_time:ae,end_time:re,duration:oe}=O(h),Nt=async()=>{var l,a,d;if(!p||!(s!=null&&s.id))return;const t=(l=u==null?void 0:u.sport_types)==null?void 0:l.some(o=>o.type&&o.type.trim()!==""),n=(a=u==null?void 0:u.sport_types)==null?void 0:a.find(o=>o.type===g),r=(d=n==null?void 0:n.subtype)==null?void 0:d.some(o=>o&&o.trim()!=="");if(!p||t&&!g||t&&r&&!f){W(null);return}Se(!0);try{const o=new URLSearchParams;o.append("sport_id",p),g&&o.append("type",g),f&&o.append("subtype",f);const y=await G.callRawAPI(`/v3/api/custom/courtmatchup/user/reservations/availability/${s.id}?${o.toString()}`,{},"GET");if(y&&!y.error){let m=[];y.qualifying_courts&&(m=y.qualifying_courts.filter(I=>{try{return JSON.parse(I.court_settings||"{}").allow_reservation!==!1}catch(T){return console.warn(`Failed to parse court_settings for court ${I.id}:`,T),!1}}));const S={...y,qualifying_courts:m};W(S),be(y.min_booking_time||30)}else console.error("Error fetching availability:",y==null?void 0:y.message),W(null)}catch(o){console.error("Error fetching availability:",o),W(null)}finally{Se(!1)}},ke=async(t,n,r)=>{try{const l=V(t).format("YYYY-MM-DD"),a=new URLSearchParams;return a.append("sport_id",p||""),a.append("type",g||""),a.append("subtype",f||""),a.append("date",l),a.append("court_id",""),a.append("start_time",n),a.append("end_time",r),await G.callRawAPI(`/v3/api/custom/courtmatchup/user/reservations/availability/${s==null?void 0:s.id}?${a.toString()}`,{},"GET")}catch(l){return console.error("Error getting qualifying courts:",l),{qualifying_courts:[],availability:[],min_booking_time:30}}},St=async(t,n,r)=>{if(!t||!n||!r)return[];const l=await ke(t,n,r);if(l.error)return console.error("API Error:",l.message),[];const d=(l.qualifying_courts||[]).filter(o=>{try{return JSON.parse(o.court_settings||"{}").allow_reservation!==!1}catch(y){return console.warn(`Failed to parse court_settings for court ${o.id}:`,y),!1}});return we(d),d},Ct=()=>!(!j||!p||ne.length===0),kt=i.useCallback(({sport:t,type:n,subType:r})=>{Ve(t),at(n),rt(r)},[]);i.useEffect(()=>{Nt()},[p,g,f,s==null?void 0:s.id,u]),i.useEffect(()=>{if(D&&D.length>0){let t=[...D];p&&(t=t.filter(n=>n.sport_id&&n.sport_id.toString()===p.toString())),g&&(t=t.filter(n=>n.type===g)),f&&(t=t.filter(n=>n.sub_type===f)),t=t.filter(n=>{try{return JSON.parse(n.court_settings||"{}").allow_reservation!==!1}catch(r){return console.warn(`Failed to parse court_settings for court ${n.id}:`,r),!1}}),_e(t)}else _e([])},[D,p,g,f]);const Tt=async()=>{var l,a,d;const t=(l=u==null?void 0:u.sport_types)==null?void 0:l.some(o=>o.type&&o.type.trim()!==""),n=(a=u==null?void 0:u.sport_types)==null?void 0:a.find(o=>o.type===g),r=(d=n==null?void 0:n.subtype)==null?void 0:d.some(o=>o&&o.trim()!=="");if(!p||t&&!g||t&&r&&!f||!j||!ae||!re){b(w,"Please select all required fields",3e3,"error");return}ue(!0);try{const o=await G.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:Z},"POST");lt(o.client_secret),dt(o.payment_intent);const y=V(j).format("YYYY-MM-DD"),m={sport_id:p,type:g,sub_type:f,date:y,start_time:ae,end_time:re,duration:oe,reservation_type:Dt.court,price:Z,player_ids:x.map(T=>T.id),primary_player_id:(_==null?void 0:_.id)||(N==null?void 0:N.id),buddy_details:null,payment_status:0,payment_intent:o.payment_intent,service_fee:ee(s==null?void 0:s.fee_settings,J),club_fee:s==null?void 0:s.club_fee,players_needed:te,min_ntrp:pe,max_ntrp:fe,note:he};let S=null;if((s==null?void 0:s.allow_user_court_selection)===1&&v)S=v;else{const T=await St(j,ae,re);T.length>0&&(S=T.sort((le,It)=>(le.min_booking_time||30)-(It.min_booking_time||30))[0].id)}S&&(m.court_id=S);const I=await G.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",m,"POST");return await Zt(G,{user_id:localStorage.getItem("user"),activity_type:Qt.court_reservation,action_type:Wt.CREATE,data:m,club_id:s==null?void 0:s.id,description:`${N==null?void 0:N.first_name} ${N==null?void 0:N.last_name} created a court reservation`}),I.error||b(w,"Reservation created successfully",3e3,"success"),pt(I.reservation_id),I.booking_id}catch(o){console.error(o),b(w,o.message||"Error creating reservation",3e3,"error")}finally{ue(!1)}},Te=t=>{Q(n=>n.some(l=>l.id===t.id)?n.filter(l=>l.id!==t.id):[...n,t])},Mt=t=>{const n=t.value||t;(n==null?void 0:n.id)!==(_==null?void 0:_.id)&&(ye(n),Q(r=>{const l=r.filter(d=>d.id!==(_==null?void 0:_.id));if(l.some(d=>d.id===n.id)){const d=l.filter(o=>o.id!==n.id);return[n,...d]}else return[n,...l]}))},$t=async()=>{q(!0);try{const t=V(h[0].from,"h:mm A").format("HH:mm"),n=V(h[0].until,"h:mm A").format("HH:mm"),r=await ke(j,t,n);if(r.error){b(w,r.message||"No courts available that match the selected criteria",4e3,"warning"),q(!1);return}const l=r.qualifying_courts||[],a=r.min_booking_time||30,d=l.filter(m=>{try{return JSON.parse(m.court_settings||"{}").allow_reservation!==!1}catch(S){return console.warn(`Failed to parse court_settings for court ${m.id}:`,S),!1}});if(d.length===0){b(w,"No courts are available for the selected time slot. Please choose a different time.",4e3,"warning"),q(!1);return}if(we(d),be(a),d.length>0){const{duration:m}=O(h),S=m*60,I=d.filter(T=>{const z=T.min_booking_time||a;return S>=z});if(I.length>0){const T=I.sort((z,le)=>(z.min_booking_time||30)-(le.min_booking_time||30));ie(T[0].id)}}const{duration:o}=O(h);if(o*60>=a){q(!1),E("players");return}b(w,`Minimum booking time requirement is ${Math.floor(a/60)} hour${Math.floor(a/60)!==1?"s":""}${a%60!==0?` ${a%60} minutes`:""}. Please select a longer time slot.`,5e3,"warning"),q(!1)}catch(t){console.error("Error checking court availability:",t),b(w,"Error checking court availability. Please try again.",3e3,"error"),q(!1)}};i.useEffect(()=>{if(x!=null&&x.length&&p&&g&&f&&(h!=null&&h.length)&&oe){const t=Ot({pricing:Ce,sportId:p,type:g,subType:f,duration:oe,selectedTime:h[0]}),n=ee(s==null?void 0:s.fee_settings,t);Qe(t),We(t+n)}},[x,p,g,f,h,Ce,s==null?void 0:s.fee_settings]);const Pt=async()=>{var l,a,d,o,y;if(!(B!=null&&B.planId)){P({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to reserve courts",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(C!=null&&C.allowCourt)){P({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${C==null?void 0:C.planName}) does not include court reservations. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(j>X&&((l=c==null?void 0:c.advance_booking_enabled)!=null&&l.court)){const m=`Your membership plan only allows booking ${((a=c==null?void 0:c.advance_booking_days)==null?void 0:a.court)||10} days in advance. Please select a valid date.`;P({isOpen:!0,title:"Date Selection Error",message:m,type:"warning"}),E("main");return}const t=(d=u==null?void 0:u.sport_types)==null?void 0:d.some(m=>m.type&&m.type.trim()!==""),n=(o=u==null?void 0:u.sport_types)==null?void 0:o.find(m=>m.type===g),r=(y=n==null?void 0:n.subtype)==null?void 0:y.some(m=>m&&m.trim()!=="");if(!p||t&&!g||t&&r&&!f||!j||!h.length){P({isOpen:!0,title:"Incomplete Details",message:"Please complete all required Reservation detail",type:"warning"}),E("main");return}if((s==null?void 0:s.allow_user_court_selection)===1&&!v){P({isOpen:!0,title:"Court Selection Required",message:"Please select a court for your reservation",type:"warning"});return}if(h.length>0){const{duration:m}=O(h),S=m*60;if(S<$){P({isOpen:!0,title:"Minimum Booking Time Not Met",message:`The minimum booking time requirement is ${$} minutes. Your current selection is ${S} minutes. Please select a longer time slot.`,type:"warning"}),E("main");return}}if(!x.length){P({isOpen:!0,title:"Players Required",message:"Please select at least one player",type:"warning"});return}try{je(!0);const m=await Tt();if(!m)throw new Error("Failed to create reservation");gt(m),E("payment")}catch(m){console.error("Reservation error:",m),P({isOpen:!0,title:"Reservation Error",message:m.message||"Error creating reservation",type:"error"})}finally{je(!1)}},Bt=t=>{const n=V(t.from,"h:mm A"),l=V(t.until,"h:mm A").diff(n,"minutes");if(v&&M.length>0){const a=M.find(o=>o.id===v),d=(a==null?void 0:a.min_booking_time)||$;if(l<d){b(w,`Minimum booking time for this court is ${d} minutes. Please select a longer time slot.`,4e3,"warning");return}}ie(null),ve([{from:t.from,until:t.until}])},Y=s!=null&&s.court_description?JSON.parse(s==null?void 0:s.court_description):{reservation_description:"",payment_description:""};return C&&!C.allowCourt?e.jsx(Ht,{message:`Your current plan (${C==null?void 0:C.planName}) does not include court reservations. Please upgrade your plan to access this feature.`}):e.jsxs("div",{className:"",children:[e.jsx(Vt,{isOpen:A.isOpen,onClose:()=>P({...A,isOpen:!1}),title:A.title,message:A.message,actionButtonText:A.actionButtonText,actionButtonLink:A.actionButtonLink,type:A.type}),it&&e.jsx(Gt,{}),e.jsxs("div",{className:"flex items-center justify-center bg-white p-4",children:[R==="main"&&e.jsx("div",{className:" ",children:"Step 1 • Select date and time"}),R==="players"&&e.jsx("div",{className:" ",children:"Step 2 • Reservation detail"}),R==="payment"&&e.jsx("div",{className:" ",children:"Step 3 • Payment"})]}),e.jsxs("div",{className:"p-4",children:[e.jsx(Kt,{onBack:()=>{R==="main"?xt(-1):E(R==="payment"?"players":"main")}}),R==="main"?e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(ts,{sports:L,userPermissions:C,initialSport:p,initialType:g,initialSubType:f,onSelectionChange:kt,courts:D,filterMode:"reservation"},`${p}-${g}-${f}`),p&&(!((Me=u==null?void 0:u.sport_types)!=null&&Me.length)||g!==null&&(f!==null||!((Be=(Pe=($e=u==null?void 0:u.sport_types)==null?void 0:$e.find(t=>t.type===g))==null?void 0:Pe.subtype)!=null&&Be.length)))?e.jsxs(e.Fragment,{children:[e.jsx("div",{children:e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[(Ie=c==null?void 0:c.advance_booking_enabled)!=null&&Ie.court?e.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["You can reserve a court up to"," ",(Re=c==null?void 0:c.advance_booking_days)==null?void 0:Re.court," ",((Ee=c==null?void 0:c.advance_booking_days)==null?void 0:Ee.court)===1?"day":"days"," ","in advance."]}):e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can reserve a court for any future date."}),e.jsx(es,{currentMonth:U,selectedDate:j,onDateSelect:t=>{var n,r;if(t>X){const l=(n=c==null?void 0:c.advance_booking_enabled)!=null&&n.court?`Your membership plan only allows booking ${((r=c==null?void 0:c.advance_booking_days)==null?void 0:r.court)||10} days in advance`:"";if(l){b(w,l,3e3,"warning");return}}Ge(t)},onPreviousMonth:bt,onNextMonth:jt,daysOff:s!=null&&s.days_off?JSON.parse(s.days_off):[],allowPastDates:!1,minDate:new Date,maxDate:X,disabledDateMessage:(Ae=c==null?void 0:c.advance_booking_enabled)!=null&&Ae.court?`Your membership plan only allows booking ${((Le=c==null?void 0:c.advance_booking_days)==null?void 0:Le.court)||10} days in advance`:"You can book for any future date"})]})}),j&&e.jsxs("div",{children:[p&&ne.length>0&&e.jsxs("div",{className:"mb-4 rounded-lg bg-blue-50 p-3",children:[e.jsxs("p",{className:"text-sm text-blue-800",children:[e.jsx("span",{className:"font-medium",children:"Minimum booking time:"})," ",e.jsxs("span",{className:"font-semibold text-blue-900",children:[Math.floor($/60)," hour",Math.floor($/60)!==1?"s":"",$%60!==0&&` ${$%60} minutes`]})]}),e.jsx("p",{className:"mt-1 text-xs text-blue-600",children:"Based on available courts for your selected sport, type, and subtype"})]}),e.jsx(Xt,{selectedDate:j,timeRange:h,onTimeClick:Bt,isLoading:ft,onNext:()=>{var t,n;if(!h.length){b(w,"Please select a time slot",3e3,"error");return}if(j>X){const r=(t=c==null?void 0:c.advance_booking_enabled)!=null&&t.court?`Your membership plan only allows booking ${((n=c==null?void 0:c.advance_booking_days)==null?void 0:n.court)||10} days in advance`:"";if(r){b(w,r,3e3,"warning");return}}$t()},nextButtonText:"Next: Players",startHour:0,endHour:24,interval:30,className:"h-fit",isTimeSlotAvailable:Ct,clubTimes:s!=null&&s.times?JSON.parse(s.times):[],minBookingTime:v&&M.length>0&&((qe=M.find(t=>t.id===v))==null?void 0:qe.min_booking_time)||$,enforceMinBookingTime:!!v,availabilityData:ht,loadingAvailability:yt})]})]}):e.jsx("div",{className:"flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5 md:col-span-2",children:e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Please select a sport"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose a sport, type, and sub-type to view available time slots"})]})})]})})})}):R==="payment"?e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsx(Oe,{selectedSport:p,sports:L,selectedType:g,selectedSubType:f,selectedDate:j,selectedTimes:h,selectedCourt:v?ne.find(t=>t.id===v):null})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Club fee"}),e.jsx("span",{children:H(J)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:H(ee(s==null?void 0:s.fee_settings,J))})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:H(Z)})]}),e.jsxs("div",{children:[e.jsx(Ut,{user:N,bookingId:mt,reservationId:Ne,clientSecret:ot,paymentIntent:ct,navigateRoute:`/user/payment-success/${Ne}?type=court`}),e.jsx("div",{className:"mt-4",children:e.jsx("div",{className:"text-sm text-gray-500",children:Y==null?void 0:Y.payment_description})})]}),e.jsx("div",{className:"space-y-4 text-sm text-gray-500",children:e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx(Ye,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(Ye,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]})})]})]})})]})]}):e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsxs("div",{className:"space-y-4",children:[" ",e.jsx(Oe,{selectedSport:p,sports:L,selectedType:g,selectedSubType:f,selectedDate:j,selectedTimes:h,selectedCourt:v?M.find(t=>t.id===v):null})]}),e.jsxs("div",{className:"space-y-4",children:[(s==null?void 0:s.allow_user_court_selection)===1&&e.jsxs("div",{className:"mb-2 h-fit rounded-lg bg-white p-4 shadow-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-900",children:"Select Court"}),v&&M.length>0&&e.jsxs("div",{className:"mb-3 rounded-lg bg-green-50 p-2 text-xs text-green-700",children:[e.jsx("span",{className:"font-medium",children:(Fe=M.find(t=>t.id===v))==null?void 0:Fe.name})," ","has been automatically selected as the best match for your time slot. If another court that matches your requirements are available, you can select them from the dropdown. Courts with higher minimum booking time requirements are shown but disabled if your current time selection is too short."]}),e.jsx(ss,{className:"w-full text-sm",options:M.map(t=>{const n=t.min_booking_time||30,{duration:r}=h.length>0?O(h):{duration:0},l=r*60,a=l>0&&n>l;let d=`${t.name} (Min: ${n}min)`;if(a){const o=Math.floor(n/60),y=n%60,m=o>0?`${o}h${y>0?` ${y}m`:""}`:`${y}m`;d+=` - Requires ${m} minimum`}return{value:t.id,label:d,isDisabled:a}}),onChange:t=>{if(ie(t?t.value:null),t&&h.length>0){const n=M.find(d=>d.id===t.value),r=(n==null?void 0:n.min_booking_time)||$,{duration:l}=O(h);l*60<r&&(ve([]),b(w,`Selected court requires minimum ${r} minutes. Please select a new time slot.`,4e3,"info"))}},value:v?(()=>{const t=M.find(n=>n.id===v);if(t){const n=t.min_booking_time||30;return{value:v,label:`${t.name} (Min: ${n}min)`}}return null})():null,isClearable:!0,placeholder:"Select a court (available courts will appear after time selection)",noOptionsMessage:()=>{var n,r,l;if(!p)return"Please select a sport first";if((n=u==null?void 0:u.sport_types)!=null&&n.some(a=>a.type&&a.type.trim()!=="")&&!g)return"Please select a type";const t=(r=u==null?void 0:u.sport_types)==null?void 0:r.find(a=>a.type===g);return(l=t==null?void 0:t.subtype)!=null&&l.some(a=>a&&a.trim()!=="")&&!f?"Please select a sub-type":!j||!h.length?"Please select date and time first to see available courts":"No courts available for the selected time slot"}})]}),e.jsx(zt,{players:Ue,groups:De,selectedPlayers:x,familyMembers:st,currentUser:_,onCurrentUserChange:Mt,onPlayerToggle:t=>{if(x.some(r=>r.id===t.id)){if(t.id===(_==null?void 0:_.id)){b(w,"You cannot remove the primary player from the reservation",3e3,"warning");return}Te(t);return}if(x.length>=k){const r=(u==null?void 0:u.name)||"this sport";b(w,`Maximum ${k} players allowed for ${r} (including yourself)`,3e3,"warning");return}Te(t)},isFindBuddyEnabled:me,setSelectedPlayers:Q,onFindBuddyToggle:()=>{Ke(!me),Xe(!ge)},playersNeeded:te,onPlayersNeededChange:se,maximumPlayers:k,userProfile:N,showPlayersNeeded:ge,onNtrpMinChange:ze,onNtrpMaxChange:et,onShortBioChange:tt,initialNtrpMin:pe,initialNtrpMax:fe,initialShortBio:he})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reservation detail"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",x==null?void 0:x.length,")"]}),e.jsx("div",{className:"mt-1",children:x.length>0&&x.map(t=>e.jsxs("div",{className:"text-sm",children:[t.first_name," ",t.last_name]},t.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Club Fee"}),e.jsx("span",{children:H(J)})]}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Service Fee"}),e.jsx("span",{children:H(ee(s==null?void 0:s.fee_settings,J))})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:H(Z)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 7.99995 2.33325C8.09995 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx(Jt,{loading:ut,onClick:Pt,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{children:"Reserve Now"}),e.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),e.jsx("div",{className:"text-center text-sm text-gray-500",children:Y==null?void 0:Y.reservation_description}),e.jsx("div",{className:"space-y-2 text-center text-sm text-gray-500",children:e.jsx("p",{children:"(You will not be charged yet)"})})]})})]})]})]})]})};export{zs as default};
