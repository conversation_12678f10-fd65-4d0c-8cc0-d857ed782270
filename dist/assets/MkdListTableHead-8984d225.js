import{j as d}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";const N=({onSort:p,columns:g,actions:e,actionPostion:y,areAllRowsSelected:t,handleSelectAll:h})=>d.jsx(d.Fragment,{children:d.jsx("tr",{children:g.map((r,s)=>{var x,l,j,w,i,k,f;if((r==null?void 0:r.accessor)===""){if([(x=e==null?void 0:e.select)==null?void 0:x.show,(l=e==null?void 0:e.view)==null?void 0:l.show,(j=e==null?void 0:e.edit)==null?void 0:j.show,(w=e==null?void 0:e.delete)==null?void 0:w.show].includes(!0))return d.jsxs("th",{scope:"col",className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 ${r.isSorted?"cursor-pointer":""} `,onClick:r.isSorted?()=>p(s):void 0,children:[r.header==="Action"&&((i=e==null?void 0:e.select)!=null&&i.show)?d.jsx("input",{type:"checkbox",disabled:!((k=e==null?void 0:e.select)!=null&&k.multiple),id:"select_all_rows",className:"mr-3",checked:t,onChange:h}):null,y==="ontable"&&r.header,d.jsx("span",{children:r.isSorted?r.isSortedDesc?" ▼":" ▲":""})]},s)}else return d.jsxs("th",{scope:"col",className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 ${r.isSorted?"cursor-pointer":""} `,onClick:r.isSorted?()=>p(s):void 0,children:[r.header==="Action"&&((f=e==null?void 0:e.select)!=null&&f.show)?d.jsx("input",{type:"checkbox",id:"select_all_rows",className:"mr-3",checked:t,onChange:h}):null,r.header,d.jsx("span",{children:r.isSorted?r.isSortedDesc?" ▼":" ▲":""})]},s);return null})})});export{N as default};
