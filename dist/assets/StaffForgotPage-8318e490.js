import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as j,b as k,L as M}from"./vendor-851db8c1.js";import{u as O}from"./react-hook-form-687afde5.js";import{o as V}from"./yup-2824f222.js";import{c as B,a as R}from"./yup-54691517.js";import{M as w,G,d as L,b as U,t as A}from"./index-13fd629e.js";import{A as H}from"./AuthLayout-ceb43654.js";import{V as I}from"./VerificationModal-3c93a328.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";new w;const Se=()=>{const[y,s]=j.useState(!1),[v,l]=j.useState(!1),_=B({email:R().email().required()}).required(),{register:b,handleSubmit:C,setError:r,watch:S,formState:{errors:a}}=O({resolver:V(_)}),{dispatch:o}=k.useContext(G),N=async E=>{var m,n,d,x,c,p,f,h;let F=new w;try{s(!0);const t=await F.forgot(E.email,"staff");if(!t.error)U(o,"Reset Code Sent"),l(!0);else if(t.validation){const u=Object.keys(t.validation);for(let i=0;i<u.length;i++){const g=u[i];r(g,{type:"manual",message:t.validation[g]})}}s(!1)}catch(t){s(!1),console.log("Error",t),r("email",{type:"manual",message:(n=(m=t==null?void 0:t.response)==null?void 0:m.data)!=null&&n.message?(x=(d=t==null?void 0:t.response)==null?void 0:d.data)==null?void 0:x.message:t==null?void 0:t.message}),A(o,(p=(c=t==null?void 0:t.response)==null?void 0:c.data)!=null&&p.message?(h=(f=t==null?void 0:t.response)==null?void 0:f.data)==null?void 0:h.message:t==null?void 0:t.message)}};return e.jsx(e.Fragment,{children:e.jsxs(H,{children:[e.jsxs("div",{className:"flex flex-col bg-white",children:[e.jsx("main",{className:"flex flex-grow flex-col bg-white",children:e.jsx("div",{className:"flex w-full flex-col px-20 pt-4 max-md:max-w-full max-md:px-5",children:e.jsx("section",{className:"mt-6 flex w-full justify-center max-md:max-w-full",children:e.jsx("div",{className:"flex w-[553px] min-w-[240px] flex-col items-center pt-12",children:e.jsxs("div",{className:"flex w-[392px] max-w-full flex-col",children:[e.jsxs("div",{className:"mb-4 flex w-full flex-col",children:[e.jsx("div",{className:"flex w-[74px] items-start gap-4 self-center overflow-hidden rounded-[96px] p-2",children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_151_231)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_151_231)"}),e.jsxs("g",{filter:"url(#filter0_d_151_231)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M37 24.8125C33.5482 24.8125 30.75 27.6107 30.75 31.0625V33.25H29.1875C27.9794 33.25 27 34.2294 27 35.4375V47.3125C27 48.5206 27.9794 49.5 29.1875 49.5H44.8125C46.0206 49.5 47 48.5206 47 47.3125V35.4375C47 34.2294 46.0206 33.25 44.8125 33.25H43.25V31.0625C43.25 27.6107 40.4518 24.8125 37 24.8125ZM41.375 33.25V31.0625C41.375 28.6463 39.4162 26.6875 37 26.6875C34.5838 26.6875 32.625 28.6463 32.625 31.0625V33.25H41.375ZM37 38.5625C37.5178 38.5625 37.9375 38.9822 37.9375 39.5V43.25C37.9375 43.7678 37.5178 44.1875 37 44.1875C36.4822 44.1875 36.0625 43.7678 36.0625 43.25V39.5C36.0625 38.9822 36.4822 38.5625 37 38.5625Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_151_231",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[e.jsx("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_151_231"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_151_231",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_151_231",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{stopColor:"#E4E5E7",stopOpacity:"0.48"}),e.jsx("stop",{offset:"1",stopColor:"#F7F8F8",stopOpacity:"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_151_231",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{stopColor:"#E4E5E7"}),e.jsx("stop",{offset:"1",stopColor:"#F7F8F8",stopOpacity:"0"})]})]})]})}),e.jsx("div",{className:"mt-4 text-center text-2xl font-semibold text-gray-900",children:"Forgot Password"}),e.jsx("div",{className:"mt-2 text-center text-base font-normal text-gray-500",children:"Enter your email address to reset your password"})]}),e.jsxs("form",{onSubmit:C(N),className:"flex w-full flex-col gap-6",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email"}),e.jsx("input",{...b("email"),type:"email",placeholder:"Enter your email",className:`block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 ${a.email?"border-red-500":""}`}),a.email&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:a.email.message})]}),e.jsx(L,{type:"submit",loading:y,className:"mt-2 w-full gap-2.5 self-stretch overflow-hidden whitespace-nowrap rounded-xl bg-emerald-800 px-2.5 py-3 text-center text-lg font-medium leading-none tracking-tight text-white shadow-sm",children:"Reset Password"}),e.jsx("div",{className:"mt-0 ",children:e.jsx(M,{to:"/staff/login",className:"text-sm font-medium text-gray-500 underline",children:"Back to Login"})})]})]})})})})}),e.jsx("footer",{className:"mt-auto",children:e.jsx("div",{className:"flex w-full flex-col"})})]}),e.jsx(I,{isOpen:v,onClose:()=>l(!1),title:"Reset Code Sent",description:"Please check your email for the reset code.",email:S("email")})]})})};export{Se as default};
