import{r as t,b as ne}from"./vendor-851db8c1.js";import{a as yt,b as ht,c as nt,o as wt,f as _t,s as bt}from"./@mantine/core-8cbffb6d.js";import{y as Ge}from"./@uppy/dashboard-4a19149e.js";/*
* React Tooltip
* {@link https://github.com/ReactTooltip/react-tooltip}
* @copyright ReactTooltip Team
* @license MIT
*/const Et="react-tooltip-core-styles",St="react-tooltip-base-styles",st={core:!1,base:!1};function ct({css:o,id:n=St,type:d="base",ref:i}){var f,y;if(!o||typeof document>"u"||st[d]||d==="core"&&typeof process<"u"&&(!((f=process==null?void 0:process.env)===null||f===void 0)&&f.REACT_TOOLTIP_DISABLE_CORE_STYLES)||d!=="base"&&typeof process<"u"&&(!((y=process==null?void 0:process.env)===null||y===void 0)&&y.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;d==="core"&&(n=Et),i||(i={});const{insertAt:A}=i;if(document.getElementById(n))return;const _=document.head||document.getElementsByTagName("head")[0],v=document.createElement("style");v.id=n,v.type="text/css",A==="top"&&_.firstChild?_.insertBefore(v,_.firstChild):_.appendChild(v),v.styleSheet?v.styleSheet.cssText=o:v.appendChild(document.createTextNode(o)),st[d]=!0}const it=async({elementReference:o=null,tooltipReference:n=null,tooltipArrowReference:d=null,place:i="top",offset:f=10,strategy:y="absolute",middlewares:A=[wt(Number(f)),_t({fallbackAxisSideDirection:"start"}),bt({padding:5})],border:_})=>{if(!o)return{tooltipStyles:{},tooltipArrowStyles:{},place:i};if(n===null)return{tooltipStyles:{},tooltipArrowStyles:{},place:i};const v=A;return d?(v.push(ht({element:d,padding:5})),nt(o,n,{placement:i,strategy:y,middleware:v}).then(({x:q,y:z,placement:T,middlewareData:se})=>{var P,N;const $={left:`${q}px`,top:`${z}px`,border:_},{x:Q,y:K}=(P=se.arrow)!==null&&P!==void 0?P:{x:0,y:0},U=(N={top:"bottom",right:"left",bottom:"top",left:"right"}[T.split("-")[0]])!==null&&N!==void 0?N:"bottom",I=_&&{borderBottom:_,borderRight:_};let X=0;if(_){const Z=`${_}`.match(/(\d+)px/);X=Z!=null&&Z[1]?Number(Z[1]):1}return{tooltipStyles:$,tooltipArrowStyles:{left:Q!=null?`${Q}px`:"",top:K!=null?`${K}px`:"",right:"",bottom:"",...I,[U]:`-${4+X}px`},place:T}})):nt(o,n,{placement:"bottom",strategy:y,middleware:v}).then(({x:q,y:z,placement:T})=>({tooltipStyles:{left:`${q}px`,top:`${z}px`},tooltipArrowStyles:{},place:T}))},at=(o,n)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(o,n),ut=(o,n,d)=>{let i=null;const f=function(...y){const A=()=>{i=null,d||o.apply(this,y)};d&&!i&&(o.apply(this,y),i=setTimeout(A,n)),d||(i&&clearTimeout(i),i=setTimeout(A,n))};return f.cancel=()=>{i&&(clearTimeout(i),i=null)},f},dt=o=>o!==null&&!Array.isArray(o)&&typeof o=="object",et=(o,n)=>{if(o===n)return!0;if(Array.isArray(o)&&Array.isArray(n))return o.length===n.length&&o.every((f,y)=>et(f,n[y]));if(Array.isArray(o)!==Array.isArray(n))return!1;if(!dt(o)||!dt(n))return o===n;const d=Object.keys(o),i=Object.keys(n);return d.length===i.length&&d.every(f=>et(o[f],n[f]))},gt=o=>{if(!(o instanceof HTMLElement||o instanceof SVGElement))return!1;const n=getComputedStyle(o);return["overflow","overflow-x","overflow-y"].some(d=>{const i=n.getPropertyValue(d);return i==="auto"||i==="scroll"})},pt=o=>{if(!o)return null;let n=o.parentElement;for(;n;){if(gt(n))return n;n=n.parentElement}return document.scrollingElement||document.documentElement},At=typeof window<"u"?t.useLayoutEffect:t.useEffect,C=o=>{o.current&&(clearTimeout(o.current),o.current=null)},kt="DEFAULT_TOOLTIP_ID",Ot={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},Tt=t.createContext({getTooltipData:()=>Ot});function ft(o=kt){return t.useContext(Tt).getTooltipData(o)}var _e={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},Ze={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};const Rt=({forwardRef:o,id:n,className:d,classNameArrow:i,variant:f="dark",anchorId:y,anchorSelect:A,place:_="top",offset:v=10,events:q=["hover"],openOnClick:z=!1,positionStrategy:T="absolute",middlewares:se,wrapper:P,delayShow:N=0,delayHide:$=0,float:Q=!1,hidden:K=!1,noArrow:U=!1,clickable:I=!1,closeOnEsc:X=!1,closeOnScroll:Z=!1,closeOnResize:Je=!1,openEvents:R,closeEvents:ce,globalCloseEvents:be,imperativeModeOnly:Qe,style:Be,position:De,afterShow:qe,afterHide:M,disableTooltip:ie,content:Ee,contentWrapperRef:G,isOpen:j,defaultIsOpen:ae=!1,setIsOpen:ee,activeAnchor:h,setActiveAnchor:ue,border:ze,opacity:Ke,arrowColor:Me,role:Ue="tooltip"})=>{var Se;const S=t.useRef(null),de=t.useRef(null),B=t.useRef(null),V=t.useRef(null),ge=t.useRef(null),[F,Ve]=t.useState({tooltipStyles:{},tooltipArrowStyles:{},place:_}),[k,We]=t.useState(!1),[te,oe]=t.useState(!1),[p,Ae]=t.useState(null),ke=t.useRef(!1),Oe=t.useRef(null),{anchorRefs:Te,setActiveAnchor:He}=ft(n),pe=t.useRef(!1),[Y,Re]=t.useState([]),re=t.useRef(!1),fe=z||q.includes("click"),xe=fe||(R==null?void 0:R.click)||(R==null?void 0:R.dblclick)||(R==null?void 0:R.mousedown),ve=R?{...R}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!R&&fe&&Object.assign(ve,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});const Le=ce?{...ce}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!ce&&fe&&Object.assign(Le,{mouseleave:!1,blur:!1,mouseout:!1});const D=be?{...be}:{escape:X||!1,scroll:Z||!1,resize:Je||!1,clickOutsideAnchor:xe||!1};Qe&&(Object.assign(ve,{mouseenter:!1,focus:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(Le,{mouseleave:!1,blur:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(D,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),At(()=>(re.current=!0,()=>{re.current=!1}),[]);const m=e=>{re.current&&(e&&oe(!0),setTimeout(()=>{re.current&&(ee==null||ee(e),j===void 0&&We(e))},10))};t.useEffect(()=>{if(j===void 0)return()=>null;j&&oe(!0);const e=setTimeout(()=>{We(j)},10);return()=>{clearTimeout(e)}},[j]),t.useEffect(()=>{if(k!==ke.current)if(C(ge),ke.current=k,k)qe==null||qe();else{const e=(l=>{const s=l.match(/^([\d.]+)(ms|s)$/);if(!s)return 0;const[,w,E]=s;return Number(w)*(E==="ms"?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"));ge.current=setTimeout(()=>{oe(!1),Ae(null),M==null||M()},e+25)}},[k]);const Pe=e=>{Ve(l=>et(l,e)?l:e)},Ce=(e=N)=>{C(B),te?m(!0):B.current=setTimeout(()=>{m(!0)},e)},me=(e=$)=>{C(V),V.current=setTimeout(()=>{pe.current||m(!1)},e)},Ne=e=>{var l;if(!e)return;const s=(l=e.currentTarget)!==null&&l!==void 0?l:e.target;if(!(s!=null&&s.isConnected))return ue(null),void He({current:null});N?Ce():m(!0),ue(s),He({current:s}),C(V)},ye=()=>{I?me($||100):$?me():m(!1),C(B)},he=({x:e,y:l})=>{var s;const w={getBoundingClientRect:()=>({x:e,y:l,width:0,height:0,top:l,left:e,right:e,bottom:l})};it({place:(s=p==null?void 0:p.place)!==null&&s!==void 0?s:_,offset:v,elementReference:w,tooltipReference:S.current,tooltipArrowReference:de.current,strategy:T,middlewares:se,border:ze}).then(E=>{Pe(E)})},we=e=>{if(!e)return;const l=e,s={x:l.clientX,y:l.clientY};he(s),Oe.current=s},$e=e=>{var l;if(!k)return;const s=e.target;s.isConnected&&(!((l=S.current)===null||l===void 0)&&l.contains(s)||[document.querySelector(`[id='${y}']`),...Y].some(w=>w==null?void 0:w.contains(s))||(m(!1),C(B)))},Xe=ut(Ne,50,!0),b=ut(ye,50,!0),x=e=>{b.cancel(),Xe(e)},r=()=>{Xe.cancel(),b()},c=t.useCallback(()=>{var e,l;const s=(e=p==null?void 0:p.position)!==null&&e!==void 0?e:De;s?he(s):Q?Oe.current&&he(Oe.current):h!=null&&h.isConnected&&it({place:(l=p==null?void 0:p.place)!==null&&l!==void 0?l:_,offset:v,elementReference:h,tooltipReference:S.current,tooltipArrowReference:de.current,strategy:T,middlewares:se,border:ze}).then(w=>{re.current&&Pe(w)})},[k,h,Ee,Be,_,p==null?void 0:p.place,v,T,De,p==null?void 0:p.position,Q]);t.useEffect(()=>{var e,l;const s=new Set(Te);Y.forEach(a=>{ie!=null&&ie(a)||s.add({current:a})});const w=document.querySelector(`[id='${y}']`);w&&!(ie!=null&&ie(w))&&s.add({current:w});const E=()=>{m(!1)},W=pt(h),H=pt(S.current);D.scroll&&(window.addEventListener("scroll",E),W==null||W.addEventListener("scroll",E),H==null||H.addEventListener("scroll",E));let g=null;D.resize?window.addEventListener("resize",E):h&&S.current&&(g=yt(h,S.current,c,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));const L=a=>{a.key==="Escape"&&m(!1)};D.escape&&window.addEventListener("keydown",L),D.clickOutsideAnchor&&window.addEventListener("click",$e);const u=[],Ie=a=>{k&&(a==null?void 0:a.target)===h||Ne(a)},vt=a=>{k&&(a==null?void 0:a.target)===h&&ye()},tt=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],ot=["click","dblclick","mousedown","mouseup"];Object.entries(ve).forEach(([a,J])=>{J&&(tt.includes(a)?u.push({event:a,listener:x}):ot.includes(a)&&u.push({event:a,listener:Ie}))}),Object.entries(Le).forEach(([a,J])=>{J&&(tt.includes(a)?u.push({event:a,listener:r}):ot.includes(a)&&u.push({event:a,listener:vt}))}),Q&&u.push({event:"pointermove",listener:we});const rt=()=>{pe.current=!0},lt=()=>{pe.current=!1,ye()};return I&&!xe&&((e=S.current)===null||e===void 0||e.addEventListener("mouseenter",rt),(l=S.current)===null||l===void 0||l.addEventListener("mouseleave",lt)),u.forEach(({event:a,listener:J})=>{s.forEach(Fe=>{var je;(je=Fe.current)===null||je===void 0||je.addEventListener(a,J)})}),()=>{var a,J;D.scroll&&(window.removeEventListener("scroll",E),W==null||W.removeEventListener("scroll",E),H==null||H.removeEventListener("scroll",E)),D.resize?window.removeEventListener("resize",E):g==null||g(),D.clickOutsideAnchor&&window.removeEventListener("click",$e),D.escape&&window.removeEventListener("keydown",L),I&&!xe&&((a=S.current)===null||a===void 0||a.removeEventListener("mouseenter",rt),(J=S.current)===null||J===void 0||J.removeEventListener("mouseleave",lt)),u.forEach(({event:Fe,listener:je})=>{s.forEach(mt=>{var Ye;(Ye=mt.current)===null||Ye===void 0||Ye.removeEventListener(Fe,je)})})}},[h,c,te,Te,Y,R,ce,be,fe,N,$]),t.useEffect(()=>{var e,l;let s=(l=(e=p==null?void 0:p.anchorSelect)!==null&&e!==void 0?e:A)!==null&&l!==void 0?l:"";!s&&n&&(s=`[data-tooltip-id='${n.replace(/'/g,"\\'")}']`);const w=new MutationObserver(E=>{const W=[],H=[];E.forEach(g=>{if(g.type==="attributes"&&g.attributeName==="data-tooltip-id"&&(g.target.getAttribute("data-tooltip-id")===n?W.push(g.target):g.oldValue===n&&H.push(g.target)),g.type==="childList"){if(h){const L=[...g.removedNodes].filter(u=>u.nodeType===1);if(s)try{H.push(...L.filter(u=>u.matches(s))),H.push(...L.flatMap(u=>[...u.querySelectorAll(s)]))}catch{}L.some(u=>{var Ie;return!!(!((Ie=u==null?void 0:u.contains)===null||Ie===void 0)&&Ie.call(u,h))&&(oe(!1),m(!1),ue(null),C(B),C(V),!0)})}if(s)try{const L=[...g.addedNodes].filter(u=>u.nodeType===1);W.push(...L.filter(u=>u.matches(s))),W.push(...L.flatMap(u=>[...u.querySelectorAll(s)]))}catch{}}}),(W.length||H.length)&&Re(g=>[...g.filter(L=>!H.includes(L)),...W])});return w.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{w.disconnect()}},[n,A,p==null?void 0:p.anchorSelect,h]),t.useEffect(()=>{c()},[c]),t.useEffect(()=>{if(!(G!=null&&G.current))return()=>null;const e=new ResizeObserver(()=>{setTimeout(()=>c())});return e.observe(G.current),()=>{e.disconnect()}},[Ee,G==null?void 0:G.current]),t.useEffect(()=>{var e;const l=document.querySelector(`[id='${y}']`),s=[...Y,l];h&&s.includes(h)||ue((e=Y[0])!==null&&e!==void 0?e:l)},[y,Y,h]),t.useEffect(()=>(ae&&m(!0),()=>{C(B),C(V)}),[]),t.useEffect(()=>{var e;let l=(e=p==null?void 0:p.anchorSelect)!==null&&e!==void 0?e:A;if(!l&&n&&(l=`[data-tooltip-id='${n.replace(/'/g,"\\'")}']`),l)try{const s=Array.from(document.querySelectorAll(l));Re(s)}catch{Re([])}},[n,A,p==null?void 0:p.anchorSelect]),t.useEffect(()=>{B.current&&(C(B),Ce(N))},[N]);const O=(Se=p==null?void 0:p.content)!==null&&Se!==void 0?Se:Ee,le=k&&Object.keys(F.tooltipStyles).length>0;return t.useImperativeHandle(o,()=>({open:e=>{if(e!=null&&e.anchorSelect)try{document.querySelector(e.anchorSelect)}catch{return void console.warn(`[react-tooltip] "${e.anchorSelect}" is not a valid CSS selector`)}Ae(e??null),e!=null&&e.delay?Ce(e.delay):m(!0)},close:e=>{e!=null&&e.delay?me(e.delay):m(!1)},activeAnchor:h,place:F.place,isOpen:!!(te&&!K&&O&&le)})),te&&!K&&O?ne.createElement(P,{id:n,role:Ue,className:Ge("react-tooltip",_e.tooltip,Ze.tooltip,Ze[f],d,`react-tooltip__place-${F.place}`,_e[le?"show":"closing"],le?"react-tooltip__show":"react-tooltip__closing",T==="fixed"&&_e.fixed,I&&_e.clickable),onTransitionEnd:e=>{C(ge),k||e.propertyName!=="opacity"||(oe(!1),Ae(null),M==null||M())},style:{...Be,...F.tooltipStyles,opacity:Ke!==void 0&&le?Ke:void 0},ref:S},O,ne.createElement(P,{className:Ge("react-tooltip-arrow",_e.arrow,Ze.arrow,i,U&&_e.noArrow),style:{...F.tooltipArrowStyles,background:Me?`linear-gradient(to right bottom, transparent 50%, ${Me} 50%)`:void 0},ref:de})):null},xt=({content:o})=>ne.createElement("span",{dangerouslySetInnerHTML:{__html:o}}),$t=ne.forwardRef(({id:o,anchorId:n,anchorSelect:d,content:i,html:f,render:y,className:A,classNameArrow:_,variant:v="dark",place:q="top",offset:z=10,wrapper:T="div",children:se=null,events:P=["hover"],openOnClick:N=!1,positionStrategy:$="absolute",middlewares:Q,delayShow:K=0,delayHide:U=0,float:I=!1,hidden:X=!1,noArrow:Z=!1,clickable:Je=!1,closeOnEsc:R=!1,closeOnScroll:ce=!1,closeOnResize:be=!1,openEvents:Qe,closeEvents:Be,globalCloseEvents:De,imperativeModeOnly:qe=!1,style:M,position:ie,isOpen:Ee,defaultIsOpen:G=!1,disableStyleInjection:j=!1,border:ae,opacity:ee,arrowColor:h,setIsOpen:ue,afterShow:ze,afterHide:Ke,disableTooltip:Me,role:Ue="tooltip"},Se)=>{const[S,de]=t.useState(i),[B,V]=t.useState(f),[ge,F]=t.useState(q),[Ve,k]=t.useState(v),[We,te]=t.useState(z),[oe,p]=t.useState(K),[Ae,ke]=t.useState(U),[Oe,Te]=t.useState(I),[He,pe]=t.useState(X),[Y,Re]=t.useState(T),[re,fe]=t.useState(P),[xe,ve]=t.useState($),[Le,D]=t.useState(null),[m,Pe]=t.useState(null),Ce=t.useRef(j),{anchorRefs:me,activeAnchor:Ne}=ft(o),ye=b=>b==null?void 0:b.getAttributeNames().reduce((x,r)=>{var c;return r.startsWith("data-tooltip-")&&(x[r.replace(/^data-tooltip-/,"")]=(c=b==null?void 0:b.getAttribute(r))!==null&&c!==void 0?c:null),x},{}),he=b=>{const x={place:r=>{var c;F((c=r)!==null&&c!==void 0?c:q)},content:r=>{de(r??i)},html:r=>{V(r??f)},variant:r=>{var c;k((c=r)!==null&&c!==void 0?c:v)},offset:r=>{te(r===null?z:Number(r))},wrapper:r=>{var c;Re((c=r)!==null&&c!==void 0?c:T)},events:r=>{const c=r==null?void 0:r.split(" ");fe(c??P)},"position-strategy":r=>{var c;ve((c=r)!==null&&c!==void 0?c:$)},"delay-show":r=>{p(r===null?K:Number(r))},"delay-hide":r=>{ke(r===null?U:Number(r))},float:r=>{Te(r===null?I:r==="true")},hidden:r=>{pe(r===null?X:r==="true")},"class-name":r=>{D(r)}};Object.values(x).forEach(r=>r(null)),Object.entries(b).forEach(([r,c])=>{var O;(O=x[r])===null||O===void 0||O.call(x,c)})};t.useEffect(()=>{de(i)},[i]),t.useEffect(()=>{V(f)},[f]),t.useEffect(()=>{F(q)},[q]),t.useEffect(()=>{k(v)},[v]),t.useEffect(()=>{te(z)},[z]),t.useEffect(()=>{p(K)},[K]),t.useEffect(()=>{ke(U)},[U]),t.useEffect(()=>{Te(I)},[I]),t.useEffect(()=>{pe(X)},[X]),t.useEffect(()=>{ve($)},[$]),t.useEffect(()=>{Ce.current!==j&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[j]),t.useEffect(()=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:j==="core",disableBase:j}}))},[]),t.useEffect(()=>{var b;const x=new Set(me);let r=d;if(!r&&o&&(r=`[data-tooltip-id='${o.replace(/'/g,"\\'")}']`),r)try{document.querySelectorAll(r).forEach(l=>{x.add({current:l})})}catch{console.warn(`[react-tooltip] "${r}" is not a valid CSS selector`)}const c=document.querySelector(`[id='${n}']`);if(c&&x.add({current:c}),!x.size)return()=>null;const O=(b=m??c)!==null&&b!==void 0?b:Ne.current,le=new MutationObserver(l=>{l.forEach(s=>{var w;if(!O||s.type!=="attributes"||!(!((w=s.attributeName)===null||w===void 0)&&w.startsWith("data-tooltip-")))return;const E=ye(O);he(E)})}),e={attributes:!0,childList:!1,subtree:!1};if(O){const l=ye(O);he(l),le.observe(O,e)}return()=>{le.disconnect()}},[me,Ne,m,n,d]),t.useEffect(()=>{M!=null&&M.border&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),ae&&!at("border",`${ae}`)&&console.warn(`[react-tooltip] "${ae}" is not a valid \`border\`.`),M!=null&&M.opacity&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),ee&&!at("opacity",`${ee}`)&&console.warn(`[react-tooltip] "${ee}" is not a valid \`opacity\`.`)},[]);let we=se;const $e=t.useRef(null);if(y){const b=y({content:(m==null?void 0:m.getAttribute("data-tooltip-content"))||S||null,activeAnchor:m});we=b?ne.createElement("div",{ref:$e,className:"react-tooltip-content-wrapper"},b):null}else S&&(we=S);B&&(we=ne.createElement(xt,{content:B}));const Xe={forwardRef:Se,id:o,anchorId:n,anchorSelect:d,className:Ge(A,Le),classNameArrow:_,content:we,contentWrapperRef:$e,place:ge,variant:Ve,offset:We,wrapper:Y,events:re,openOnClick:N,positionStrategy:xe,middlewares:Q,delayShow:oe,delayHide:Ae,float:Oe,hidden:He,noArrow:Z,clickable:Je,closeOnEsc:R,closeOnScroll:ce,closeOnResize:be,openEvents:Qe,closeEvents:Be,globalCloseEvents:De,imperativeModeOnly:qe,style:M,position:ie,isOpen:Ee,defaultIsOpen:G,border:ae,opacity:ee,arrowColor:h,setIsOpen:ue,afterShow:ze,afterHide:Ke,disableTooltip:Me,activeAnchor:m,setActiveAnchor:b=>Pe(b),role:Ue};return ne.createElement(Rt,{...Xe})});typeof window<"u"&&window.addEventListener("react-tooltip-inject-styles",o=>{o.detail.disableCore||ct({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),o.detail.disableBase||ct({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:8px;height:8px}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});export{$t as M};
