import{r as l}from"./vendor-851db8c1.js";import{G as n,b as p,s as c,aq as E}from"./index-13fd629e.js";const A=()=>{const{state:a,dispatch:e}=l.useContext(n);return{state:a,dispatch:e,toast:(t,o=3e3,s="success")=>{p(e,t,o,s)},updateClubProfile:t=>{c(e,t)},updateGlobalProjectRow:t=>{E(e,t)},setPath:t=>{e({type:"SETPATH",payload:{path:t}})},refreshData:(t=!0)=>{e({type:"REFRESH_DATA",payload:{refreshData:t}})},toggleSidebar:t=>{e({type:"OPEN_SIDEBAR",payload:{isOpen:t}})},toggleBackButton:t=>{e({type:"SHOW_BACKBUTTON",payload:{showBackButton:t}})},setLeftPanel:t=>{e({type:"SET_LEFT_PANEL",payload:t})},setMiddlePanel:t=>{e({type:"SET_MIDDLE_PANEL",payload:t})},setRightPanel:(t,o="")=>{e({type:"SET_RIGHT_PANEL",payload:t,rightComponentId:o})},setSelectedComponent:t=>{e({type:"SET_SELECTED_COMPONENT",payload:t})},setSelectedPageComponent:t=>{e({type:"SET_SELECTED_PAGE_COMPONENT",payload:t})},setRoom:(t,o)=>{e({type:"SETROOM",payload:{position:t,value:o}})}}};export{A as u};
