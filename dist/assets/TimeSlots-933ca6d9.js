import{j as i}from"./@nivo/heatmap-ba1ecfff.js";import{r as w}from"./vendor-851db8c1.js";import{d as st}from"./index-13fd629e.js";import{M as xt}from"./react-tooltip-7a26650a.js";import{f as pt,g as ht}from"./date-fns-cca0f4f7.js";const gt=({selectedDate:x,timeRange:y,onTimeClick:$,onNext:_,nextButtonText:nt="Next",startHour:z=8,endHour:U=24,interval:q=30,className:it="",multipleSlots:M=!1,timeSlots:yt=[],onTimeSlotsChange:I,individualSelection:wt=!1,isTimeSlotAvailable:It,clubTimes:k=[],isLoading:rt,coachAvailability:H=[],height:ot="h-fit",minBookingTime:B=30,enforceMinBookingTime:F=!1,availabilityData:v=null,loadingAvailability:lt=!1})=>{var K;const[N,P]=w.useState([]),[c,p]=w.useState([]),[at,E]=w.useState(350),ct=w.useRef(null),b=w.useCallback(()=>{const t=[];for(let e=z;e<=U;e++)for(let r=0;r<60;r+=q){const s=e===24?0:e,n=`${s.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`,a=s>=12?"PM":"AM",o=`${s===0?12:s>12?s-12:s}:${r.toString().padStart(2,"0")} ${a}`;t.push({time24:n,time12:o})}return t},[z,U,q]);w.useEffect(()=>{if(y&&y.length>0&&y[0].from&&y[0].until){const t=b(),e=y[0].from,r=y[0].until;if(!t.find(o=>o.time12===e))return;const n=t.findIndex(o=>o.time12===r);if(n===-1)return;const a=t.findIndex(o=>o.time12===e),l=[];for(let o=a;o<n;o++)l.push(t[o].time24);p(l)}else p([])},[y,b]),w.useEffect(()=>{const t=()=>{const e=window.innerHeight;e<600?E(200):e<800?E(300):E(350)};return t(),window.addEventListener("resize",t),()=>window.removeEventListener("resize",t)},[]);const J=t=>{if(!k||k.length===0)return!0;const[e,r]=t.split(":"),s=parseInt(e)*60+parseInt(r);return k.some(n=>{const[a,l]=n.from.split(":"),[o,m]=n.until.split(":"),h=parseInt(a)*60+parseInt(l),u=parseInt(o)*60+parseInt(m);return s>=h&&s<=u})},V=t=>N.some(e=>{const r=b(),s=r.findIndex(l=>l.time12===e.from),n=r.findIndex(l=>l.time12===e.until),a=r.findIndex(l=>l.time24===t);return a>=s&&a<=n}),W=t=>{if(!x||!H||H.length===0)return!0;const e=x.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase(),r=H.find(n=>n.day===e);if(!r)return!1;const s=`${t}:00`;return r.timeslots.includes(s)},Z=t=>{if(!x||!v)return!0;if(v.qualifying_courts&&v.qualifying_courts.length>0){const n=x.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase();return v.qualifying_courts.some(l=>{try{const o=l.availability?JSON.parse(l.availability):[];if(!o||o.length===0)return!0;const m=o.find(u=>u.day===n);if(!m||!m.timeslots)return!1;const h=`${t}:00`;return m.timeslots.includes(h)}catch(o){return console.warn(`Failed to parse availability for court ${l.id}:`,o),!0}})}if(!v.availability)return!0;const e=x.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase(),r=v.availability.find(n=>n.day===e);if(!r||!r.timeslots)return!1;const s=`${t}:00`;return r.timeslots.includes(s)},G=t=>{if(!x||!ht(x))return!1;const e=new Date,[r,s]=t.split(":").map(Number),n=new Date(x);return n.setHours(r,s,0,0),e>n},dt=t=>{var u,Q,X,Y;if(!Z(t.time24)||!W(t.time24)||!J(t.time24)||M&&V(t.time24)||G(t.time24))return;const e=b(),r=e.findIndex(d=>d.time24===t.time24);if(c.includes(t.time24)){if(c.length>=2){const d=[...c].sort();if(t.time24===d[0]||t.time24===d[d.length-1]){const T=c.filter(f=>f!==t.time24);p(T);const g=[...T].sort(),j=(u=e.find(f=>f.time24===g[0]))==null?void 0:u.time12;let L;const D=e.findIndex(f=>f.time24===g[g.length-1]),S=e[D+1];L=(S==null?void 0:S.time12)||((Q=e.find(f=>f.time24===g[g.length-1]))==null?void 0:Q.time12),$({from:j,until:L})}}else c.length===1&&(p([]),$({from:"",until:""}));return}let s;if(c.length===0)s=[t.time24],p(s);else{const d=[...c].sort(),T=d[d.length-1],g=e.findIndex(j=>j.time24===T);Math.abs(r-g)===1?(s=[...c,t.time24],p(s)):(s=[t.time24],p(s))}const n=[...s].sort(),a=(X=e.find(d=>d.time24===n[0]))==null?void 0:X.time12;let l;const o=e.findIndex(d=>d.time24===n[n.length-1]),m=e[o+1];l=(m==null?void 0:m.time12)||((Y=e.find(d=>d.time24===n[n.length-1]))==null?void 0:Y.time12);const h={from:a,until:l};if(F){const[d,T]=n[0].split(":").map(Number),[g,j]=n[n.length-1].split(":").map(Number),L=d*60+T,S=g*60+j-L+30;if(S<B){const O=Math.ceil((B-S)/30),f=[];for(let A=1;A<=O;A++){const et=o+A;et<e.length&&f.push(e[et].time24)}s=[...n,...f],p(s);const tt=e.findIndex(A=>A.time24===f[f.length-1]),R=e[tt+1];l=(R==null?void 0:R.time12)||e[tt].time12,h.until=l}}$(h)},mt=()=>{var m,h;if(c.length===0)return;const t=b(),e=[...c].sort(),r=(m=t.find(u=>u.time24===e[0]))==null?void 0:m.time12;let s;const n=t.findIndex(u=>u.time24===e[e.length-1]),a=t[n+1];s=(a==null?void 0:a.time12)||((h=t.find(u=>u.time24===e[e.length-1]))==null?void 0:h.time12);const l={from:r,until:s},o=[...N,l];P(o),p([]),I==null||I(o)},ut=t=>{const e=N.filter((r,s)=>s!==t);P(e),I==null||I(e)},ft=t=>c.includes(t),C=b();return i.jsxs("div",{className:`rounded-lg bg-white p-4 shadow-5 ${it} ${ot}`,children:[x&&i.jsx("p",{className:"text-center font-medium",children:pt(x,"EEEE, MMMM d, yyyy")}),F&&i.jsxs("div",{className:"mb-3 mt-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["Minimum booking time: ",B," minutes"]}),lt&&i.jsx("div",{className:"mb-3 mt-2 rounded-lg bg-yellow-50 p-3 text-center",children:i.jsxs("div",{className:"flex items-center justify-center gap-2",children:[i.jsxs("svg",{className:"h-4 w-4 animate-spin text-yellow-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[i.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),i.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i.jsx("span",{className:"text-sm text-yellow-700",children:"Loading available time slots..."})]})}),i.jsx("div",{ref:ct,className:"scrollbar-hide mb-5 mt-2 flex h-full flex-col gap-2 overflow-y-auto",style:{maxHeight:`${at}px`},children:C.map((t,e)=>{const r=J(t.time24),s=W(t.time24),n=Z(t.time24),a=M&&V(t.time24),l=G(t.time24),o=n&&s&&r&&!a&&!l,m=r?n?s?l?"Time has passed":"":"Coach not available":"Not available for selected sport/type":"Club Closed";return i.jsxs("button",{onClick:()=>dt(t),disabled:!o,"data-tooltip-id":`time-${e}`,"data-tooltip-content":m,type:"button",className:`
                rounded-lg border-[1.5px] px-4 py-2 text-sm font-medium transition-colors
                ${o?ft(t.time24)?"border-[1.5px] border-primaryBlue bg-primaryBlue/10 text-primaryBlue":"border-gray-200 text-gray-500 hover:bg-gray-50":"cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400"}
              `,children:[t.time12,!o&&i.jsx(xt,{id:`time-${e}`,place:"top",className:"z-50 !bg-gray-900 !px-2 !py-1 !text-xs !text-white"})]},t.time24)})}),c.length>0&&i.jsxs("div",{className:"space-y-2 border-t border-gray-200 pt-4",children:[i.jsxs("div",{className:"flex items-center justify-center gap-2 text-sm",children:[i.jsx("span",{className:"font-medium",children:"From: "}),i.jsx("span",{className:"text-primaryBlue",children:(K=C.find(t=>t.time24===c.sort()[0]))==null?void 0:K.time12}),i.jsx("span",{className:"font-medium",children:"Until: "}),i.jsx("span",{className:"text-primaryBlue",children:(()=>{var s;const t=[...c].sort(),e=C.findIndex(n=>n.time24===t[t.length-1]),r=C[e+1];return(r==null?void 0:r.time12)||((s=C.find(n=>n.time24===t[t.length-1]))==null?void 0:s.time12)})()})]}),M&&i.jsx(st,{className:"mt-2 w-full rounded-lg border border-primaryBlue bg-primaryBlue/10 px-4 py-2 text-primaryBlue hover:bg-primaryBlue/20",onClick:mt,disabled:c.length===0,children:"Add Time Range"})]}),M&&N.length>0&&i.jsxs("div",{className:"mt-4 space-y-2 border-t border-gray-200 pt-4",children:[i.jsx("p",{className:"text-center font-medium",children:"Selected Time Ranges"}),i.jsx("div",{className:"flex flex-col justify-center gap-2 ",children:N.map((t,e)=>i.jsxs("div",{className:"grid grid-cols-[auto_auto_auto_auto_auto] items-center gap-2 rounded-lg px-3 py-1 text-sm",children:[i.jsx("span",{className:"text-gray-500",children:"From"}),i.jsx("span",{children:t.from}),i.jsx("span",{children:"-"}),i.jsx("span",{className:"text-gray-500",children:"Until"}),i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx("span",{children:t.until}),i.jsx("button",{onClick:()=>ut(e),className:"text-primaryBlue hover:text-primaryBlue/80",children:i.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{d:"M15 9L9 15M15 15L9 9M21.25 12C21.25 17.1086 17.1086 21.25 12 21.25C6.89137 21.25 2.75 17.1086 2.75 12C2.75 6.89137 6.89137 2.75 12 2.75C17.1086 2.75 21.25 6.89137 21.25 12Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})})]})]},e))})]}),_&&i.jsx("div",{className:"sticky bottom-0 bg-white pt-2",children:i.jsx(st,{className:"mt-2 w-full rounded-lg bg-primaryBlue px-4 py-2 text-white disabled:opacity-50",onClick:_,disabled:M?N.length===0:c.length===0,loading:rt,children:nt})})]})},Mt=gt;export{Mt as T};
