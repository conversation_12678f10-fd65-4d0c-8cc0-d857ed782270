import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{r as m,b as h}from"./vendor-851db8c1.js";import{S as u}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./cal-heatmap-cf010ec4.js";const i=({children:a,counts:n=[1],count:c=1,circle:p=!1,brand:f=!1})=>{var r,o,t,l;const e=h.Children.toArray(a).filter(Boolean),x=(o=(r=e.filter(Boolean)[0])==null?void 0:r.props)!=null&&o.className?(l=(t=e[0])==null?void 0:t.props)==null?void 0:l.className:"";return s.jsx(m.Suspense,{fallback:f?s.jsx("div",{className:"flex h-svh max-h-svh min-h-svh w-full min-w-full max-w-full items-center justify-center bg-black",children:s.jsx(i,{})}):s.jsx(u,{counts:n,count:c,className:x,circle:p}),children:a})},k=m.memo(i);export{k as default};
