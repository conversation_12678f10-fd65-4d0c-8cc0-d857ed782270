import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as Ae,r as s,b as $e,k as Ye,L as Fe}from"./vendor-851db8c1.js";import{G as je,A as He,u as ve,aF as Ve,b as U,aB as ts,ab as fe,P as V,d as Le,aR as Ue,M as Ne,T as Te,t as as,aG as Ge,X as Ze,Z as qe,c as Je,ao as ns,e as Qe,aP as rs,aQ as is}from"./index-13fd629e.js";import{c as We,e as ls}from"./index.esm-3f8dc7b8.js";import{B as Ie}from"./BottomDrawer-3018f655.js";import{A as os}from"./AddPlayers-6917a66e.js";import{B as ze}from"./BackButton-11ba52b2.js";import{L as De}from"./ReservationSummary-5582c1fb.js";import{g as Ke}from"./customThresholdUtils-f40b07d5.js";import{f as pe}from"./date-fns-cca0f4f7.js";import{T as Xe}from"./TimeSlots-933ca6d9.js";import{C as es}from"./Calendar-35bce269.js";import{S as ss}from"./SportTypeSelection-1b7f983d.js";import{C as Pe}from"./CalendarIcon-b3488133.js";import{h as ds}from"./moment-a9aaa855.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./index.esm-09a3a6b8.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./SelectionOptionsCard-30c39f7f.js";import"./SelectionOption-658322e6.js";let _e=new Ne,cs=new Te;function ms({isOpen:c,onClose:d,selectedDate:i,players:D,sports:j,selectedSport:k,groups:M,coach:m,selectedTimes:u,selectedType:A,selectedSubType:x,userProfile:g}){var de,we;Ae();const[R,F]=s.useState(""),[t,L]=s.useState([]),[G,v]=s.useState(!1),[I,n]=s.useState(!1),[p,Z]=s.useState(4),[X,J]=s.useState(null),[h,P]=s.useState(1),[r,Q]=s.useState([]),[C,le]=s.useState(null),[W,re]=s.useState(!1),[ce,z]=s.useState(null),{dispatch:ee}=s.useContext(je);s.useContext(He);const[oe,l]=s.useState(null),[O,$]=s.useState(null),[b,N]=s.useState(null),[B,o]=s.useState(null),[T,_]=s.useState(!1),[E,K]=s.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),{user_subscription:he,user_permissions:se,club:S}=ve(),me=localStorage.getItem("user"),ue=u.reduce((y,f)=>{const ge=new Date(`2000/01/01 ${f.from}`),ye=(new Date(`2000/01/01 ${f.until}`)-ge)/(1e3*60*60);return y+ye},0),te=Ke(S==null?void 0:S.custom_request_threshold,k,A,x,4,j),a=Ve({hourlyRate:m==null?void 0:m.hourly_rate,hours:ue,playerCount:t.length,feeSettings:S==null?void 0:S.fee_settings});$e.useEffect(()=>{t.length>te&&(console.log(`Clearing selected players: current ${t.length} exceeds new threshold ${te}`),L([]),Z(1),U(ee,`Player selection cleared. New maximum is ${te} players. Please select players again.`,4e3,"warning"))},[te]);const Y=()=>{Z(y=>Math.min(y+1,te))},ae=()=>{Z(y=>Math.max(y-1,0))};async function ne(){var y;try{re(!0);const{data:f,limit:ge,error:q,message:ye}=await _e.getCustomerStripeCards();if(q&&U(ee,ye,5e3,"error"),!f)return;const ie=(y=f==null?void 0:f.data)==null?void 0:y.find(be=>{var Be,Me;return be.id===((Me=(Be=f==null?void 0:f.data[0])==null?void 0:Be.customer)==null?void 0:Me.default_source)});z(ie)}catch(f){console.error("ERROR",f),U(ee,f.message,5e3,"error"),as(dispatch,f.code)}finally{re(!1)}}async function xe(){try{const y=await _e.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:a.total},"POST");N(y.client_secret),o(y.payment_intent)}catch(y){console.error(y)}finally{_(!1)}}const Se=async()=>{if(!(he!=null&&he.planId)){K({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to book lessons with coaches",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(se!=null&&se.allowCoach)){K({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${se==null?void 0:se.planName}) does not include coach lessons. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(!t.length){K({isOpen:!0,title:"Players Required",message:"Please select at least one player",type:"warning"});return}const{duration:y,start_time:f,end_time:ge}=fe(u);v(!0);try{const q=pe(new Date(i),"yyyy-MM-dd"),ye={sport_id:k,type:A,sub_type:x,date:q,start_time:f,end_time:ge,duration:y,court_id:1,price:a.total,coach_fee:a.coachFee,service_fee:a.serviceFee,reservation_type:Ge.lesson,player_ids:t.map(be=>be.id),primary_player_id:(C==null?void 0:C.id)||(g==null?void 0:g.id),buddy_details:null,payment_status:0,payment_intent:null,coach_id:m==null?void 0:m.id},ie=await _e.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",ye,"POST");_e.setTable("activity_logs"),await _e.callRestAPI({user_id:me,activity_type:Ze.lesson,action_type:qe.CREATE,data:JSON.stringify(ye),club_id:S==null?void 0:S.id,description:"Created a lesson reservation"},"POST"),ie.error||($(ie.reservation_id),l(ie.booking_id),await xe(),P(2))}catch(q){console.error("ERROR",q),K({isOpen:!0,title:"Reservation Error",message:q.message||"Error creating lesson reservation",type:"error"})}finally{v(!1)}},Ce=async()=>{try{const y=parseInt(me);if(!me||isNaN(y)){console.error("Invalid user_id for fetching family members:",me);return}const f=await cs.getList("user",{filter:[`guardian,eq,${y}`,"role,cs,user"]});Q(f.list)}catch(y){console.error("Error fetching family members:",y)}},ke=y=>{const f=y.value||y;(f==null?void 0:f.id)!==(C==null?void 0:C.id)&&(le(f),L(ge=>{const q=ge.filter(ie=>ie.id!==(C==null?void 0:C.id));if(q.some(ie=>ie.id===f.id)){const ie=q.filter(be=>be.id!==f.id);return[f,...ie]}else return[f,...q]}))},w=y=>{L(f=>f.some(q=>q.id===y.id)?f.filter(q=>q.id!==y.id):[...f,y])};$e.useEffect(()=>{ne(),Ce()},[]),$e.useEffect(()=>{g&&!C&&le(g)},[g,C]);const H=S!=null&&S.lesson_description?JSON.parse(S==null?void 0:S.lesson_description):{reservation_description:"",payment_description:""};return e.jsxs(e.Fragment,{children:[e.jsx(ts,{isOpen:E.isOpen,onClose:()=>K({...E,isOpen:!1}),title:E.title,message:E.message,actionButtonText:E.actionButtonText,actionButtonLink:E.actionButtonLink,type:E.type}),e.jsx(Ie,{isOpen:c,onClose:d,title:"Reservation detail",children:e.jsxs("div",{className:"mx-auto max-w-7xl space-y-6",children:[e.jsx(ze,{onBack:()=>{h===2?P(1):d()}}),h===1&&e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx(De,{selectedSport:k,sports:j,selectedType:A,selectedSubType:x,selectedDate:i,selectedTimes:u,playersNeeded:p,selectedCoach:m,timeRange:fe(u)}),e.jsx("div",{className:"space-y-4",children:e.jsx(os,{searchQuery:R,setSearchQuery:F,selectedPlayers:t,setSelectedPlayers:L,onPlayerToggle:w,players:D,groups:M,selectedGroup:X,isFindBuddyEnabled:I,setIsFindBuddyEnabled:n,playersNeeded:p,handleIncrement:Y,handleDecrement:ae,showPlayersNeeded:!1,showAddReservationToFindBuddy:!1,maximumPlayers:te,familyMembers:r,currentUser:C,onCurrentUserChange:ke,userProfile:g})}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-2  ",children:[e.jsxs("div",{className:"divide-y",children:[e.jsxs("div",{className:"py-3",children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["PLAYERS (",t.length,")"]}),e.jsx("div",{className:"mt-1",children:t.map(y=>e.jsxs("div",{className:"text-sm",children:[y.first_name," ",y.last_name]},y.id))})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"COACH"}),e.jsx("div",{className:"mt-1",children:e.jsxs("div",{className:"text-sm",children:[(de=m==null?void 0:m.user)==null?void 0:de.first_name," ",(we=m==null?void 0:m.user)==null?void 0:we.last_name]})})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsxs("span",{className:"flex items-center gap-1",children:["Coach fee",e.jsxs("span",{className:"text-xs text-gray-500",children:["(",V(m==null?void 0:m.hourly_rate),"/hr ×"," ",ue,"hr × ",t.length," ","players)"]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{children:V(a.coachFee)})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Service fee"}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{children:V(a.serviceFee)})})]})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:V(a.total)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 8 2.33325C8.10005 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx(Le,{loading:G,onClick:Se,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{children:"Reserve Now"}),e.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:H.reservation_description}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:"(You will not be charged yet)"})]})})]})]}),h===2&&e.jsx("div",{children:e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx(De,{selectedSport:k,sports:j,selectedType:A,selectedSubType:x,selectedDate:i,selectedTimes:u,playersNeeded:p,timeRange:fe(u),selectedCoach:m}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"flex items-center gap-1",children:["Coach fee",e.jsxs("span",{className:"text-xs text-gray-500",children:["(",V(m==null?void 0:m.hourly_rate),"/hr ×"," ",ue,"hr × ",t.length," ","players)"]})]}),e.jsx("span",{children:V(a.coachFee)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:V(a.serviceFee)})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:V(a.total)})]}),e.jsxs("div",{children:[e.jsx(Ue,{user:g,bookingId:oe,reservationId:O,clientSecret:b,paymentIntent:B,navigateRoute:`/user/payment-success/${O}?type=lesson`}),e.jsx("p",{className:"text-sm text-gray-500",children:H.payment_description})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur erat nisi, porta a ipsum eu, accumsan dapibus enim. Donec ultrices congue libero in convallis. Cras condimentum felis eget dignissim tincidunt."})]})]})})]})]})})]})})]})}function us({isOpen:c,onClose:d,coach:i,players:D,selectedLocation:j,selectedSport:k,sports:M,groups:m,club:u,selectedType:A,selectedSubType:x,userProfile:g,coachAvailability:R}){var oe,l,O,$,b;const[F,t]=s.useState(new Date),[L,G]=s.useState(new Date),[v,I]=s.useState({from:null,until:null}),[n,p]=s.useState(!1),[Z,X]=s.useState([]),{dispatch:J}=s.useContext(je),{club_membership:h,user_subscription:P}=ve(),r=s.useMemo(()=>!(P!=null&&P.planId)||!(h!=null&&h.length)?null:h.find(N=>N.plan_id===P.planId),[P,h]),Q=s.useMemo(()=>{var T,_;if(((T=r==null?void 0:r.advance_booking_enabled)==null?void 0:T.lesson)===!1){const E=new Date;return E.setFullYear(E.getFullYear()+10),E}const N=((_=r==null?void 0:r.advance_booking_days)==null?void 0:_.lesson)||10,B=new Date,o=new Date;return o.setDate(B.getDate()+N),o},[r]),C=N=>{if(!(i!=null&&i.availability)||!F)return!1;const B=pe(F,"EEEE").toLowerCase(),T=(Array.isArray(i.availability)?i.availability:Object.entries(i.availability).map(([E,K])=>({day:E,timeslots:K}))).find(E=>E.day===B);if(!T)return!1;const _=`${N.time24}:00`;return T.timeslots.includes(_)},le=()=>{G(new Date(L.setMonth(L.getMonth()-1)))},W=()=>{G(new Date(L.setMonth(L.getMonth()+1)))},re=()=>{var N,B;if(F>Q&&((N=r==null?void 0:r.advance_booking_enabled)==null?void 0:N.lesson)!==!1){const o=((B=r==null?void 0:r.advance_booking_days)==null?void 0:B.lesson)||10;U(J,`Your membership plan only allows booking ${o} days in advance`,3e3,"warning");return}p(!0)},ce=()=>{p(!1)},z=N=>{console.log("Selected players:",N.selectedPlayers)},ee=N=>{X([{from:N.from,until:N.until}])};return e.jsxs(e.Fragment,{children:[e.jsx(Ie,{isOpen:c&&!n,onClose:d,title:`${(oe=i==null?void 0:i.user)==null?void 0:oe.first_name}'s availability`,children:e.jsx("div",{className:"mx-auto max-w-3xl space-y-6 overflow-hidden",children:e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsxs("div",{children:[((l=r==null?void 0:r.advance_booking_enabled)==null?void 0:l.lesson)===!1?e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can reserve a lesson for any future date."}):e.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["Your ",(r==null?void 0:r.plan_name)||"current"," membership plan allows you to book lessons up to"," ",((O=r==null?void 0:r.advance_booking_days)==null?void 0:O.lesson)||10," ","days in advance."]}),e.jsx(es,{currentMonth:L,selectedDate:F,onDateSelect:N=>{var B,o;if(N>Q&&((B=r==null?void 0:r.advance_booking_enabled)==null?void 0:B.lesson)!==!1){const T=((o=r==null?void 0:r.advance_booking_days)==null?void 0:o.lesson)||10;U(J,`Your membership plan only allows booking ${T} days in advance`,3e3,"warning");return}t(N)},onPreviousMonth:le,onNextMonth:W,daysOff:u!=null&&u.days_off?JSON.parse(u.days_off):[],coachAvailability:R,allowPastDates:!1,minDate:new Date,maxDate:Q,disabledDateMessage:(($=r==null?void 0:r.advance_booking_enabled)==null?void 0:$.lesson)===!1?"You can book for any future date":`Your membership plan only allows booking ${((b=r==null?void 0:r.advance_booking_days)==null?void 0:b.lesson)||10} days in advance`})]})}),e.jsx(Xe,{selectedDate:F,onTimeClick:ee,onNext:re,nextButtonText:"Next: Players",startHour:0,endHour:24,interval:30,isTimeSlotAvailable:C,timeRange:Z,clubTimes:u!=null&&u.times?JSON.parse(u.times):[],coachAvailability:R,height:"h-full"})]})})}),e.jsx(ms,{players:D,isOpen:n,onClose:ce,selectedDate:F,timeRange:v,onNext:z,selectedSport:k,selectedLocation:j,sports:M,groups:m,coach:i,club:u,selectedTimes:Z,selectedType:A,selectedSubType:x,userProfile:g})]})}function xs({selectedCoach:c,onCheckAvailability:d,loadingAvailability:i}){var D,j,k,M,m;return c?e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Coach profile"}),e.jsx(Le,{onClick:d,className:"rounded-xl bg-blue-900 px-3 py-2 text-sm text-white hover:bg-blue-800",loading:i,children:i?"Checking...":"Check availability"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:c.photo||((D=c.user)==null?void 0:D.photo)||"/default-avatar.png",alt:`${(j=c.user)==null?void 0:j.first_name} ${(k=c.user)==null?void 0:k.last_name}`,className:"h-12 w-12 rounded-lg object-cover"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-medium capitalize",children:[(M=c.user)==null?void 0:M.first_name," ",(m=c.user)==null?void 0:m.last_name]}),e.jsxs("p",{className:"text-sm text-gray-600",children:[V(c.hourly_rate),"/h"]})]})]}),c.bio&&e.jsx("div",{className:"space-y-2",children:e.jsx("p",{className:"text-gray-600",children:c.bio})})]})}):null}let ps=new Ne;function hs({sports:c,coaches:d,players:i,groups:D,club:j,userProfile:k}){var ce,z,ee,oe;const[M,m]=s.useState("Tennis"),[u,A]=s.useState("Indoors"),[x,g]=s.useState(null),[R,F]=s.useState(""),[t,L]=s.useState(!0),[G,v]=s.useState(!1),[I,n]=s.useState(null),[p,Z]=s.useState(null),[X,J]=s.useState(!1),[h,P]=s.useState(null),[r]=Ye(),Q=r.get("coach"),{user_permissions:C,courts:le}=ve();s.useEffect(()=>{if(Q){const l=d.find(O=>O.id===parseInt(Q));g(l)}},[Q,d]);const W=c==null?void 0:c.find(l=>l.id===M),re=async()=>{J(!0);try{const l=await ps.callRawAPI(`/v3/api/custom/courtmatchup/user/coach/availability/${x.id}`,{},"GET");P(l.availability),v(!0),console.log(l)}catch(l){console.log(l)}finally{J(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(ss,{sports:c,userPermissions:C,courts:le,filterMode:"lesson",onSelectionChange:({sport:l,type:O,subType:$})=>{m(l),n(O),Z($)}}),M&&(!((ce=W==null?void 0:W.sport_types)!=null&&ce.length)||I!==null&&(p!==null||!((oe=(ee=(z=W==null?void 0:W.sport_types)==null?void 0:z.find(l=>l.type===I))==null?void 0:ee.subtype)!=null&&oe.length)))?e.jsxs("div",{className:"max-h-fit space-y-6 rounded-xl bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search by name",value:R,onChange:l=>F(l.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none"}),e.jsx(Je,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("button",{onClick:()=>L(!t),className:"absolute right-2 top-1/2 -translate-y-1/2 transform rounded-md border border-gray-300 px-2 py-1 text-sm hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:"A-Z"}),e.jsx(We,{className:`text-xs transition-transform ${t?"":"rotate-180"}`})]})})]}),e.jsxs("div",{className:"max-h-[400px] space-y-4 overflow-y-auto",children:[d.length>0&&d.filter(l=>{var $,b;return`${($=l.user)==null?void 0:$.first_name} ${(b=l.user)==null?void 0:b.last_name}`.toLowerCase().includes(R.toLowerCase())}).sort((l,O)=>{var N,B,o,T;const $=`${(N=l.user)==null?void 0:N.first_name} ${(B=l.user)==null?void 0:B.last_name}`.toLowerCase(),b=`${(o=O.user)==null?void 0:o.first_name} ${(T=O.user)==null?void 0:T.last_name}`.toLowerCase();return t?$.localeCompare(b):b.localeCompare($)}).map(l=>{var O,$,b,N,B;return e.jsxs("div",{className:`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${(x==null?void 0:x.id)===l.id?"border-primaryBlue bg-blue-50":"border-gray-100 hover:bg-gray-50"}`,onClick:()=>g(l),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:((O=l.user)==null?void 0:O.photo)||(l==null?void 0:l.photo)||"/default-avatar.png",alt:`${($=l.user)==null?void 0:$.first_name} ${(b=l.user)==null?void 0:b.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("span",{className:"font-medium capitalize",children:[(N=l.user)==null?void 0:N.first_name," ",(B=l.user)==null?void 0:B.last_name]})})]}),e.jsxs("span",{className:"text-gray-600",children:[V(l.hourly_rate),"/h"]})]},l.id)}),d.length===0&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No coaches found"})]})]}):e.jsx("div",{className:"col-span-2 flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5",children:e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Please select a sport"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose a sport, type, and sub-type to view available coaches"})]})}),e.jsx(xs,{selectedCoach:x,onCheckAvailability:re,loadingAvailability:X})]})})}),e.jsx(us,{isOpen:G,coachAvailability:h,onClose:()=>v(!1),selectedSport:M,selectedLocation:u,sports:c,groups:D,club:j,players:i,selectedType:I,selectedSubType:p,userProfile:k,coach:{...x,availability:JSON.parse((x==null?void 0:x.availability)||"{}")}})]})}let gs=new Ne;function ys({userProfile:c}){const[d,i]=s.useState(null),[D,j]=s.useState(!1),[k,M]=s.useState([]),{dispatch:m}=s.useContext(je),{dispatch:u}=s.useContext(He),[A,x]=s.useState([]),[g,R]=s.useState(!1),{sports:F}=ve();s.useEffect(()=>{const n=()=>{R(window.innerWidth<768)};return n(),window.addEventListener("resize",n),()=>window.removeEventListener("resize",n)},[]);async function t(){try{j(!0);const n=await gs.callRawAPI("/v3/api/custom/courtmatchup/user/reservations?custom_request=1",{},"GET");M(n.list)}catch(n){console.error(n)}finally{j(!1)}}s.useEffect(()=>{t()},[]),s.useEffect(()=>{k.length>0&&!d&&(i(k[0]),I(k[0]))},[k]);const L=n=>{switch(n){case 0:return"border-gray-300 border bg-gray-50 text-gray-600";case 1:return"border-green-300 border bg-green-50 text-green-600";case 2:return"border-red-300 border bg-red-50 text-red-600";default:return"border-gray-300 border bg-gray-50 text-gray-600"}},G=n=>{switch(n){case 0:return"Pending";case 1:return"Approved";case 2:return"Declined";default:return"Pending"}},v=(n,p,Z)=>{const X=F.find(h=>h.id===n);return`${X?X.name:"Unknown Sport"} • ${p} • ${Z}`},I=async n=>{j(!0);try{const p=JSON.parse(n.player_ids),Z=await ns(m,u,"user",p);i(n),x(Z.list)}catch(p){console.error(p),U(m,p.message,5e3,"error")}finally{j(!1)}};return e.jsxs("div",{className:"relative flex h-full flex-col gap-3 p-2 md:flex-row",children:[D&&e.jsx(Qe,{}),e.jsx("div",{className:`${g?"w-full":"w-full md:w-1/3"} max-h-[300px] space-y-4 overflow-y-auto p-2 md:max-h-[600px]`,children:k.length>0?k.map(n=>e.jsxs("div",{onClick:()=>I(n),className:`cursor-pointer rounded-lg bg-white p-4 shadow-sm transition-shadow hover:shadow-md ${(d==null?void 0:d.reservation_id)===n.reservation_id?"ring-2 ring-blue-500":""}`,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between",children:e.jsx("span",{className:`rounded-full px-3 py-1 text-xs font-medium ${L(n.booking_status)}`,children:G(n.booking_status)})}),e.jsxs("div",{className:"mb-2 flex items-center text-sm text-gray-600",children:[e.jsx("span",{className:"mr-1",children:e.jsx(Pe,{className:"h-4 w-4"})}),e.jsxs("div",{className:"text-xs md:text-sm",children:[pe(new Date(`${n.booking_date}T${n.start_time}`),"EEEE (MMM dd) • h:mmaaa")," ","-",pe(new Date(`${n.booking_date}T${n.end_time}`),"h:mmaaa")]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-xs font-medium md:text-sm",children:v(n.sport_id,n.sub_type,n.type)}),e.jsxs("div",{className:"flex items-center text-xs text-gray-600 md:text-sm",children:[e.jsx(ls,{className:"mr-1"}),e.jsx("span",{children:n.num_players})]})]})]},n.reservation_id)):!D&&e.jsxs("div",{className:"flex h-full w-full flex-col items-center justify-center rounded-lg bg-white p-6 text-center shadow-sm",children:[e.jsx("div",{className:"mb-4 text-gray-400",children:e.jsx(Pe,{className:"mx-auto h-12 w-12"})}),e.jsx("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"No Custom Requests"}),e.jsx("p",{className:"text-sm text-gray-600",children:"You don't have any custom requests at the moment."})]})}),d&&k.length>0&&e.jsxs("div",{className:`${g?"mt-4 w-full":"w-full md:w-2/3"} rounded-lg bg-white shadow-sm md:sticky md:top-20`,children:[e.jsx("div",{className:"rounded-xl bg-gray-100 p-4 text-center",children:e.jsx("p",{className:"text-sm md:text-base",children:"Custom request details"})}),e.jsxs("div",{className:"space-y-6 p-4",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-2 rounded-xl bg-gray-100 p-2 text-xs sm:grid-cols-2 md:grid-cols-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"STATUS"}),e.jsx("span",{className:`rounded-full px-3 py-1 text-xs font-medium ${L(d.booking_status)}`,children:G(d.booking_status)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"SPORT"}),e.jsx("p",{className:"text-xs md:text-sm",children:v(d.sport_id,d.sub_type,d.type)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"REQUEST DATE"}),e.jsxs("p",{className:"text-xs md:text-sm",children:[pe(new Date(`${d.booking_date}T${d.start_time}`),"EEEE (MMM dd) • h:mmaaa")," ","-",pe(new Date(`${d.booking_date}T${d.end_time}`),"h:mmaaa")]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"SUBMITTED ON"}),e.jsx("p",{className:"text-xs md:text-sm",children:pe(new Date(d.reservation_created_at),"MMMM dd, yyyy, (EEEE)")})]}),A.length>0&&e.jsxs("div",{children:[e.jsxs("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:["PLAYERS (",A.length,")"]}),e.jsx("div",{className:"flex flex-col gap-2 rounded-lg border border-gray-300 bg-gray-100 p-3",children:A.map(n=>e.jsxs("div",{className:"rounded-full text-xs md:text-sm",children:[n.first_name," ",n.last_name]},n.id))})]}),d.notes&&e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"REQUEST"}),e.jsx("p",{className:"text-xs text-gray-700 md:text-sm",children:d.notes})]})]})]})]})}const Re=new Ne,fs=new Te;function vs({coaches:c,onClose:d,selectedDate:i,selectedSport:D,sports:j,players:k,groups:M,isOpen:m,selectedType:u,selectedSubType:A,selectedTimes:x,userProfile:g}){const[R,F]=s.useState(!0),[t,L]=s.useState(null),[G,v]=s.useState(""),[I,n]=s.useState(1),[p,Z]=s.useState([]),[X,J]=s.useState(1);s.useState(!1),s.useState(null);const[h,P]=s.useState(!1),[r,Q]=s.useState([]),[C,le]=s.useState(null);Ae();const{duration:W,end_time:re,start_time:ce}=fe(x),[z,ee]=s.useState(null),[oe,l]=s.useState(null),[O,$]=s.useState(null),{dispatch:b}=s.useContext(je),[N,B]=s.useState(null),{user_subscription:o,user_permissions:T,club:_}=ve(),E=localStorage.getItem("user");console.log("selectedTimes",x);const K=Ke(_==null?void 0:_.custom_request_threshold,D,u,A,4,j);React.useEffect(()=>{p.length>K&&(console.log(`Clearing selected players: current ${p.length} exceeds new threshold ${K}`),Z([]),J(1),U(b,`Player selection cleared. New maximum is ${K} players. Please select players again.`,4e3,"warning"))},[K]);const he=async()=>{try{const a=parseInt(E);if(!E||isNaN(a)){console.error("Invalid user_id for fetching family members:",E);return}const Y=await fs.getList("user",{filter:[`guardian,eq,${a}`,"role,cs,user"]});Q(Y.list)}catch(a){console.error("Error fetching family members:",a)}};React.useEffect(()=>{g&&!C&&le(g),he()},[g,C]);const se=x.reduce((a,Y)=>{const ae=new Date(`2000/01/01 ${Y.from}`),xe=(new Date(`2000/01/01 ${Y.until}`)-ae)/(1e3*60*60);return a+xe},0),S=Ve({hourlyRate:t==null?void 0:t.hourly_rate,hours:se,playerCount:p.length,feeSettings:_==null?void 0:_.fee_settings}),me=async()=>{if(!(o!=null&&o.planId)){U(b,"Please subscribe to a membership plan to book lessons with coaches",3e3,"error");return}if(!(T!=null&&T.allowCoach)){U(b,`Your current plan (${T==null?void 0:T.planName}) does not include coach lessons. Please upgrade your plan.`,3e3,"error");return}if(!D||!i||!x||!p){U(b,"Please select all required fields",3e3,"error");return}P(!0);try{const a=await Re.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:S.total},"POST");if(a.error){U(b,a.message,3e3,"error"),P(!1);return}ee(a.client_secret),l(a.payment_intent);const Y=ds(i).format("YYYY-MM-DD"),ae={sport_id:D,type:u,sub_type:A,date:Y,player_ids:p.map(xe=>xe.id),primary_player_id:(C==null?void 0:C.id)||(g==null?void 0:g.id),start_time:ce,end_time:re,price:S.total,coach_fee:S.coachFee,service_fee:S.serviceFee,duration:W,coach_id:t.id,payment_intent:a.payment_intent,reservation_type:Ge.lesson},ne=await Re.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",ae,"POST");Re.setTable("activity_logs"),await Re.callRestAPI({user_id:E,activity_type:Ze.lesson,action_type:qe.CREATE,data:JSON.stringify(ae),club_id:_==null?void 0:_.id,description:"Created a lesson reservation"},"POST"),ne.error||(U(b,"Reservation created successfully",3e3,"success"),B(ne.reservation_id),$(ne.booking_id),n(3))}catch(a){console.error(a),U(b,a.message||"Error creating reservation",3e3,"error")}finally{P(!1)}},ue=()=>{t&&n(2)},te=_!=null&&_.lesson_description?JSON.parse(_==null?void 0:_.lesson_description):{reservation_description:"",payment_description:""};return e.jsx(Ie,{onClose:d,isOpen:m,title:I===1?"Select Coach":"Reservation detail",children:e.jsxs("div",{className:"relative mx-auto h-[90vh] w-full max-w-7xl overflow-y-auto rounded-lg ",children:[e.jsx(ze,{onBack:()=>{I===1?n(2):d()}}),I===1&&e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"max-h-fit space-y-6 rounded-xl bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search by name",value:G,onChange:a=>v(a.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none"}),e.jsx(Je,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("button",{onClick:()=>F(!R),className:"absolute right-2 top-1/2 -translate-y-1/2 transform rounded-md border border-gray-300 px-2 py-1 text-sm hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:"A-Z"}),e.jsx(We,{className:`text-xs transition-transform ${R?"":"rotate-180"}`})]})})]}),e.jsxs("div",{className:"max-h-[calc(90vh-200px)] space-y-4 overflow-y-auto",children:[c.length>0&&c.filter(a=>`${a==null?void 0:a.first_name} ${a==null?void 0:a.last_name}`.toLowerCase().includes(G.toLowerCase())).sort((a,Y)=>{const ae=`${a==null?void 0:a.first_name} ${a==null?void 0:a.last_name}`.toLowerCase(),ne=`${Y==null?void 0:Y.first_name} ${Y==null?void 0:Y.last_name}`.toLowerCase();return R?ae.localeCompare(ne):ne.localeCompare(ae)}).map(a=>e.jsxs("div",{className:`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${(t==null?void 0:t.id)===a.id?"border-primaryBlue bg-blue-50":"border-gray-100 hover:bg-gray-50"}`,onClick:()=>L(a),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:(a==null?void 0:a.photo)||(a==null?void 0:a.photo)||"/default-avatar.png",alt:`${a==null?void 0:a.first_name} ${a==null?void 0:a.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("span",{className:"font-medium capitalize",children:[a==null?void 0:a.first_name," ",a==null?void 0:a.last_name]})})]}),e.jsxs("span",{className:"text-gray-600",children:[V(a.hourly_rate),"/h"]})]},a.id)),c.length===0&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No coaches found"})]})]}),e.jsx("div",{children:t&&e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsxs("div",{className:"space-y-6 p-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h2",{className:"text-xl font-semibold",children:"Coach Profile"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:(t==null?void 0:t.photo)||(t==null?void 0:t.photo)||"/default-avatar.png",alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-16 w-16 rounded-lg object-cover"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-medium capitalize",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsxs("p",{className:"text-lg text-gray-600",children:[V(t==null?void 0:t.hourly_rate),"/h"]})]})]}),(t==null?void 0:t.bio)&&e.jsx("div",{className:"space-y-2",children:e.jsx("p",{className:"text-gray-600",children:t==null?void 0:t.bio})})]}),e.jsx("div",{className:"border-t p-3",children:e.jsxs("button",{onClick:ue,className:"rounded-lg bg-blue-900 px-4 py-2 text-white hover:bg-blue-800",children:["Continue with ",t==null?void 0:t.first_name]})})]})})]}),I===2&&e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx(De,{selectedSport:D,sports:j,selectedType:u,selectedSubType:A,selectedDate:i,timeRange:fe(x),playersNeeded:X,selectedCoach:t}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving Details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"divide-y",children:[e.jsxs("div",{className:"py-3",children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["PLAYERS (",p.length,")"]}),e.jsx("div",{className:"mt-1",children:p.map(a=>e.jsxs("div",{className:"text-sm",children:[a.first_name," ",a.last_name]},a.id))})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"COACH"}),e.jsx("div",{className:"mt-1",children:e.jsxs("div",{className:"text-sm",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]})})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{className:"flex items-center gap-1",children:"Coach fee"}),e.jsx("span",{children:V(S.coachFee)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Service fee"}),e.jsx("span",{children:V(S.serviceFee)})]})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:V(S.total)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 8 2.33325C8.10005 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx(Le,{loading:h,onClick:me,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{children:"Reserve Now"}),e.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:te.reservation_description}),e.jsxs("div",{className:"space-y-2 text-center text-sm text-gray-500",children:[e.jsx("p",{children:"(You will not be charged yet)"}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})})]})]}),I===3&&e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsx(De,{selectedSport:D,sports:j,selectedType:u,selectedSubType:A,selectedDate:i,timeRange:fe(x),selectedCoach:t})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Coach fee"}),e.jsxs("span",{children:[V(t==null?void 0:t.hourly_rate),"/hr ×"," ",se,"hr × ",p.length," players"]}),e.jsx("span",{children:V((t==null?void 0:t.hourly_rate)*se*p.length)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:V(rs(_==null?void 0:_.fee_settings,S.baseAmount))})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:V(S.total)})]}),e.jsx("div",{children:e.jsx(Ue,{user:g,bookingId:O,reservationId:N,clientSecret:z,paymentIntent:oe,navigateRoute:`/user/payment-success/${N}?type=lesson`})}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:te.payment_description}),e.jsxs("div",{className:"space-y-4 text-sm text-gray-500",children:[e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx(Fe,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(Fe,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})]})})]})]})]})})}let Oe=new Ne;new Te;function js({sports:c=[],players:d=[],club:i,userProfile:D}){var ue,te,a,Y,ae,ne,xe,Se,Ce,ke;const[j,k]=s.useState(null),[M,m]=s.useState(null),[u,A]=s.useState(null),[x,g]=s.useState(new Date),[R,F]=s.useState([]),[t,L]=s.useState(null),[G,v]=s.useState(null),[I,n]=s.useState([]),[p,Z]=s.useState(0),[X,J]=s.useState(0),[h,P]=s.useState(!1),[r,Q]=s.useState([]),[C,le]=s.useState([]),[W,re]=s.useState(!1),{state:ce,dispatch:z}=s.useContext(je),[ee,oe]=s.useState({from:null,until:null}),[l,O]=s.useState("main"),{club_membership:$,user_subscription:b,user_permissions:N,courts:B}=ve(),o=s.useMemo(()=>!(b!=null&&b.planId)||!($!=null&&$.length)?null:$.find(w=>w.plan_id===b.planId),[b,$]),T=s.useMemo(()=>{var we;const w=((we=o==null?void 0:o.advance_booking_days)==null?void 0:we.lesson)||10,H=new Date,de=new Date;return de.setDate(H.getDate()+w),de},[o]),{start_time:_,end_time:E}=fe(r),K=async()=>{try{const w=await Oe.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");n(w.groups)}catch(w){console.error(w)}};s.useEffect(()=>{K()},[]),s.useEffect(()=>{},[l]);const he=()=>{g(new Date(x.setMonth(x.getMonth()-1)))},se=()=>{g(new Date(x.setMonth(x.getMonth()+1)))},S=w=>{Q([{from:w.from,until:w.until}])};s.useEffect(()=>{p&&(R!=null&&R.length)?J(p*(R==null?void 0:R.length)):J(p)},[p,R]);const me=async()=>{P(!0);const w={start_time:_,sport_id:j,end_time:E,date:pe(new Date(u),"yyyy-MM-dd")};try{const H=await Oe.callRawAPI("/v3/api/custom/courtmatchup/user/coach/search-time-slots",w,"POST");if(!H.error){if(console.log("response",H),H.list.length===0){U(z,"No coaches found for the selected time slot",4e3,"error");return}le(H.list),re(!0)}}catch(H){console.error("ERROR",H),U(z,H.message,5e3)}finally{P(!1)}};return e.jsx("div",{className:"",children:e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(ss,{sports:c,userPermissions:N,courts:B,filterMode:"lesson",onSelectionChange:({sport:w,type:H,subType:de})=>{k(w),L(H),v(de),A(null),Q([])}}),j&&(!((te=(ue=c==null?void 0:c.find(w=>w.id===j))==null?void 0:ue.sport_types)!=null&&te.length)||t!==null&&(G!==null||!((ne=(ae=(Y=(a=c==null?void 0:c.find(w=>w.id===j))==null?void 0:a.sport_types)==null?void 0:Y.find(w=>w.type===t))==null?void 0:ae.subtype)!=null&&ne.length)))?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[((xe=o==null?void 0:o.advance_booking_days)==null?void 0:xe.lesson)!==void 0&&e.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["You can reserve a lesson up to"," ",(Se=o==null?void 0:o.advance_booking_days)==null?void 0:Se.lesson," ",((Ce=o==null?void 0:o.advance_booking_days)==null?void 0:Ce.lesson)===1?"day":"days"," ","in advance."]}),e.jsx(es,{currentMonth:x,selectedDate:u,onDateSelect:w=>{var H;if(w>T){const de=((H=o==null?void 0:o.advance_booking_days)==null?void 0:H.lesson)||10;U(z,`Your membership plan only allows booking ${de} days in advance`,3e3,"warning");return}A(w)},onPreviousMonth:he,onNextMonth:se,daysOff:i!=null&&i.days_off?JSON.parse(i.days_off):[],allowPastDates:!1,minDate:new Date,maxDate:T,disabledDateMessage:`Your membership plan only allows booking ${((ke=o==null?void 0:o.advance_booking_days)==null?void 0:ke.lesson)||10} days in advance`})]}),u&&e.jsx(Xe,{isLoading:h,selectedDate:u,timeRange:r,onTimeClick:S,onNext:()=>{var w;if(!r.length){U(z,"Please select a time slot",3e3,"error");return}if(u>T){const H=((w=o==null?void 0:o.advance_booking_days)==null?void 0:w.lesson)||10;U(z,`Your membership plan only allows booking ${H} days in advance`,3e3,"warning");return}me()},nextButtonText:"Next: Select coach",startHour:0,endHour:24,clubTimes:i!=null&&i.times?JSON.parse(i.times):[],interval:30,isTimeSlotAvailable:()=>!0})]}):e.jsx("div",{className:"col-span-2 flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5",children:e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Please select a sport"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose a sport, type, and sub-type to view available time slots"})]})})]})})})}),W&&C.length>0&&e.jsx(vs,{coaches:C,onClose:()=>re(!1),selectedDate:u,selectedSport:j,selectedLocation:M,timeRange:ee,sports:c,players:d,groups:I,club:i,isOpen:W,selectedTimes:r,selectedPlayers:R,selectedType:t,selectedSubType:G,userProfile:D})]})})}let Ee=new Te,Ns=new Ne;function yt(){const[c]=Ye(),d=c.get("coach"),[i,D]=s.useState("coach"),[j,k]=s.useState([]),[M,m]=s.useState([]),{dispatch:u}=s.useContext(je),[A,x]=s.useState(!0),[g,R]=s.useState([]),[F,t]=s.useState(null),L=Ae(),G=[{id:"coach",label:"Find by coach"},{id:"time",label:"Find by time"},{id:"custom",label:"Custom request"}],{club:v,sports:I,user_permissions:n}=ve(),p=localStorage.getItem("user"),Z=async()=>{try{const h=await Ee.getOne("user",p,{});t(h.model)}catch(h){console.error(h)}},X=async()=>{const h=await Ee.getList("coach",{join:["user|user_id"],filter:[`courtmatchup_coach.club_id,eq,${parseInt(v==null?void 0:v.id)}`]}),P=await Ee.getList("user",{filter:["role,cs,user",`club_id,eq,${parseInt(v==null?void 0:v.id)}`]});k(h.list),m(P.list)},J=async()=>{try{const h=await Ns.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");R(h.groups)}catch(h){console.error(h)}};return s.useEffect(()=>{(async()=>v!=null&&v.id&&(x(!0),await Z(),await X(),await J(),x(!1)))()},[v==null?void 0:v.id]),$e.useEffect(()=>{u({type:"SETPATH",payload:{path:"lessons"}}),d&&D("coach")},[d]),n&&!n.allowCoach?e.jsx(is,{message:`Your current plan (${n==null?void 0:n.planName}) does not include coach lessons. Please upgrade your plan to access this feature.`}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white px-3 py-3 sm:px-4 sm:py-4",children:[A&&e.jsx(Qe,{}),e.jsx("h1",{className:"mb-4 text-xl font-semibold sm:mb-6 sm:text-2xl",children:"Lessons"}),e.jsxs("div",{className:"flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0",children:[e.jsx("div",{className:"flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:mb-0 sm:text-sm",children:G.map(h=>e.jsx("button",{onClick:()=>D(h.id),className:`whitespace-nowrap px-2 py-1.5 sm:px-3 sm:py-2 ${i===h.id?"bg-white-600 font-medium":"bg-gray-100 text-gray-600"}`,children:h.label},h.id))}),i==="custom"&&e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:()=>L("/user/create-custom-request"),className:"rounded-lg bg-primaryBlue px-3 py-1.5 text-sm text-white transition-colors hover:bg-blue-600 sm:px-4 sm:py-2",children:"Create request"})})]})]}),e.jsxs("div",{className:"mx-auto max-w-7xl",children:[i==="coach"&&e.jsx(hs,{sports:I,coaches:j,players:M,groups:g,club:v,userProfile:F}),i==="time"&&e.jsx(js,{sports:I,coaches:j,players:M,groups:g,club:v,userProfile:F}),i==="custom"&&e.jsx(ys,{sports:I,coaches:j,players:M,groups:g,club:v,userProfile:F})]})]})}export{yt as default};
