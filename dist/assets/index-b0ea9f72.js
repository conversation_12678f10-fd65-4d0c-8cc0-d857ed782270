import{j as l}from"./@nivo/heatmap-ba1ecfff.js";import{f as L,r as g}from"./vendor-851db8c1.js";import{u as S,aa as M,an as R,a9 as a,af as w,P as C,M as P,G as H,e as O,b as k,d as G,A as z,R as q,ao as K}from"./index-13fd629e.js";import{h as E}from"./moment-a9aaa855.js";import{R as $,T as D,P as A,F as Y,C as I,G as J,L as Q}from"./ReservationStatus-5ced670f.js";function W({reservation:s,clubSports:t,players:d,onCancelClick:c}){var f,N;const h=L(),{club:i,courts:p}=S(),u=M(s==null?void 0:s.reservation_updated_at),y=R(s,t);return console.log("canCancel",y),console.log("reservation",s),console.log("club courts",p),console.log("club",i),l.jsx("div",{children:l.jsxs("div",{className:"space-y-6",children:[l.jsx("div",{className:" px-5 pt-5",children:l.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[l.jsx("div",{children:l.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),l.jsx("div",{className:"flex items-center gap-2",children:l.jsx("div",{children:(s==null?void 0:s.booking_status)===a.PENDING&&l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx($,{}),l.jsx(D,{timeLeft:u})]})||(s==null?void 0:s.booking_status)===a.SUCCESS&&l.jsx(A,{})||(s==null?void 0:s.booking_status)===a.FAIL&&l.jsx(Y,{})||(s==null?void 0:s.booking_status)===a.CANCELLED&&l.jsx(I,{})})})]})}),l.jsxs("div",{className:"divide-y px-5 py-1",children:[l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(f=t==null?void 0:t.find(m=>m.id===(s==null?void 0:s.sport_id)))==null?void 0:f.name," ",(s==null?void 0:s.type)&&`• ${s==null?void 0:s.type}`," ",(s==null?void 0:s.sub_type)&&`• ${s==null?void 0:s.sub_type}`]}),i.allow_user_court_selection==1&&l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"COURT"})}),(N=p==null?void 0:p.find(m=>m.id===(s==null?void 0:s.court_id)))==null?void 0:N.name," "]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),l.jsx("p",{className:"mt-1 font-medium",children:E(s==null?void 0:s.booking_date).format("MMM D, YYYY")})]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),l.jsxs("p",{className:"mt-1 font-medium",children:[w(s==null?void 0:s.start_time)," -"," ",w(s==null?void 0:s.end_time)]})]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),l.jsx("div",{className:"flex flex-col gap-2",children:d.map(m=>l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("img",{src:m.photo||"/default-avatar.png",alt:m.first_name,className:"h-8 w-8 rounded-full"}),l.jsx("p",{className:"font-medium capitalize",children:m.first_name||m.last_name?`${m.first_name} ${m.last_name}`:"Player"})]},m.user_id))})]})]}),l.jsx("div",{className:"px-5 py-3",children:l.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[l.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),l.jsxs("div",{className:"mt-2",children:[l.jsxs("div",{className:"mb-2 flex justify-between",children:[l.jsx("p",{className:"text-sm ",children:"Club fee"}),l.jsx("p",{className:"text-sm ",children:C(s==null?void 0:s.club_fee)})]}),l.jsxs("div",{className:"flex justify-between",children:[l.jsx("p",{className:"text-sm ",children:"Service fee"}),l.jsx("p",{className:"text-sm ",children:C(s==null?void 0:s.service_fee)})]}),l.jsx("div",{className:"my-2 border-t border-gray-300"}),l.jsxs("div",{className:"flex justify-between font-medium",children:[l.jsx("p",{children:"Total"}),l.jsx("p",{children:C((s==null?void 0:s.club_fee)+(s==null?void 0:s.service_fee))})]})]}),(s==null?void 0:s.booking_status)==a.PENDING&&l.jsx("div",{className:"mt-3 flex items-center justify-between",children:l.jsx("button",{onClick:()=>{h(`/user/reservation-payment/${s==null?void 0:s.reservation_id}?type=${s==null?void 0:s.booking_type}`)},disabled:u==="0min",className:`w-full rounded-lg border border-gray-300 px-2 py-2 text-sm text-white ${u==="0min"?"cursor-not-allowed bg-gray-400":"bg-primaryBlue"}`,children:u==="0min"?"Time Expired":"Pay now"})}),(s==null?void 0:s.booking_status)==a.SUCCESS&&l.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:l.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),l.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),l.jsx("button",{onClick:()=>{h(`/user/payment-receipt/${s==null?void 0:s.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),y&&(s==null?void 0:s.booking_status)===a.SUCCESS&&(s==null?void 0:s.booking_status)!==a.CANCELLED&&l.jsx("div",{className:"mt-3",children:l.jsx("button",{onClick:c,className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),l.jsx("div",{className:"mt-3",children:(()=>{const m=t==null?void 0:t.find(j=>j.id===(s==null?void 0:s.sport_id)),e=(m==null?void 0:m.cancel_hours_before)||24;return(m==null?void 0:m.allow_cancel_reservation)===1?l.jsxs("p",{className:"text-xs text-gray-500",children:[l.jsx("strong",{children:"Cancellation Policy:"})," You can cancel this reservation up to ",e," ",e===1?"hour":"hours"," before the reservation starts.",!y&&l.jsxs("span",{className:"text-red-600",children:[" ","The cancellation window has passed for this reservation."]})]}):l.jsxs("p",{className:"text-xs text-gray-500",children:[l.jsx("strong",{children:"Cancellation Policy:"})," This reservation cannot be cancelled."]})})()})]})})]})})}function X({reservation:s,club:t,clubSports:d,players:c,coach:h,onCancelClick:i}){var N,m;const p=L(),{courts:u}=S(),y=M(s==null?void 0:s.reservation_updated_at),f=R(s,d);return l.jsx("div",{children:l.jsxs("div",{className:"space-y-6",children:[l.jsx("div",{className:" px-5 pt-5",children:l.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[l.jsx("div",{children:l.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("div",{children:(s==null?void 0:s.booking_status)===a.PENDING&&l.jsx($,{})||(s==null?void 0:s.booking_status)===a.SUCCESS&&l.jsx(A,{})||(s==null?void 0:s.booking_status)===a.FAIL&&l.jsx(Y,{})||(s==null?void 0:s.booking_status)===a.CANCELLED&&l.jsx(I,{})}),l.jsx("div",{children:l.jsx(D,{timeLeft:y})})]})]})}),l.jsxs("div",{className:"divide-y px-5 py-1",children:[l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(N=d==null?void 0:d.find(e=>e.id===(s==null?void 0:s.sport_id)))==null?void 0:N.name," ",(s==null?void 0:s.type)&&`• ${s==null?void 0:s.type}`," ",(s==null?void 0:s.sub_type)&&`• ${s==null?void 0:s.sub_type}`]}),t.allow_user_court_selection==1&&l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"COURT"})}),(m=u==null?void 0:u.find(e=>e.id===(s==null?void 0:s.court_id)))==null?void 0:m.name," "]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),l.jsx("p",{className:"mt-1 font-medium",children:E(s==null?void 0:s.booking_date).format("MMM D, YYYY")})]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),l.jsxs("p",{className:"mt-1 font-medium",children:[w(s==null?void 0:s.start_time)," -"," ",w(s==null?void 0:s.end_time)]})]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"COACH"})}),l.jsx("div",{className:"flex flex-col gap-2",children:h&&l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("img",{src:h.photo||"/default-avatar.png",alt:h.first_name,className:"h-8 w-8 rounded-full"}),l.jsx("p",{className:"font-medium capitalize",children:h.first_name||h.last_name?`${h.first_name} ${h.last_name}`:"Coach"})]},h.user_id)})]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),l.jsx("div",{className:"flex flex-col gap-2",children:c.map(e=>l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("img",{src:e.photo||"/default-avatar.png",alt:e.first_name,className:"h-8 w-8 rounded-full"}),l.jsx("p",{className:"font-medium capitalize",children:e.first_name||e.last_name?`${e.first_name} ${e.last_name}`:"Player"})]},e.user_id))})]})]}),l.jsx("div",{className:"px-5 py-3",children:l.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[l.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),l.jsxs("div",{className:"mt-2",children:[l.jsxs("div",{className:"mb-2 flex justify-between",children:[l.jsx("p",{className:"text-sm ",children:"Club fee"}),l.jsx("p",{className:"text-sm ",children:C(t==null?void 0:t.club_fee)})]}),l.jsxs("div",{className:"flex justify-between",children:[l.jsx("p",{className:"text-sm ",children:"Service fee"}),l.jsx("p",{className:"text-sm ",children:"$12.50"})]}),l.jsx("div",{className:"my-2 border-t border-gray-300"}),l.jsxs("div",{className:"flex justify-between font-medium",children:[l.jsx("p",{children:"Total"}),l.jsx("p",{children:C((s==null?void 0:s.club_fee)+12.5)})]})]}),(s==null?void 0:s.booking_status)===a.SUCCESS&&l.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:l.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),l.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),l.jsx("button",{onClick:()=>{p(`/user/payment-receipt/${s==null?void 0:s.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),f&&(s==null?void 0:s.booking_status)===a.SUCCESS&&(s==null?void 0:s.booking_status)!==a.CANCELLED&&l.jsx("div",{className:"mt-3",children:l.jsx("button",{onClick:i,className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),l.jsx("div",{className:"mt-3",children:(()=>{const e=d==null?void 0:d.find(_=>_.id===(s==null?void 0:s.sport_id)),x=(e==null?void 0:e.cancel_hours_before)||24;return(e==null?void 0:e.allow_cancel_reservation)===1?l.jsxs("p",{className:"text-xs text-gray-500",children:[l.jsx("strong",{children:"Cancellation Policy:"})," You can cancel this lesson reservation up to ",x," ",x===1?"hour":"hours"," before the lesson starts.",!f&&l.jsxs("span",{className:"text-red-600",children:[" ","The cancellation window has passed for this reservation."]})]}):l.jsxs("p",{className:"text-xs text-gray-500",children:[l.jsx("strong",{children:"Cancellation Policy:"})," This reservation cannot be cancelled."]})})()})]})})]})})}const o=new P,Z=(s,t)=>{if(!s||!t)return!1;const d=new Date,h=(new Date(`${s.booking_date} ${s.start_time}`).getTime()-d.getTime())/(1e3*60*60),i=(t.cancellation_policy_days||1)*24;return h>=i};function r({reservation:s,clubSports:t,players:d,onCancelClick:c}){var j,_;const h=L(),[i,p]=g.useState(null),[u,y]=g.useState(!1),{courts:f,club:N}=S(),m=M(s==null?void 0:s.reservation_updated_at),e=i?Z(s,i):R(s,t),x=async()=>{if(s!=null&&s.clinic_id){y(!0);try{o.setTable("clinics");const n=await o.callRestAPI({id:s==null?void 0:s.clinic_id},"GET");p(n.model)}catch(n){console.error("Error fetching clinic:",n)}finally{y(!1)}}};return g.useEffect(()=>{x()},[s==null?void 0:s.clinic_id]),l.jsxs("div",{children:[u&&l.jsx("div",{className:"flex items-center justify-center p-4",children:l.jsx("div",{className:"text-sm text-gray-500",children:"Loading clinic details..."})}),l.jsxs("div",{className:"space-y-6",children:[l.jsx("div",{className:" px-5 pt-5",children:l.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[l.jsx("div",{children:l.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),l.jsx("div",{className:"flex items-center gap-2",children:l.jsx("div",{children:(s==null?void 0:s.booking_status)===a.PENDING&&l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx($,{}),l.jsx(D,{timeLeft:m})]})||(s==null?void 0:s.booking_status)===a.SUCCESS&&l.jsx(A,{})||(s==null?void 0:s.booking_status)===a.FAIL&&l.jsx(Y,{})||(s==null?void 0:s.booking_status)===a.CANCELLED&&l.jsx(I,{})})})]})}),l.jsxs("div",{className:"divide-y px-5 py-1",children:[i&&l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"CLINIC"})}),l.jsx("p",{className:"mt-1 font-medium",children:i.name}),i.description&&l.jsx("p",{className:"mt-1 text-sm text-gray-600",children:i.description})]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(j=t==null?void 0:t.find(n=>n.id===(s==null?void 0:s.sport_id)))==null?void 0:j.name," ",(s==null?void 0:s.type)&&`• ${s==null?void 0:s.type}`," ",(s==null?void 0:s.sub_type)&&`• ${s==null?void 0:s.sub_type}`]}),N.allow_user_court_selection==1&&l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"COURT"})}),(_=f==null?void 0:f.find(n=>n.id===(s==null?void 0:s.court_id)))==null?void 0:_.name," "]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),l.jsx("p",{className:"mt-1 font-medium",children:E(s==null?void 0:s.booking_date).format("MMM D, YYYY")})]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),l.jsxs("p",{className:"mt-1 font-medium",children:[w(s==null?void 0:s.start_time)," -"," ",w(s==null?void 0:s.end_time)]})]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),l.jsx("div",{className:"flex flex-col gap-2",children:d==null?void 0:d.map(n=>l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("img",{src:n.photo||"/default-avatar.png",alt:n.first_name,className:"h-8 w-8 rounded-full"}),l.jsx("p",{className:"font-medium capitalize",children:n.first_name||n.last_name?`${n.first_name} ${n.last_name}`:"Player"})]},n.user_id))})]})]}),l.jsx("div",{className:"px-5 py-3",children:l.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[l.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),l.jsxs("div",{className:"mt-2",children:[l.jsxs("div",{className:"mb-2 flex justify-between",children:[l.jsx("p",{className:"text-sm ",children:"Club fee"}),l.jsx("p",{className:"text-sm ",children:C(s==null?void 0:s.club_fee)})]}),l.jsxs("div",{className:"flex justify-between",children:[l.jsx("p",{className:"text-sm ",children:"Service fee"}),l.jsx("p",{className:"text-sm ",children:C(s==null?void 0:s.service_fee)})]}),l.jsx("div",{className:"my-2 border-t border-gray-300"}),l.jsxs("div",{className:"flex justify-between font-medium",children:[l.jsx("p",{children:"Total"}),l.jsx("p",{children:C((s==null?void 0:s.club_fee)+(s==null?void 0:s.service_fee))})]})]}),l.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:l.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),l.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),l.jsx("button",{onClick:()=>{h(`/user/payment-receipt/${s==null?void 0:s.reservation_id}?type=clinic`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),e&&(s==null?void 0:s.booking_status)==a.SUCCESS&&(s==null?void 0:s.booking_status)!=a.CANCELLED&&l.jsx("div",{className:"mt-3",children:l.jsx("button",{onClick:c,className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),l.jsxs("div",{className:"mt-3",children:[i&&l.jsxs("p",{className:"text-xs text-gray-500",children:[l.jsx("strong",{children:"Cancellation Policy:"})," You can cancel this clinic reservation up to"," ",i.cancellation_policy_days||1," ",(i.cancellation_policy_days||1)===1?"day":"days"," ","before the clinic starts.",!e&&l.jsxs("span",{className:"text-red-600",children:[" ","The cancellation window has passed for this reservation."]})]}),!i&&l.jsx("p",{className:"text-xs text-gray-500",children:"Loading cancellation policy information..."})]})]})})]})]})}const v=new P;function ss({reservation:s,club:t,clubSports:d,players:c,onCancelClick:h}){var e;const i=L(),p=M(s==null?void 0:s.reservation_updated_at),{dispatch:u}=g.useContext(H),[y,f]=g.useState(!1),N=R(s,d),m=async x=>{if(!x){k(u,"Email is required",5e3,"error");return}try{f(!0);const j=await v.callRawAPI(`/v3/api/custom/courtmatchup/user/buddy/${s==null?void 0:s.buddy_id}/send-mail?email=${x}`,{},"GET");console.log(j),k(u,"Email reminder sent",5e3,"success")}catch(j){console.log(j),k(u,j==null?void 0:j.message,5e3,"error")}finally{f(!1)}};return l.jsxs("div",{children:[y&&l.jsx(O,{}),l.jsxs("div",{className:"space-y-6",children:[l.jsx("div",{className:" px-5 pt-5",children:l.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[l.jsx("div",{children:l.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),l.jsx("div",{className:"flex items-center gap-2",children:l.jsxs("div",{children:[(s==null?void 0:s.num_needed)==(s==null?void 0:s.num_players)&&l.jsx(J,{title:"Group full"}),(s==null?void 0:s.num_needed)!=(s==null?void 0:s.num_players)&&l.jsx(Q,{numberOfBuddies:s==null?void 0:s.num_needed})]})})]})}),l.jsxs("div",{className:"divide-y px-5 py-1",children:[l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"REQUEST MADE"})}),E(s==null?void 0:s.reservation_created_at).format("MMM D, YYYY")]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"DTE & TIME"})}),E(s==null?void 0:s.booking_date).format("MMM D, YYYY")," •"," ",w(s==null?void 0:s.start_time)," -"," ",w(s==null?void 0:s.end_time)]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(e=d==null?void 0:d.find(x=>x.id===(s==null?void 0:s.sport_id)))==null?void 0:e.name," ",(s==null?void 0:s.type)&&`• ${s==null?void 0:s.type}`," ",(s==null?void 0:s.sub_type)&&`• ${s==null?void 0:s.sub_type}`]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"NOTES"})}),l.jsxs("p",{className:"mt-1 font-medium",children:[s==null?void 0:s.ntrp," ",(s==null?void 0:s.max_ntrp)&&`- ${s==null?void 0:s.max_ntrp}`]})]}),l.jsxs("div",{className:"py-3",children:[l.jsx("div",{className:" py-1 ",children:l.jsx("p",{className:"text-sm text-gray-500",children:"LOOKING FOR PLAYERS"})}),l.jsx("p",{className:"mt-1 font-medium",children:`${s==null?void 0:s.num_needed}/${s==null?void 0:s.num_players}`})]}),l.jsxs("div",{className:"py-3",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"}),l.jsx("button",{className:"rounded-lg border border-gray-300 bg-white px-2 py-1 text-sm text-gray-700",children:"Email all"})]}),l.jsx("div",{className:"mt-4 flex flex-col gap-4",children:c.map(x=>l.jsxs("div",{className:"flex items-center justify-between rounded-lg bg-gray-100 p-2",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx("img",{src:x.photo||"/default-avatar.png",alt:x.first_name,className:"h-10 w-10 rounded-full object-cover"}),l.jsxs("div",{children:[l.jsx("p",{className:"font-medium capitalize",children:x.first_name||x.last_name?`${x.first_name} ${x.last_name}`:"Player"}),l.jsx("p",{className:"text-sm text-gray-500",children:x.email})]})]}),l.jsxs("div",{className:"flex flex-col items-end gap-3",children:[l.jsx("button",{onClick:()=>m(x.email),className:"rounded-lg bg-white px-2 py-1 text-sm text-gray-700",children:"Send email"}),l.jsxs("p",{className:"text-sm text-gray-700",children:["NTRP: ",x.ntrp]})]})]},x.user_id))})]})]}),l.jsx("div",{className:"px-5 py-3",children:l.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[l.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),l.jsxs("div",{className:"mt-2",children:[l.jsxs("div",{className:"mb-2 flex justify-between",children:[l.jsx("p",{className:"text-sm ",children:"Club fee"}),l.jsx("p",{className:"text-sm ",children:C(s==null?void 0:s.club_fee)})]}),l.jsxs("div",{className:"flex justify-between",children:[l.jsx("p",{className:"text-sm ",children:"Service fee"}),l.jsx("p",{className:"text-sm ",children:C(s==null?void 0:s.service_fee)})]}),l.jsx("div",{className:"my-2 border-t border-gray-300"}),l.jsxs("div",{className:"flex justify-between font-medium",children:[l.jsx("p",{children:"Total"}),l.jsx("p",{children:C((s==null?void 0:s.club_fee)+(s==null?void 0:s.service_fee))})]})]}),(s==null?void 0:s.booking_status)==a.PENDING&&l.jsx("div",{className:"mt-3 flex items-center justify-between",children:l.jsx("button",{onClick:()=>{i(`/user/reservation-payment/${s==null?void 0:s.reservation_id}?type=${s==null?void 0:s.booking_type}`)},disabled:p==="0min",className:`w-full rounded-lg border border-gray-300 px-2 py-2 text-sm text-white ${p==="0min"?"cursor-not-allowed bg-gray-400":"bg-primaryBlue"}`,children:p==="0min"?"Time Expired":"Pay now"})}),(s==null?void 0:s.booking_status)==a.SUCCESS&&l.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:l.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),l.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),l.jsx("button",{onClick:()=>{i(`/user/payment-receipt/${s==null?void 0:s.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),N&&(s==null?void 0:s.booking_status)===a.SUCCESS&&(s==null?void 0:s.booking_status)!==a.CANCELLED&&l.jsx("div",{className:"mt-3",children:l.jsx("button",{onClick:h,className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),l.jsx("div",{className:"mt-3",children:(()=>{const x=d==null?void 0:d.find(n=>n.id===(s==null?void 0:s.sport_id)),j=(x==null?void 0:x.cancel_hours_before)||24;return(x==null?void 0:x.allow_cancel_reservation)===1?l.jsxs("p",{className:"text-xs text-gray-500",children:[l.jsx("strong",{children:"Cancellation Policy:"})," You can cancel this buddy request up to ",j," ",j===1?"hour":"hours"," before the session starts.",!N&&l.jsxs("span",{className:"text-red-600",children:[" ","The cancellation window has passed for this buddy request."]})]}):l.jsxs("p",{className:"text-xs text-gray-500",children:[l.jsx("strong",{children:"Cancellation Policy:"})," This buddy request cannot be cancelled."]})})()})]})})]})]})}const ls=({isOpen:s,onClose:t,onCancel:d,loading:c=!1})=>l.jsxs("div",{className:`fixed inset-0 z-[999999] flex items-center justify-center ${s?"":"hidden"}`,children:[l.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),l.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white",children:[l.jsxs("div",{className:"p-6",children:[l.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Cancel reservation"}),l.jsx("div",{className:"flex items-start justify-center rounded-xl bg-[#F17B2C] p-3",children:l.jsxs("div",{children:[l.jsx("div",{className:"text-sm font-medium text-white",children:"IMPORTANT"}),l.jsx("p",{className:"text-sm text-white",children:"Are you sure you want to cancel this reservation?"})]})})]}),l.jsxs("div",{className:"flex justify-end gap-3 border-t px-6 py-4",children:[l.jsx("button",{onClick:t,className:"rounded-xl border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),l.jsx(G,{onClick:d,className:"rounded-xl bg-green-800 px-4 py-2 text-white hover:bg-green-700",loading:c,children:"Cancel reservation"})]})]})]});let T=new P;function as({isOpen:s,onClose:t,clubSports:d,reservation:c,club:h,onReservationCanceled:i}){const{dispatch:p}=g.useContext(H),{dispatch:u}=g.useContext(z),[y,f]=g.useState(!1),[N,m]=g.useState([]),[e,x]=g.useState(null),[j,_]=g.useState(!1),[n,U]=g.useState(!1);async function F(){try{const b=await K(p,u,"user",JSON.parse(c==null?void 0:c.player_ids),"user|user_id");m(b.list)}catch(b){console.error(b)}}async function V(){try{T.setTable("user");const b=await T.callRestAPI({id:c==null?void 0:c.coach_id},"GET");x(b.model)}catch(b){console.error(b)}}const B=async()=>{U(!0);try{T.setTable("booking"),await T.callRestAPI({id:c==null?void 0:c.booking_id,status:a.CANCELLED},"PUT"),k(p,"Reservation cancelled successfully",3e3,"success"),_(!1),c&&(c.booking_status=a.CANCELLED),i&&i()}catch(b){console.error("Error cancelling reservation:",b),k(p,b.message||"Error cancelling reservation",3e3,"error")}finally{U(!1)}};return g.useEffect(()=>{(async()=>(f(!0),await F(),await V(),f(!1)))()},[c]),c?y?l.jsx(O,{}):(console.log({reservation:c,sports:d,players:N,coach:e}),l.jsxs(l.Fragment,{children:[l.jsxs(q,{isOpen:s,onClose:t,title:c.booking_type==="Court"?"Court details":c.booking_type==="Coach"?"Coach details":c.booking_type==="Clinic"?"Clinic details":"Details",showFooter:!1,className:"!p-0",children:[c.booking_type==="Court"&&l.jsx(W,{reservation:c,club:h,clubSports:d,players:N,onCancelClick:()=>_(!0)}),c.booking_type==="Find Buddy"&&l.jsx(ss,{reservation:c,club:h,clubSports:d,players:N,onCancelClick:()=>_(!0)}),c.booking_type==="Coach"&&l.jsx(X,{reservation:c,club:h,clubSports:d,players:N,coach:e,onCancelClick:()=>_(!0)}),c.booking_type==="Clinic"&&l.jsx(r,{reservation:c,club:h,clubSports:d,players:N,onCancelClick:()=>_(!0)})]}),l.jsx(ls,{isOpen:j,onClose:()=>_(!1),onCancel:B,loading:n})]})):null}export{as as R};
