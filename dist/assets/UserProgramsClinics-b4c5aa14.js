import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as me,r}from"./vendor-851db8c1.js";import{G as ce,R as te,af as ee,M as de,b as K,aS as Y,A as ue,u as xe,ao as he,e as pe,T as ge,t as fe}from"./index-13fd629e.js";import{q as ye}from"./index.esm-09a3a6b8.js";import{f as se,s as be,m as je}from"./date-fns-cca0f4f7.js";import{C as we}from"./Calendar-35bce269.js";import{h as Ne}from"./moment-a9aaa855.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";const V=new de;function re({isOpen:i,onClose:N,clinic:t,fetchCoachesForClinic:l}){const j=me(),{dispatch:v}=r.useContext(ce),[_,f]=r.useState(!1),[k,M]=r.useState(!1),[F,A]=r.useState([]),[C,w]=r.useState(!1),[s,u]=r.useState(null),[x,b]=r.useState(!1),$=r.useRef(null),W=r.useRef(null),B=async()=>{if(t!=null&&t.id)try{const p=await V.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/clinic-subscription",{},"GET");p&&!p.error&&f(p.subscribed||!1)}catch(p){console.error("Error checking subscription status:",p)}},R=async()=>{if(!(t!=null&&t.id)){K(v,"Clinic information not available",3e3,"error");return}M(!0);try{const p={clinic_id:t.id,sport_id:t.sport_id,type:t.type,subtype:t.subtype};let S,O;_?(O="/v3/api/custom/courtmatchup/user/reservations/unsubscribe",S=await V.callRawAPI(O,p,"POST")):(O="/v3/api/custom/courtmatchup/user/reservations/subscribe",S=await V.callRawAPI(O,p,"POST")),S&&!S.error?(f(!_),K(v,`Notifications ${_?"disabled":"enabled"} successfully`,3e3,"success")):K(v,(S==null?void 0:S.message)||"Failed to update notification settings",3e3,"error")}catch(p){console.error("Error toggling notifications:",p),K(v,p.message||"Error updating notification settings",3e3,"error")}finally{M(!1)}},G=async()=>{if(!(!t||!t.sport_id||!t.type||!t.subtype)){b(!0);try{const p=await V.callRawAPI("/v3/api/custom/courtmatchup/club/courts/affected-reservations",{sport:t.sport_id,type:t.type,subtype:t.subtype},"POST");p&&!p.error&&u({totalScheduled:p.total_affected||0,completedEvents:p.completed_events||0,upcomingEvents:p.upcoming_events||0,lastEventDate:p.last_event_date})}catch(p){console.error("Error fetching affected reservations stats:",p),u(null)}finally{b(!1)}}};return r.useEffect(()=>{(async()=>{if(i&&t&&t.coach_ids&&Array.isArray(t.coach_ids)&&t.coach_ids.length>0&&l){const S=JSON.stringify([...t.coach_ids].sort());if($.current===t.id&&W.current===S)return;w(!0);try{const O=await l(t.coach_ids);A(O),$.current=t.id,W.current=S}catch(O){console.error("Error fetching coaches:",O),A([])}finally{w(!1)}}else i||(A([]),w(!1),$.current=null,W.current=null)})()},[i,t==null?void 0:t.id,JSON.stringify(t==null?void 0:t.coach_ids),l]),r.useEffect(()=>{i&&t?(G(),B()):i||(u(null),b(!1),f(!1))},[i,t==null?void 0:t.id,t==null?void 0:t.sport_id,t==null?void 0:t.type,t==null?void 0:t.subtype]),t?e.jsx(te,{isOpen:i,onClose:N,title:t.name,showFooter:!1,primaryButtonText:"Join",showOnlyPrimary:!0,className:"!p-0",children:e.jsxs("div",{className:"flex h-full flex-col pb-4",children:[e.jsxs("div",{className:"flex flex-1 flex-col gap-4 p-5 pb-6",children:[e.jsx("div",{className:"inline-flex items-center gap-2",children:t.slots_remaining>0?e.jsxs("span",{className:"rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-sm text-[#176448]",children:["Slots available: ",t.slots_remaining," (out of"," ",t.max_participants,")"]}):e.jsx("span",{className:"rounded-full border border-red-800 bg-red-50 px-3 py-1 text-sm text-red-800",children:"No slots available"})}),e.jsxs("div",{className:"border-1 space-y-1 rounded-xl border border-gray-200 bg-gray-100 p-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"DATE & TIME"}),e.jsxs("p",{className:"text-base",children:[new Date(t.clinic_date).toLocaleDateString("en-US",{month:"long",day:"numeric"})," ","• ",ee(t.clinic_start_time)," -"," ",ee(t.clinic_end_time)]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"DETAILS"}),e.jsx("p",{className:"text-base",children:t.details})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"COACHES"}),e.jsx("div",{className:"space-y-3",children:C?e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-10 w-10 animate-pulse rounded-full bg-gray-200"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"h-4 w-24 animate-pulse rounded bg-gray-200"}),e.jsx("div",{className:"h-3 w-32 animate-pulse rounded bg-gray-200"})]})]}):F&&F.length>0?F.map(p=>{var S,O,q,P,g,D;return e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:((S=p.user)==null?void 0:S.photo)||"/default-avatar.png",alt:`${(O=p.user)==null?void 0:O.first_name} ${(q=p.user)==null?void 0:q.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-base font-medium",children:[(P=p.user)==null?void 0:P.first_name," ",(g=p.user)==null?void 0:g.last_name]}),((D=p.user)==null?void 0:D.email)&&e.jsx("p",{className:"text-sm text-gray-500",children:p.user.email}),p.bio&&e.jsx("p",{className:"mt-1 line-clamp-2 text-xs text-gray-400",children:p.bio})]})]},p.id)}):e.jsx("p",{className:"text-base text-gray-500",children:"No coaches assigned"})})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"SPORT & TYPE"}),e.jsxs("p",{className:"text-base",children:["Tennis • Indoors •"," ",t.surface_id===1?"Hard Court":"Clay Court"]})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"NTRP"}),e.jsx("p",{className:"text-base",children:"4.0-5.0"})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"CLINIC STATISTICS"}),x?e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"h-4 w-32 animate-pulse rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-8 animate-pulse rounded bg-gray-200"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"h-4 w-28 animate-pulse rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-8 animate-pulse rounded bg-gray-200"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"h-4 w-36 animate-pulse rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-20 animate-pulse rounded bg-gray-200"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"h-4 w-32 animate-pulse rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-20 animate-pulse rounded bg-gray-200"})]})]}):s?e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Total Scheduled Events:"}),e.jsx("span",{className:"text-sm font-medium",children:s.totalScheduled})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Completed Events:"}),e.jsx("span",{className:"text-sm font-medium",children:s.completedEvents})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Upcoming Events:"}),e.jsx("span",{className:"text-sm font-medium",children:s.upcomingEvents>0?`${s.upcomingEvents} upcoming events`:"None"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Last Event Date:"}),e.jsx("span",{className:"text-sm font-medium",children:s.lastEventDate?new Date(s.lastEventDate).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"}):"None"})]})]}):e.jsx("p",{className:"text-sm text-gray-500",children:"No statistics available"})]})]}),e.jsx("div",{className:"sticky bottom-0 border-t border-gray-200 bg-gray-100 p-4",children:t.slots_remaining>0?e.jsxs("button",{onClick:()=>{j(`/user/clinic-booking/${t.id}`,{state:{clinic:t}})},className:"flex w-full items-center justify-center gap-2 rounded-xl bg-primaryBlue py-3 text-center text-white hover:bg-blue-900",children:[e.jsx("span",{children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10.75 11.5H9.25C8.0197 11.4995 6.81267 11.8354 5.75941 12.4712C4.70614 13.107 3.8467 14.0186 3.274 15.1075C3.2579 14.9054 3.2499 14.7027 3.25 14.5C3.25 10.3578 6.60775 7 10.75 7V2.875L18.625 9.25L10.75 15.625V11.5ZM9.25 10H12.25V12.481L16.2408 9.25L12.25 6.019V8.5H10.75C9.88769 8.49903 9.03535 8.68436 8.25129 9.04332C7.46724 9.40227 6.76999 9.92637 6.20725 10.5797C7.17574 10.1959 8.20822 9.99919 9.25 10Z",fill:"white"})})}),e.jsx("span",{children:" Join"})]}):e.jsx("div",{className:"rounded-2xl border border-gray-200 bg-white p-4 shadow-sm",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Get notified"}),e.jsx("div",{className:"flex items-center",children:e.jsx("button",{type:"button",disabled:k,className:`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${_?"bg-blue-600":"bg-gray-200"} ${k?"cursor-not-allowed opacity-50":""}`,role:"switch","aria-checked":_,onClick:R,children:k?e.jsx("span",{"aria-hidden":"true",className:"pointer-events-none inline-block h-5 w-5 translate-x-2.5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out",children:e.jsxs("svg",{className:"ml-1 mt-1 h-3 w-3 animate-spin text-gray-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}):e.jsx("span",{"aria-hidden":"true",className:`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${_?"translate-x-5":"translate-x-0"}`})})})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"We will email you when slots for this clinic become available again, e.g. if someone opts-put."})]})})})})]})}):null}function ne({getActiveFiltersCount:i,clearFilters:N,setIsFilterModalOpen:t,availableOnly:l,toggleAvailableSlots:j,sortOrder:v,showSortOptions:_,setShowSortOptions:f,sortClinics:k}){return e.jsxs("div",{className:"mb-4 flex flex-col justify-between gap-3 sm:mb-6 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{className:"flex items-center gap-3 sm:gap-4",children:[e.jsxs("button",{onClick:()=>t(!0),className:"flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-3 py-1.5 sm:px-4 sm:py-2",children:[e.jsx(ye,{className:"text-blue-600"}),e.jsx("span",{className:"text-sm text-gray-700 sm:text-base",children:"Filter"}),i()>0&&e.jsx("span",{className:"flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-xs text-white",children:i()})]}),i()>0&&e.jsx("button",{onClick:N,className:"text-sm text-gray-500 hover:underline sm:text-base",children:"Clear all"})]}),e.jsxs("div",{className:"mt-3 flex flex-wrap items-center gap-3 sm:mt-0 sm:gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"whitespace-nowrap text-xs text-gray-700 sm:text-sm",children:"Available slot only"}),e.jsx("button",{onClick:j,className:`relative h-5 w-10 rounded-full transition-colors duration-200 ease-in-out sm:h-6 sm:w-12 ${l?"bg-blue-600":"bg-gray-200"}`,children:e.jsx("div",{className:`absolute top-0.5 h-4 w-4 transform rounded-full bg-white shadow-md transition-transform duration-200 ease-in-out sm:h-5 sm:w-5 ${l?"translate-x-5 sm:translate-x-6":"translate-x-0.5 sm:translate-x-1"}`})})]}),e.jsxs("div",{className:"relative border-gray-200 sm:border-l sm:pl-4",children:[e.jsxs("button",{onClick:()=>f(!_),className:"flex items-center gap-1 rounded-xl border border-gray-200 bg-white px-2 py-1.5 text-xs sm:gap-2 sm:px-4 sm:py-2 sm:text-sm",children:[e.jsxs("span",{className:"whitespace-nowrap text-gray-700",children:["By date (",v==="desc"?"Latest":"Earliest",")"]}),e.jsx(Y,{size:16,className:`text-gray-400 transition-transform duration-200 ${_?"rotate-180":""}`})]}),_&&e.jsxs("div",{className:"absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white py-1 shadow-lg",children:[e.jsxs("button",{onClick:()=>k("desc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${v==="desc"?"text-blue-600":"text-gray-700"}`,children:["Latest first",v==="desc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),e.jsxs("button",{onClick:()=>k("asc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${v==="asc"?"text-blue-600":"text-gray-700"}`,children:["Earliest first",v==="asc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]})]})]})]})]})}function ie({clinic:i}){return e.jsx("div",{className:"cursor-pointer rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100 sm:p-4",children:e.jsxs("div",{className:"flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsx("h3",{className:"text-base font-medium sm:text-lg",children:i.name}),i.type==1&&e.jsx("span",{className:"rounded bg-blue-600 px-2 py-1 text-xs text-white",children:"REGISTERED"})]}),e.jsxs("p",{className:"mt-1 text-xs text-gray-600 sm:text-sm",children:[se(new Date(i.clinic_date+"T00:00:00"),"MMMM d, yyyy"),i.clinic_end_date&&e.jsxs(e.Fragment,{children:[" - "," ",se(new Date(i.clinic_end_date+"T00:00:00"),"MMMM d, yyyy")]})," • ",ee(i.clinic_start_time)," -"," ",ee(i.clinic_end_time)]})]}),e.jsx("div",{className:"flex flex-col gap-2 sm:items-end sm:gap-4",children:i.slots_remaining>0?e.jsxs("span",{className:"w-fit rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-xs text-[#176448]",children:["Slots available: ",i.slots_remaining," (out of"," ",i.max_participants,")"]}):e.jsx("span",{className:"w-fit rounded-full border border-red-800 bg-red-50 px-3 py-1 text-xs text-red-800",children:"No slots available"})})]})})}function ve({programs:i,fetchClinics:N,FiltersContent:t,filters:l,setFilters:j,clearFilters:v,fetchCoachesForClinic:_}){const[f,k]=r.useState(!1),[M,F]=r.useState(!1),[A,C]=r.useState(null),[w,s]=r.useState(i),[u,x]=r.useState(!1);r.useState(!1);const[b,$]=r.useState("desc"),[W,B]=r.useState(!1),[R,G]=r.useState(new Date),p=async g=>{x(!0);const D=se(be(g,{weekStartsOn:0}),"yyyy-MM-dd"),E=se(je(g,{weekStartsOn:0}),"yyyy-MM-dd");let T=[];T.push(`start_date=${D}`),T.push(`end_date=${E}`);const o=Object.entries(l.days).filter(([y,c])=>c).map(([y])=>y.toLowerCase());o.length>0&&T.push(`weekday=${o.join(",")}`);const m=Object.entries(l.timeOfDay).filter(([y,c])=>c).map(([y])=>y.toLowerCase());m.length>0&&T.push(`times=${m.join(",")}`);const n=T.join("&");await N(n),x(!1)},S=g=>{const D=[...w].sort((E,T)=>{const o=new Date(E.clinic_date+" "+E.clinic_start_time),m=new Date(T.clinic_date+" "+T.clinic_start_time);return g==="asc"?o-m:m-o});s(D),$(g),B(!1)};r.useEffect(()=>{let g=[...i];f&&(g=g.filter(E=>parseInt(E.slots_remaining)>0));const D=g.sort((E,T)=>{const o=new Date(E.clinic_date+" "+E.clinic_start_time),m=new Date(T.clinic_date+" "+T.clinic_start_time);return b==="asc"?o-m:m-o});s(D)},[i,b,f]),r.useEffect(()=>{p(R)},[]);const O=()=>{const g=Object.values(l.days).filter(Boolean).length,D=Object.values(l.timeOfDay).filter(Boolean).length,E=l.price.from||l.price.to?1:0,T=l.customFilters?Object.values(l.customFilters).reduce((o,m)=>o+(m&&m.length>0?m.length:0),0):0;return g+D+E+T},q=async()=>{x(!0);let g=[];const D=Object.entries(l.days).filter(([o,m])=>m).map(([o])=>o.toLowerCase());D.length>0&&g.push(`weekday=${D.join(",")}`);const E=Object.entries(l.timeOfDay).filter(([o,m])=>m).map(([o])=>o.toLowerCase());E.length>0&&g.push(`times=${E.join(",")}`),l.customFilters&&Object.entries(l.customFilters).forEach(([o,m])=>{m&&m.length>0&&(o==="categories"?g.push(`category=${m.join(",")}`):o==="subcategories"?g.push(`subcategory=${m.join(",")}`):o==="tags"?g.push(`tag=${m.join(",")}`):g.push(`${o}=${m.join(",")}`))});const T=g.join("&");await N(null,!1,T),F(!1),x(!1)},P=()=>{if(k(!f),f)s(i);else{const g=i.filter(D=>parseInt(D.slots_remaining)>0);s(g)}};return console.log("clinics",w),e.jsxs("div",{className:"mx-auto mt-3 max-w-4xl rounded-lg bg-white p-3 shadow-sm sm:mt-5 sm:p-4",children:[e.jsx(ne,{getActiveFiltersCount:O,clearFilters:v,setIsFilterModalOpen:F,availableOnly:f,toggleAvailableSlots:P,sortOrder:b,showSortOptions:W,setShowSortOptions:B,sortClinics:S}),e.jsx("div",{className:"space-y-3 sm:space-y-4",children:w.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No clinics found"}),e.jsx("p",{className:"px-4 text-center text-xs text-gray-500 sm:text-sm",children:f?"There are no clinics with available slots for the selected filters.":"There are no clinics matching your selected filters."}),(O()>0||f)&&e.jsx("button",{onClick:()=>{v(),k(!1)},className:"mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm",children:"Clear all filters"})]}):w.map(g=>e.jsx("div",{onClick:()=>C(g),children:e.jsx(ie,{clinic:g})},g.id))}),e.jsx(te,{isOpen:M,onClose:()=>F(!1),title:"Filters",primaryButtonText:"Apply and close",onPrimaryAction:q,className:"bg-gray-100",submitting:u,children:e.jsx(t,{filters:l,setFilters:j})}),e.jsx(re,{isOpen:A!==null,onClose:()=>C(null),clinic:A,fetchCoachesForClinic:_})]})}function Ce({programs:i,fetchClinics:N,FiltersContent:t,filters:l,setFilters:j,clearFilters:v,clubProfile:_,fetchCoachesForClinic:f}){const[k,M]=r.useState(!1),[F,A]=r.useState(!1),[C,w]=r.useState(new Date),[s,u]=r.useState(null),[x,b]=r.useState(null),[$,W]=r.useState(i),[B,R]=r.useState(!1),[G,p]=r.useState("desc"),[S,O]=r.useState(!1),q=n=>{const y=[...$].sort((c,h)=>{const a=new Date(c.clinic_date+" "+c.clinic_start_time),d=new Date(h.clinic_date+" "+h.clinic_start_time);return n==="asc"?a-d:d-a});W(y),p(n),O(!1)};r.useEffect(()=>{let n=[...i];if(s){const c=s.toISOString().split("T")[0];n=n.filter(h=>h.clinic_date===c)}k&&(n=n.filter(c=>parseInt(c.slots_remaining)>0));const y=n.sort((c,h)=>{const a=new Date(c.clinic_date+" "+c.clinic_start_time),d=new Date(h.clinic_date+" "+h.clinic_start_time);return G==="asc"?a-d:d-a});W(y)},[i,G,s,k]);const P=()=>{const n=Object.values(l.days).filter(Boolean).length,y=Object.values(l.timeOfDay).filter(Boolean).length,c=l.price.from||l.price.to?1:0,h=l.customFilters?Object.values(l.customFilters).reduce((a,d)=>a+(d&&d.length>0?d.length:0),0):0;return n+y+c+h},g=async n=>{if(n)try{const y=new Date(C.getFullYear(),C.getMonth(),n,0,0,0);u(y);const c=y.toISOString().split("T")[0];let h=[];h.push(`start_date=${c}`),h.push(`end_date=${c}`);const a=Object.entries(l.days).filter(([I,L])=>L).map(([I])=>I.toLowerCase());a.length>0&&h.push(`weekday=${a.join(",")}`);const d=Object.entries(l.timeOfDay).filter(([I,L])=>L).map(([I])=>I.toLowerCase());d.length>0&&h.push(`times=${d.join(",")}`),l.customFilters&&Object.entries(l.customFilters).forEach(([I,L])=>{L&&L.length>0&&(I==="categories"?h.push(`category=${L.join(",")}`):I==="subcategories"?h.push(`subcategory=${L.join(",")}`):I==="tags"?h.push(`tag=${L.join(",")}`):h.push(`${I}=${L.join(",")}`))});const Z=h.join("&");await N(c,!1,Z)}catch(y){console.error("Error handling date selection:",y)}},D=async()=>{u(null);const n=Object.entries(l.days).filter(([a,d])=>d).map(([a])=>a.toLowerCase()),y=Object.entries(l.timeOfDay).filter(([a,d])=>d).map(([a])=>a.toLowerCase());let c=[];n.length>0&&c.push(`weekday=${n.join(",")}`),y.length>0&&c.push(`times=${y.join(",")}`),l.customFilters&&Object.entries(l.customFilters).forEach(([a,d])=>{d&&d.length>0&&(a==="categories"?c.push(`category=${d.join(",")}`):a==="subcategories"?c.push(`subcategory=${d.join(",")}`):a==="tags"?c.push(`tag=${d.join(",")}`):c.push(`${a}=${d.join(",")}`))});const h=c.join("&");await N(null,!1,h)},E=async()=>{R(!0);let n=[];if(s){const a=s.toISOString().split("T")[0];n.push(`start_date=${a}`),n.push(`end_date=${a}`)}const y=Object.entries(l.days).filter(([a,d])=>d).map(([a])=>a.toLowerCase());y.length>0&&n.push(`weekday=${y.join(",")}`);const c=Object.entries(l.timeOfDay).filter(([a,d])=>d).map(([a])=>a.toLowerCase());c.length>0&&n.push(`times=${c.join(",")}`),l.customFilters&&Object.entries(l.customFilters).forEach(([a,d])=>{d&&d.length>0&&(a==="categories"?n.push(`category=${d.join(",")}`):a==="subcategories"?n.push(`subcategory=${d.join(",")}`):a==="tags"?n.push(`tag=${d.join(",")}`):n.push(`${a}=${d.join(",")}`))});const h=n.join("&");await N(s?s.toISOString().split("T")[0]:null,!1,h),A(!1),R(!1)},T=()=>{M(!k)},o=()=>{w(new Date(C.setMonth(C.getMonth()-1)))},m=()=>{w(new Date(C.setMonth(C.getMonth()+1)))};return e.jsx("div",{children:e.jsx("div",{className:"mx-auto max-w-6xl p-2 sm:p-4",children:e.jsxs("div",{className:"flex flex-col gap-4 sm:gap-6 md:flex-row md:gap-8",children:[e.jsxs("div",{className:"h-fit w-full rounded-lg bg-white p-3 shadow-sm sm:p-4 sm:shadow-5 md:w-[350px] lg:w-[400px]",children:[e.jsx(we,{clinics:i,currentMonth:C,selectedDate:s,onDateClick:g,onPreviousMonth:o,onNextMonth:m,onDateSelect:n=>{n&&g(n.getDate())},daysOff:(()=>{try{return _!=null&&_.days_off?JSON.parse(_.days_off):[]}catch(n){return console.error("Error parsing days_off:",n),[]}})()}),s&&e.jsxs("div",{className:"mt-3 flex flex-wrap items-center justify-between border-t border-gray-200 pt-3 sm:mt-4 sm:pt-4",children:[e.jsxs("span",{className:"mr-2 text-xs text-gray-600 sm:text-sm",children:["Showing clinics for"," ",s.toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})]}),e.jsx("button",{onClick:D,className:"text-xs text-blue-600 hover:underline sm:text-sm",children:"Clear"})]})]}),e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"space-y-3 rounded-lg bg-white p-3 shadow-sm sm:space-y-4 sm:p-5",children:[e.jsx(ne,{getActiveFiltersCount:P,clearFilters:v,setIsFilterModalOpen:A,availableOnly:k,toggleAvailableSlots:T,sortOrder:G,showSortOptions:S,setShowSortOptions:O,sortClinics:q}),$.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No clinics found"}),e.jsx("p",{className:"px-4 text-center text-xs text-gray-500 sm:text-sm",children:k?"There are no clinics with available slots for the selected filters.":"There are no clinics matching your selected filters."}),(P()>0||k||s)&&e.jsx("button",{onClick:async()=>{await v(),M(!1),u(null)},className:"mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm",children:"Clear all filters"})]}):$.map(n=>e.jsx("div",{onClick:()=>b(n),children:e.jsx(ie,{clinic:n})},n.id))]}),e.jsx(te,{isOpen:F,onClose:()=>A(!1),title:"Filters",primaryButtonText:"Apply and close",onPrimaryAction:E,className:"bg-gray-100",submitting:B,children:e.jsx(t,{filters:l,setFilters:j})}),e.jsx(re,{isOpen:x!==null,onClose:()=>b(null),clinic:x,fetchCoachesForClinic:f})]})]})})})}function Se({programs:i,fetchClinics:N,FiltersContent:t,filters:l,setFilters:j,clearFilters:v,fetchCoachesForClinic:_}){const[f,k]=r.useState(!1),[M,F]=r.useState(!1),[A,C]=r.useState(null),[w,s]=r.useState(i),[u,x]=r.useState(!1);r.useState(!1);const[b,$]=r.useState("desc"),[W,B]=r.useState(!1),[R,G]=r.useState(0),p=o=>{const m=[...w].sort((n,y)=>{const c=new Date(n.clinic_date+" "+n.clinic_start_time),h=new Date(y.clinic_date+" "+y.clinic_start_time);return o==="asc"?c-h:h-c});s(m),$(o),B(!1)};r.useEffect(()=>{const o=[...i].sort((m,n)=>{const y=new Date(m.clinic_date+" "+m.clinic_start_time),c=new Date(n.clinic_date+" "+n.clinic_start_time);return b==="asc"?y-c:c-y});s(o)},[i,b]);const S=()=>{const o=Object.values(l.days).filter(Boolean).length,m=Object.values(l.timeOfDay).filter(Boolean).length,n=l.price.from||l.price.to?1:0,y=l.customFilters?Object.values(l.customFilters).reduce((c,h)=>c+(h&&h.length>0?h.length:0),0):0;return o+m+n+y},O=async()=>{x(!0);let o=[];o.push(`week=${R}`);const m=Object.entries(l.days).filter(([c,h])=>h).map(([c])=>c.toLowerCase());m.length>0&&o.push(`weekday=${m.join(",")}`);const n=Object.entries(l.timeOfDay).filter(([c,h])=>h).map(([c])=>c.toLowerCase());n.length>0&&o.push(`times=${n.join(",")}`),l.customFilters&&Object.entries(l.customFilters).forEach(([c,h])=>{h&&h.length>0&&(c==="categories"?o.push(`category=${h.join(",")}`):c==="subcategories"?o.push(`subcategory=${h.join(",")}`):c==="tags"?o.push(`tag=${h.join(",")}`):o.push(`${c}=${h.join(",")}`))});const y=o.join("&");await N(null,!1,y),F(!1),x(!1)},q=()=>{if(k(!f),f)s(i);else{const o=i.filter(m=>parseInt(m.slots_remaining)>0);s(o)}},[P,g]=r.useState(Ne()),D=async()=>{if(R>0){const o=R-1;G(o),g(m=>m.clone().subtract(1,"week")),await N(null,!1,`filter=week+${o}`)}},E=async()=>{const o=R+1;G(o),g(m=>m.clone().add(1,"week")),await N(null,!1,`filter=week+${o}`)},T=()=>{const o=P.clone().startOf("week"),m=P.clone().endOf("week"),n=`${o.format("MMM D")} - ${m.format("MMM D")}`;return R===0?`This week (${n})`:R===1?`Next week (${n})`:`${R} weeks from now (${n})`};return r.useEffect(()=>{N(null,!1,"filter=week")},[]),e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"mx-auto max-w-4xl rounded-lg bg-white p-3 shadow-sm sm:p-4",children:[e.jsx("div",{className:"mx-auto mb-3 mt-3 w-fit max-w-xs rounded-xl bg-white p-1 shadow-sm sm:mb-5 sm:mt-5 sm:max-w-lg",children:e.jsxs("div",{className:"flex items-center justify-between gap-2 rounded-xl bg-gray-50 p-2 sm:gap-4",children:[e.jsx("button",{onClick:D,disabled:R===0,className:`rounded-xl bg-white p-1 text-gray-600 sm:p-2 ${R===0?"cursor-not-allowed opacity-50":"hover:text-gray-800"}`,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:e.jsx("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("div",{className:"text-center text-sm font-medium sm:text-lg",children:T()}),e.jsx("button",{onClick:E,className:"rounded-xl bg-white p-1 text-gray-600 hover:text-gray-800 sm:p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:e.jsx("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})}),e.jsx(ne,{getActiveFiltersCount:S,clearFilters:v,setIsFilterModalOpen:F,availableOnly:f,toggleAvailableSlots:q,sortOrder:b,showSortOptions:W,setShowSortOptions:B,sortClinics:p}),e.jsx("div",{className:"mx-auto mt-4 max-w-4xl space-y-3 sm:space-y-4",children:w.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No clinics found"}),e.jsx("p",{className:"px-4 text-center text-xs text-gray-500 sm:text-sm",children:f?"There are no clinics with available slots for the selected filters.":"There are no clinics matching your selected filters."}),(S()>0||f)&&e.jsx("button",{onClick:()=>{v(),k(!1)},className:"mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm",children:"Clear all filters"})]}):w.map(o=>e.jsx("div",{onClick:()=>C(o),children:e.jsx(ie,{clinic:o})},o.id))})]}),e.jsx(te,{isOpen:M,onClose:()=>F(!1),title:"Filters",primaryButtonText:"Apply and close",onPrimaryAction:O,className:"bg-gray-100",submitting:u,children:e.jsx(t,{filters:l,setFilters:j})}),e.jsx(re,{isOpen:A!==null,onClose:()=>C(null),clinic:A,fetchCoachesForClinic:_})]})}let X=new ge,Q=new de;function ae({filters:i,setFilters:N,customFilters:t=[],programs:l=[],availableCategories:j=[],availableSubcategories:v=[],availableTags:_=[]}){const[f,k]=r.useState({dayOfWeek:!0,timeOfDay:!0,priceRange:!0,categories:!0,subcategories:!0,tags:!0,...t.reduce((s,u)=>({...s,[`custom_${u.id}`]:!0}),{})}),M=s=>{k(u=>({...u,[s]:!u[s]}))},F=s=>{N(u=>({...u,days:{...u.days,[s.toLowerCase()]:!u.days[s.toLowerCase()]}}))},A=s=>{N(u=>({...u,timeOfDay:{...u.timeOfDay,[s.toLowerCase()]:!u.timeOfDay[s.toLowerCase()]}}))},C=(s,u)=>{N(x=>({...x,price:{...x.price,[s]:u}}))},w=(s,u)=>{N(x=>{var b,$,W;return{...x,customFilters:{...x.customFilters,[s]:($=(b=x.customFilters)==null?void 0:b[s])!=null&&$.includes(u)?x.customFilters[s].filter(B=>B!==u):[...((W=x.customFilters)==null?void 0:W[s])||[],u]}}})};return e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>M("dayOfWeek"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Day of week"}),e.jsx(Y,{size:20,className:`text-gray-400 transition-transform duration-200 ${f.dayOfWeek?"rotate-180":""}`})]}),f.dayOfWeek&&e.jsx("div",{className:"space-y-3",children:["Weekend","Weekday","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"].map(s=>e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:i.days[s.toLowerCase()],onChange:()=>F(s),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:s})]},s))})]}),e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>M("timeOfDay"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Time of the day"}),e.jsx(Y,{size:20,className:`text-gray-400 transition-transform duration-200 ${f.timeOfDay?"rotate-180":""}`})]}),f.timeOfDay&&e.jsx("div",{className:"space-y-3",children:["Morning","Afternoon","Evening"].map(s=>e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:i.timeOfDay[s.toLowerCase()],onChange:()=>A(s),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:s})]},s))})]}),e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>M("priceRange"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Price range"}),e.jsx(Y,{size:20,className:`text-gray-400 transition-transform duration-200 ${f.priceRange?"rotate-180":""}`})]}),f.priceRange&&e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"From"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-500",children:"$"}),e.jsx("input",{type:"number",value:i.price.from,onChange:s=>C("from",s.target.value),className:"w-full rounded-lg border border-gray-200 px-7 py-2 focus:border-blue-500 focus:outline-none",placeholder:"0.00"})]})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"To"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-500",children:"$"}),e.jsx("input",{type:"number",value:i.price.to,onChange:s=>C("to",s.target.value),className:"w-full rounded-lg border border-gray-200 px-7 py-2 focus:border-blue-500 focus:outline-none",placeholder:"0.00"})]})]})]})]}),t.filter(s=>s.enabled).map(s=>{const u=[...new Set(l.map(x=>x[s.key]).filter(x=>x!=null&&x!==""))];return u.length===0?null:e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>M(`custom_${s.id}`),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:s.label}),e.jsx(Y,{size:20,className:`text-gray-400 transition-transform duration-200 ${f[`custom_${s.id}`]?"rotate-180":""}`})]}),f[`custom_${s.id}`]&&e.jsx("div",{className:"space-y-3",children:u.map(x=>{var b,$;return e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:(($=(b=i.customFilters)==null?void 0:b[s.key])==null?void 0:$.includes(x))||!1,onChange:()=>w(s.key,x),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:s.key==="recurring"?x===1?"Yes":"No":x})]},x)})})]},s.id)}),e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>M("categories"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Categories"}),e.jsx(Y,{size:20,className:`text-gray-400 transition-transform duration-200 ${f.categories?"rotate-180":""}`})]}),f.categories&&e.jsx("div",{className:"space-y-3",children:j.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"No categories available. Create clinics with categories to see filter options."}):j.map(s=>{var u,x;return e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:((x=(u=i.customFilters)==null?void 0:u.categories)==null?void 0:x.includes(s.name))||!1,onChange:()=>w("categories",s.name),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:s.name})]},s.id)})})]}),e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>M("subcategories"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Subcategories"}),e.jsx(Y,{size:20,className:`text-gray-400 transition-transform duration-200 ${f.subcategories?"rotate-180":""}`})]}),f.subcategories&&e.jsx("div",{className:"space-y-3",children:v.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"No subcategories available. Create clinics with subcategories to see filter options."}):v.map(s=>{var u,x;return e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:((x=(u=i.customFilters)==null?void 0:u.subcategories)==null?void 0:x.includes(s.name))||!1,onChange:()=>w("subcategories",s.name),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:s.name})]},s.id)})})]}),e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>M("tags"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Tags"}),e.jsx(Y,{size:20,className:`text-gray-400 transition-transform duration-200 ${f.tags?"rotate-180":""}`})]}),f.tags&&e.jsx("div",{className:"space-y-3",children:_.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"No tags available. Create clinics with tags to see filter options."}):_.map(s=>{var u,x;return e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:((x=(u=i.customFilters)==null?void 0:u.tags)==null?void 0:x.includes(s.name))||!1,onChange:()=>w("tags",s.name),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:s.name})]},s.id)})})]})]})}function ds(){const[i,N]=r.useState(null),[t,l]=r.useState([]),[j,v]=r.useState(null),[_,f]=r.useState([]),[k,M]=r.useState([]),{dispatch:F}=r.useContext(ce),{dispatch:A}=r.useContext(ue),{user_subscription:C,club_membership:w}=xe(),[s,u]=r.useState([]),[x,b]=r.useState(!1),[$,W]=r.useState([]),[B,R]=r.useState([]),[G,p]=r.useState([]),[S,O]=r.useState([]),q=r.useMemo(()=>!(C!=null&&C.planId)||!(w!=null&&w.length)?null:w.find(a=>a.plan_id===C.planId),[C,w]),[P,g]=r.useState({days:{weekend:!1,weekday:!1,sunday:!1,monday:!1,tuesday:!1,wednesday:!1,thursday:!1,friday:!1,saturday:!1},timeOfDay:{morning:!1,afternoon:!1,evening:!1},price:{from:"",to:""},customFilters:{}}),D=[{id:"table",label:"Table"},{id:"calendar",label:"Calendar"},{id:"weekly",label:"Weekly"}],E=localStorage.getItem("user"),T=async()=>{var a;try{const d=await X.getOne("user",E,{}),Z=await X.getOne("clubs",d.model.club_id,{}),I=await X.getList("sports",{filter:[`club_id,eq,${d.model.club_id}`]});console.log("sportsResponse",I),l(I.list),v(Z.model);let L="table";if((a=Z.model)!=null&&a.clinic_description)try{const U=JSON.parse(Z.model.clinic_description);U.default_view&&(L=U.default_view),U.custom_filters&&W(U.custom_filters)}catch(U){console.error("Error parsing clinic_description:",U)}N(L)}catch(d){console.error(d),N("table")}},o=async()=>{const a=await X.getList("coach",{join:["user|user_id"]}),d=await X.getList("user",{filter:["role,cs,user"]});f(a.list),M(d.list)},m=async()=>{try{if(!(j!=null&&j.id))return;Q.setTable("clinic_categories");const a=await Q.callRestAPI({filter:[`club_id,eq,${j.id}`]},"GETALL");Q.setTable("clinic_subcategories");const d=await Q.callRestAPI({filter:[`club_id,eq,${j.id}`]},"GETALL");Q.setTable("clinic_tags");const Z=await Q.callRestAPI({filter:[`club_id,eq,${j.id}`]},"GETALL");R(a.list||[]),p(d.list||[]),O(Z.list||[])}catch(a){console.error("Error fetching categories, subcategories and tags:",a),R([]),p([]),O([])}},n=async(a,d=!1,Z="")=>{var I;b(!0);try{let L="/v3/api/custom/courtmatchup/user/clinics";if(Z)L+=`?${Z}`;else{let J=[];if(!d){if(a&&!isNaN(new Date(a).getTime())){const H=new Date(a);H.setHours(0,0,0,0);const z=new Date(H);z.setDate(z.getDate()+6),z.setHours(23,59,59,999),J.push(`start_date=${H.toISOString().split("T")[0]}`),J.push(`clinic_end_date=${z.toISOString().split("T")[0]}`)}const le=Object.entries(P.days).filter(([H,z])=>z).map(([H])=>H.toLowerCase());le.length>0&&J.push(`weekday=${le.join(",")}`);const oe=Object.entries(P.timeOfDay).filter(([H,z])=>z).map(([H])=>H.toLowerCase());oe.length>0&&J.push(`times=${oe.join(",")}`),P.customFilters&&Object.entries(P.customFilters).forEach(([H,z])=>{z&&z.length>0&&(H==="categories"?J.push(`category=${z.join(",")}`):H==="subcategories"?J.push(`subcategory=${z.join(",")}`):H==="tags"?J.push(`tag=${z.join(",")}`):J.push(`${H}=${z.join(",")}`))})}((I=q==null?void 0:q.applicable_sports)==null?void 0:I.length)>0&&J.push(`sport_ids=${q.applicable_sports.join(",")}`),J.length===0&&J.push("week=0"),J.length>0&&(L+=`?${J.join("&")}`)}const U=await Q.callRawAPI(L,{},"GET");!U.error&&(U!=null&&U.programs)&&u(U.programs)}catch(L){console.log(L),K(F,L.message,"3000","error"),fe(A,L.status)}finally{b(!1)}},y=r.useCallback(async a=>{try{if(!a||!Array.isArray(a)||a.length===0)return[];const d=await he(F,A,"coach",a,"user|user_id");return d.list&&Array.isArray(d.list)?d.list:[]}catch(d){return console.error("Error fetching coaches for clinic:",d),[]}},[F,A]),c=async()=>{g({days:{weekend:!1,weekday:!1,sunday:!1,monday:!1,tuesday:!1,wednesday:!1,thursday:!1,friday:!1,saturday:!1},timeOfDay:{morning:!1,afternoon:!1,evening:!1},price:{from:"",to:""},customFilters:{}}),await n(null,!0)};r.useEffect(()=>{(async()=>(b(!0),await T(),await o(),await m(),b(!1),F({type:"SETPATH",payload:{path:"program-clinics"}})))()},[]),r.useEffect(()=>{i&&(async()=>(b(!0),await n(),b(!1)))()},[i]),r.useEffect(()=>{j!=null&&j.id&&m()},[j==null?void 0:j.id]),console.log("programs",s);async function h(a){await c(),N(a)}return e.jsxs(e.Fragment,{children:[(x||!i)&&e.jsx(pe,{}),i&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col justify-between bg-white px-3 py-3 sm:flex-row sm:items-center sm:px-4 sm:py-4",children:[e.jsx("h1",{className:"mb-3 text-xl font-semibold sm:mb-6 sm:text-2xl",children:"Clinics"}),e.jsx("div",{className:"mb-4 flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:mb-8 sm:text-sm",children:D.map(a=>e.jsx("button",{onClick:()=>h(a.id),className:`whitespace-nowrap px-2 py-1.5 sm:px-3 sm:py-2 ${i===a.id?"bg-white-600":"bg-gray-100 text-gray-600"}`,children:a.label},a.id))})]}),e.jsx("div",{className:"px-2 py-2 sm:px-3 sm:py-3",children:e.jsxs("div",{className:"mx-auto max-w-7xl",children:[i==="table"&&e.jsx(ve,{programs:s,fetchClinics:n,FiltersContent:a=>e.jsx(ae,{...a,customFilters:$,programs:s,availableCategories:B,availableSubcategories:G,availableTags:S}),filters:P,setFilters:g,clearFilters:c,fetchCoachesForClinic:y}),i==="calendar"&&e.jsx(Ce,{programs:s,fetchClinics:n,FiltersContent:a=>e.jsx(ae,{...a,customFilters:$,programs:s,availableCategories:B,availableSubcategories:G,availableTags:S}),filters:P,setFilters:g,clubProfile:j,clearFilters:c,fetchCoachesForClinic:y}),i==="weekly"&&e.jsx(Se,{programs:s,fetchClinics:n,FiltersContent:a=>e.jsx(ae,{...a,customFilters:$,programs:s,availableCategories:B,availableSubcategories:G,availableTags:S}),filters:P,setFilters:g,clearFilters:c,fetchCoachesForClinic:y})]})})]})]})}export{ds as default};
