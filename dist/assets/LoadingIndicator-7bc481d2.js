import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import{m as s}from"./framer-motion-c6ba3e69.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";const o={start:{transition:{staggerChildren:.2}},end:{transition:{staggerChildren:.2}}},a={start:{y:"0%"},end:{y:"100%"}},i={duration:.4,yoyo:1/0,ease:"easeIn"};function f({dotsClasses:r,size:m,style:e}){const n="block w-[9px] h-[9px] bg-slate-900 rounded-md shrink-0 "+r;return t.jsxs(s.div,{variants:o,className:"flex justify-between items-center w-[40px] pb-[10px]",initial:"start",animate:"end",style:{...e},children:[t.jsx(s.span,{className:n,variants:a,transition:i}),t.jsx(s.span,{className:n,variants:a,transition:i}),t.jsx(s.span,{className:n,variants:a,transition:i})]})}export{f as default};
