import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as u}from"./vendor-851db8c1.js";import{B as C}from"./BottomDrawer-3018f655.js";import{T as D}from"./TimeSlotGrid-d346d4e3.js";const E=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];function W({showTimesAvailableModal:p,setShowTimesAvailableModal:e,selectedTimes:n,setSelectedTimes:c,title:l,onSave:x,isSubmitting:m}){const h=(a,i)=>{const s=n==null?void 0:n.find(o=>o.day===i.toLowerCase());if(!s)return!1;const r=a.replace(":00","");return s.timeslots.some(o=>o===a||o.replace(":00","")===r)},d=(a,i)=>{c(s=>s.map(r=>{if(r.day===i.toLowerCase()){const o=a.replace(":00","");if(!r.timeslots.some(f=>f===a||f===o))return{...r,timeslots:[...r.timeslots,a].sort()}}return r}))},j=(a,i)=>{c(s=>s.map(r=>r.day===i.toLowerCase()?{...r,timeslots:r.timeslots.filter(o=>o!==a&&o!==a.replace(":00",""))}:r))},v=u.useMemo(()=>t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("div",{className:"flex gap-3"}),t.jsx("p",{className:"text-xl",children:n==null?void 0:n.name})]}),[n==null?void 0:n.name]),y=u.useCallback(()=>{c([]),e(!1)},[e]);return t.jsx(C,{isOpen:p,onClose:()=>e(!1),title:l,onDiscard:y,discardLabel:"Discard",showActions:!0,saveLabel:"Save changes",leftElement:v,onSave:x,isSubmitting:m,children:t.jsx("div",{className:"relative overflow-hidden",children:t.jsx(D,{days:E,isSelected:h,handleTimeSelect:d,handleDeleteTime:j,containerRef:u.useRef(null)})})})}export{W as T};
