<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Court Matchup - Tennis Court Booking and Matchmaking"
    />
    <meta name="theme-color" content="#ffffff" />
    <link rel="icon" type="image/svg+xml" id="favicon" href="/favicon.png" />
    <link rel="apple-touch-icon" id="apple-touch-icon" href="/favicon.png" />

    <!-- <link rel="icon" type="image/svg+xml" href="/tennis.ico" />
    <link rel="apple-touch-icon" href="/tennis.ico" /> -->

    <title>Court Match Up</title>
    <script type="module" crossorigin src="/assets/index-13fd629e.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-851db8c1.js">
    <link rel="modulepreload" crossorigin href="/assets/@craftjs/core-d3c11b68.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/fontawesome-svg-core-4fa3e289.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/react-fontawesome-13437837.js">
    <link rel="modulepreload" crossorigin href="/assets/@nivo/heatmap-ba1ecfff.js">
    <link rel="modulepreload" crossorigin href="/assets/react-confirm-alert-cd7ccfe7.js">
    <link rel="modulepreload" crossorigin href="/assets/@tanstack/react-query-20158223.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/stripe-js-6b714a86.js">
    <link rel="modulepreload" crossorigin href="/assets/moment-a9aaa855.js">
    <link rel="modulepreload" crossorigin href="/assets/cal-heatmap-cf010ec4.js">
    <link rel="modulepreload" crossorigin href="/assets/react-icons-51bc3cff.js">
    <link rel="modulepreload" crossorigin href="/assets/smoothscroll-polyfill-a5c0a116.js">
    <link rel="modulepreload" crossorigin href="/assets/date-fns-cca0f4f7.js">
    <link rel="modulepreload" crossorigin href="/assets/lodash-91d5d207.js">
    <link rel="modulepreload" crossorigin href="/assets/numeral-ea653b2a.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/react-stripe-js-64f0e61f.js">
    <link rel="modulepreload" crossorigin href="/assets/react-hook-form-687afde5.js">
    <link rel="modulepreload" crossorigin href="/assets/@mantine/core-8cbffb6d.js">
    <link rel="modulepreload" crossorigin href="/assets/@emotion/cache-9a5b99cd.js">
    <link rel="modulepreload" crossorigin href="/assets/@emotion/utils-8a8f62c5.js">
    <link rel="modulepreload" crossorigin href="/assets/@emotion/serialize-460cad7f.js">
    <link rel="modulepreload" crossorigin href="/assets/@emotion/react-89b506c3.js">
    <link rel="modulepreload" crossorigin href="/assets/@fullcalendar/core-8ccc1ac4.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/aws-s3-c5961f7a.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/compressor-11f993e4.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/core-0760343f.js">
    <link rel="modulepreload" crossorigin href="/assets/@uppy/dashboard-4a19149e.js">
    <link rel="modulepreload" crossorigin href="/assets/react-select-c8303602.js">
    <link rel="modulepreload" crossorigin href="/assets/@headlessui/react-a5400090.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-solid-svg-icons-0a9c4907.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-regular-svg-icons-0a88e957.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-brands-svg-icons-fae0dcac.js">
    <link rel="stylesheet" href="/assets/core-b9802b0d.css">
    <link rel="stylesheet" href="/assets/index-be128dce.css">
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this application.</noscript>
    <main id="root"></main>
    <div id="portal" role="presentation"></div>

    
  </body>
</html>
