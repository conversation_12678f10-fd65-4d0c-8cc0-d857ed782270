import React from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "Context/Global";
import Skeleton from "react-loading-skeleton";
import { ModalSidebar } from "Components/ModalSidebar";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import { FaArrowRight } from "react-icons/fa";
import { HiOutlineTrash } from "react-icons/hi";
// import AddAdminEmailPage from "../Add/AddAdminEmailPage";
// import EditAdminEmailPage from "../Edit/EditAdminEmailPage";
import { AddButton } from "Components/AddButton";
import { PaginationBar } from "Components/PaginationBar";
import _, { set } from "lodash";
import SkeletonLoader from "Components/Skeleton/Skeleton";
import { Link } from "react-router-dom";
import { EmailTemplateDrawer } from "Components/EmailTemplateDrawer/EmailTemplateDrawer";
import DeleteModal from "Components/Modals/DeleteModal";
import TreeSDK from "Utils/TreeSDK";
import RightSideModal from "Components/RightSideModal";

let sdk = new MkdSDK();
let treeSdk = new TreeSDK();
const columns = [
  {
    header: "Subject",
    accessor: "subject",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Action",
    accessor: "",
  },
];

const AdminListEmailTablePage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [optionValue, setOptionValue] = React.useState("eq");
  const [loading, setLoading] = React.useState(true);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const navigate = useNavigate();
  const dropdownFilterRef = React.useRef(null);
  const [isEmailDrawerOpen, setIsEmailDrawerOpen] = React.useState(false);
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [selectedEmailId, setSelectedEmailId] = React.useState(null);
  const [members, setMembers] = React.useState([]);
  const [isEmailModalOpen, setIsEmailModalOpen] = React.useState(false);
  const [editingEmail, setEditingEmail] = React.useState(null);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);

  const schema = yup.object({
    id: yup.string(),
    email: yup.string(),
    role: yup.string(),
    status: yup.string(),
  });

  const {
    register,
    handleSubmit: handleFormSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const selectRole = ["", "admin", "employee"];
  const selectStatus = [
    { key: "", value: "All" },
    { key: "0", value: "Inactive" },
    { key: "1", value: "Active" },
    { key: "2", value: "Suspend" },
  ];

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }

  const addFilterCondition = (option, selectedValue, inputValue) => {
    const input =
      selectedValue === "eq" && isNaN(inputValue)
        ? `${inputValue}`
        : inputValue;
    const condition = `${option},${selectedValue},${input}`;
    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (condition) => !condition.includes(option)
      );
      return [...newConditions, condition];
    });
  };

  const handleFilter = () => {
    getData(0, pageSize, {}, filterConditions);
  };
  const deleteFilter = (deleted) => {
    getData(0, pageSize, {}, deleted);
  };

  async function getData(pageNum, limitNum, data = {}, filters = []) {
    setLoading(showAddSidebar || showEditSidebar ? false : true);
    try {
      sdk.setTable("email");

      const result = await sdk.callRestAPI(
        {
          payload: {
            ...data,
          },
          page: pageNum,
          limit: limitNum,
          filter: filters,
        },
        "PAGINATE"
      );

      if (result) {
        setLoading(false);
      }
      const { list, total, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const fetchMembers = async () => {
    // setUsersLoading(true);
    try {
      sdk.setTable("user");
      const memberResponse = await sdk.callRestAPI(
        {
          filter: ["role,cs,user"],
        },
        "GETALL"
      );
      sdk.setTable("profile");
      const profileResponse = await sdk.callRestAPI({}, "GETALL");

      const mergedMembers = memberResponse.list.map((member) => {
        const profile = profileResponse.list.find(
          (p) => p.user_id === member.id
        );
        return {
          ...member,
          ...profile,
        };
      });
      // console.log("mergedMembers", mergedMembers);
      setMembers(mergedMembers);
    } catch (error) {
      console.error("Error fetching users:", error);
      return [];
    } finally {
      // setUsersLoading(false);
    }
  };

  const onSubmit = (data) => {
    if (data.search) {
      getData(0, pageSize, {}, [
        `first_name,cs,${data.search}`,
        `last_name,cs,${data.search}`,
      ]);
    } else {
      getData(0, pageSize);
    }
  };
  const onStatusSelect = (e) => {
    if (e.target.value === "") {
      getData(0, pageSize);
    } else {
      getData(0, pageSize, {}, [`status,cs,${e.target.value}`]);
    }
  };
  const onAgeGroupSelect = (e) => {
    getData(0, pageSize, {}, [`age_group,cs,${e.target.value}`]);
  };

  const handleEmailSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (editingEmail?.id) {
        // Update existing template
        sdk.setTable("email");
        const result = await sdk.callRestAPI(
          {
            subject: editingEmail.subject,
            html: editingEmail.html,
            id: editingEmail.id,
          },
          "PUT"
        );

        if (!result.error) {
          showToast(
            globalDispatch,
            "Email template updated successfully!",
            3000,
            "success"
          );

          setCurrentTableData(
            data.map((item) =>
              item.id === editingEmail.id ? { ...item, ...editingEmail } : item
            )
          );
        }
      } else {
        // Create new template
        sdk.setTable("email");
        const result = await sdk.callRestAPI(
          {
            subject: editingEmail.subject,
            html: editingEmail.html,
          },
          "POST"
        );

        if (!result.error) {
          showToast(
            globalDispatch,
            "Email template created successfully!",
            3000,
            "success"
          );
          setCurrentTableData([{ ...editingEmail, id: result.data }, ...data]);
        }
      }
      setIsEmailModalOpen(false);
      setEditingEmail(null);
    } catch (error) {
      console.error(error);
      showToast(globalDispatch, "Error creating email template", 3000, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (id) => {
    setIsDeleting(true);
    try {
      sdk.setTable("email");
      const result = await sdk.callRestAPI({ id }, "DELETE");
      if (!result.error) {
        showToast(
          globalDispatch,
          "Email template deleted successfully!",
          3000,
          "success"
        );
        setCurrentTableData(data.filter((item) => item.id !== id));
      }
    } catch (error) {
      console.error(error);
      showToast(
        globalDispatch,
        "An error occurred while deleting the template.",
        3000,
        "error"
      );
    } finally {
      setIsDeleting(false);
      setShowDeleteModal(false);
      setSelectedEmailId(null);
      setIsDeleteModalOpen(false);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "email",
      },
    });
    fetchMembers();
    const delay = 700;
    const timeoutId = setTimeout(async () => {
      await getData(1, pageSize);
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  const handleClickOutside = (event) => {
    if (
      dropdownFilterRef.current &&
      !dropdownFilterRef.current.contains(event.target)
    ) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const clearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    getData(1, pageSize);
  };

  return (
    <div className="h-screen px-8">
      <div className="flex flex-col justify-between gap-4 py-3 md:flex-row md:items-center">
        <div className="relative flex max-w-[300px] flex-1 items-center justify-between">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <BiSearch className="text-gray-500" />
          </div>
          <input
            type="text"
            className="block w-full rounded-xl border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="search email subject"
            onChange={(e) => {
              if (e.target.value) {
                getData(0, pageSize, {}, [`subject,cs,${e.target.value}`]);
              } else {
                getData(0, pageSize);
              }
            }}
          />
        </div>

        <div className="flex items-center gap-4">
          <button
            onClick={() => setIsEmailDrawerOpen(true)}
            className="inline-flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-4 py-2 text-sm font-semibold text-gray-500 hover:bg-gray-100"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M4.99919 10.0017H7.70752M4.99919 10.0017L2.81729 3.45596C2.6999 3.1038 3.06689 2.78551 3.39891 2.95152L16.7538 9.62898C17.0609 9.78253 17.0609 10.2208 16.7538 10.3743L3.39891 17.0518C3.06689 17.2178 2.6999 16.8995 2.81729 16.5474L4.99919 10.0017Z"
                stroke="#868C98"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            Send custom email
          </button>
          <button
            onClick={() => navigate("/club/program-clinics/add")}
            className="inline-flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-4 py-2 text-sm font-semibold text-gray-500 hover:bg-gray-100"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10 6.45703V9.9987L12.9167 12.9154"
                stroke="#868C98"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M2.29102 3.95703V7.29036H5.62435"
                stroke="#868C98"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M2.70898 12.5707C3.76873 15.5646 6.62818 17.7096 9.98935 17.7096C14.2528 17.7096 17.709 14.2585 17.709 10.0013C17.709 5.74411 14.2528 2.29297 9.98935 2.29297C6.79072 2.29297 4.04646 4.23552 2.87521 7.00362"
                stroke="#868C98"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            History
          </button>

          <button
            onClick={() => {
              setEditingEmail(null);
              setIsEmailModalOpen(true);
            }}
            className="inline-flex items-center gap-2 rounded-xl bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700"
          >
            <span>+</span>
            Add new
          </button>

          {/* <button className="inline-flex items-center gap-2 rounded-md border border-gray-200 px-4 py-2 text-sm font-semibold text-gray-700 hover:bg-gray-50">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path
                    d="M3.33301 10H16.6663M3.33301 5H16.6663M3.33301 15H16.6663"
                    stroke="currentColor"
                    strokeWidth="1.67"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                History
              </button> */}
        </div>
      </div>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full min-w-full table-auto border-separate border-spacing-y-2">
            <thead className="!border-none">
              <tr>
                {columns.map((column, index) => {
                  if (column.accessor === "") {
                    return (
                      <th
                        key={index}
                        scope="col"
                        className="whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500"
                      ></th>
                    );
                  }
                  return (
                    <th
                      key={index}
                      scope="col"
                      className="whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500"
                    >
                      {column.header}
                    </th>
                  );
                })}
              </tr>
            </thead>
            <tbody className="">
              {data.map((row, i) => {
                return (
                  <tr
                    key={i}
                    className=" bg-gray-100 px-4 py-3 hover:bg-gray-50"
                  >
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900">
                          {row.subject}
                        </div>
                      </div>
                    </td>
                    <td className="flex justify-end whitespace-nowrap px-6 py-4">
                      <div className="flex justify-end gap-4">
                        <button
                          onClick={() => {
                            setEditingEmail(row);
                            setIsEmailModalOpen(true);
                          }}
                          className="text-sm font-medium text-blue-600 hover:text-blue-800"
                        >
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M13.041 7.20951L15.5768 4.67377C15.9022 4.34833 16.4298 4.34833 16.7553 4.67376L19.3268 7.24525C19.6522 7.57069 19.6522 8.09833 19.3268 8.42377L16.791 10.9595M13.041 7.20951L4.53509 15.7154C4.37881 15.8717 4.29102 16.0837 4.29102 16.3047V19.7095H7.69584C7.91685 19.7095 8.12881 19.6217 8.28509 19.4654L16.791 10.9595M13.041 7.20951L16.791 10.9595"
                              stroke="#868C98"
                              stroke-width="1.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                        </button>
                        <button
                          onClick={() => {
                            setSelectedEmailId(row.id);
                            setIsDeleteModalOpen(true);
                          }}
                          className="text-sm font-medium text-red-600 hover:text-red-800"
                        >
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M6.79167 6.79297V18.8763C6.79167 19.3365 7.16476 19.7096 7.625 19.7096H16.375C16.8352 19.7096 17.2083 19.3365 17.2083 18.8763V6.79297M6.79167 6.79297H17.2083M6.79167 6.79297H5.125M17.2083 6.79297H18.875M13.6667 10.9596V15.543M10.3333 10.9596V15.543M9.5 6.79297C9.5 5.41226 10.6193 4.29297 12 4.29297C13.3807 4.29297 14.5 5.41226 14.5 6.79297"
                              stroke="#868C98"
                              stroke-width="1.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          {loading && (
            <div className="px-6 py-4">
              <p className="text-gray-500">Loading...</p>
            </div>
          )}
          {!loading && data.length === 0 && (
            <div className="w-full px-6 py-4 text-center">
              <p className="text-gray-500">No data available</p>
            </div>
          )}
        </div>
      )}
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={(newPageSize) => {
          setPageSize(newPageSize);
          getData(1, newPageSize);
        }}
        previousPage={previousPage}
        nextPage={nextPage}
        gotoPage={(pageNum) => getData(pageNum, pageSize)}
      />
      {/* <ModalSidebar
            isModalActive={showAddSidebar}
            closeModalFn={() => setShowAddSidebar(false)}
          >
            <AddAdminEmailPage setSidebar={setShowAddSidebar} getData={getData} />
          </ModalSidebar> */}
      {/* {showEditSidebar && (
            <ModalSidebar
              isModalActive={showEditSidebar}
              closeModalFn={() => setShowEditSidebar(false)}
            >
              <EditAdminEmailPage
                activeId={activeEditId}
                setSidebar={setShowEditSidebar}
              />
            </ModalSidebar>
          )} */}
      <EmailTemplateDrawer
        isOpen={isEmailDrawerOpen}
        onClose={() => setIsEmailDrawerOpen(false)}
        members={members}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedEmailId(null);
        }}
        onDelete={() => handleDelete(selectedEmailId)}
        loading={isDeleting}
      />
      <RightSideModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        title={editingEmail ? "Edit Email Template" : "Add New Email Template"}
        primaryButtonText={editingEmail ? "Save Template" : "Save Template"}
        onPrimaryAction={handleEmailSubmit}
        submitting={isSubmitting}
      >
        <form onSubmit={handleEmailSubmit} className="space-y-6 py-6">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Subject
            </label>
            <input
              type="text"
              name="subject"
              value={editingEmail?.subject || ""}
              onChange={(e) =>
                setEditingEmail({ ...editingEmail, subject: e.target.value })
              }
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Message
            </label>
            <textarea
              name="message"
              value={editingEmail?.html || ""}
              onChange={(e) =>
                setEditingEmail({ ...editingEmail, html: e.target.value })
              }
              rows={6}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              required
            />
          </div>
        </form>
      </RightSideModal>
    </div>
  );
};

export default AdminListEmailTablePage;
