import React, { useState, useEffect } from 'react';
import { usePushNotifications } from 'Context/PushNotificationContext';
import { FaBell, FaBellSlash, FaCheck, FaTimes, FaExclamationTriangle } from 'react-icons/fa';

const PushNotificationManager = ({ className = '' }) => {
  const {
    isSupported,
    isSubscribed,
    permission,
    requestPermission,
    subscribe,
    unsubscribe,
    showNotification
  } = usePushNotifications();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Clear messages after 5 seconds
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError('');
        setSuccess('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, success]);

  const handleSubscribe = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      await subscribe();
      setSuccess('Push notifications enabled successfully!');
    } catch (err) {
      setError(err.message || 'Failed to enable push notifications');
    } finally {
      setLoading(false);
    }
  };

  const handleUnsubscribe = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      await unsubscribe();
      setSuccess('Push notifications disabled successfully!');
    } catch (err) {
      setError(err.message || 'Failed to disable push notifications');
    } finally {
      setLoading(false);
    }
  };

  const handleTestNotification = () => {
    showNotification('Test Notification', {
      body: 'This is a test notification from Court Matchup!',
      icon: '/courtmatchup-logo.png',
      tag: 'test-notification',
      requireInteraction: false,
      actions: [
        {
          action: 'open',
          title: 'Open App'
        }
      ]
    });
    setSuccess('Test notification sent!');
  };

  if (!isSupported) {
    return (
      <div className={`rounded-lg border border-orange-200 bg-orange-50 p-4 ${className}`}>
        <div className="flex items-center">
          <FaExclamationTriangle className="mr-3 h-5 w-5 text-orange-500" />
          <div>
            <h3 className="text-sm font-medium text-orange-800">
              Push Notifications Not Supported
            </h3>
            <p className="mt-1 text-sm text-orange-700">
              Your browser doesn't support push notifications.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`rounded-lg border border-gray-200 bg-white p-6 shadow-sm ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {isSubscribed ? (
            <FaBell className="mr-3 h-6 w-6 text-blue-500" />
          ) : (
            <FaBellSlash className="mr-3 h-6 w-6 text-gray-400" />
          )}
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Push Notifications
            </h3>
            <p className="text-sm text-gray-500">
              {isSubscribed 
                ? 'You will receive push notifications for new messages and updates'
                : 'Enable push notifications to stay updated with new messages'
              }
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {isSubscribed && (
            <button
              onClick={handleTestNotification}
              className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Test
            </button>
          )}
          
          <button
            onClick={isSubscribed ? handleUnsubscribe : handleSubscribe}
            disabled={loading || permission === 'denied'}
            className={`rounded-md px-4 py-2 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${
              isSubscribed
                ? 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
                : 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
            }`}
          >
            {loading ? (
              <div className="flex items-center">
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                {isSubscribed ? 'Disabling...' : 'Enabling...'}
              </div>
            ) : (
              <>
                {isSubscribed ? 'Disable' : 'Enable'} Notifications
              </>
            )}
          </button>
        </div>
      </div>

      {/* Permission Status */}
      <div className="mt-4">
        <div className="flex items-center text-sm">
          <span className="text-gray-500">Permission Status:</span>
          <span className={`ml-2 flex items-center ${
            permission === 'granted' 
              ? 'text-green-600' 
              : permission === 'denied' 
              ? 'text-red-600' 
              : 'text-yellow-600'
          }`}>
            {permission === 'granted' && <FaCheck className="mr-1 h-3 w-3" />}
            {permission === 'denied' && <FaTimes className="mr-1 h-3 w-3" />}
            {permission === 'default' && <FaExclamationTriangle className="mr-1 h-3 w-3" />}
            {permission.charAt(0).toUpperCase() + permission.slice(1)}
          </span>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mt-4 rounded-md bg-red-50 p-3">
          <div className="flex">
            <FaTimes className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="mt-4 rounded-md bg-green-50 p-3">
          <div className="flex">
            <FaCheck className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm text-green-800">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* Permission Denied Help */}
      {permission === 'denied' && (
        <div className="mt-4 rounded-md bg-yellow-50 p-3">
          <div className="flex">
            <FaExclamationTriangle className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <p className="text-sm text-yellow-800">
                Notifications are blocked. To enable them:
              </p>
              <ul className="mt-2 list-disc list-inside text-sm text-yellow-700">
                <li>Click the lock icon in your browser's address bar</li>
                <li>Change notifications from "Block" to "Allow"</li>
                <li>Refresh the page and try again</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PushNotificationManager;
