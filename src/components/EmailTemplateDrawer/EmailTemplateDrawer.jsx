import React, { useState, useEffect, useContext } from "react";
import Select from "react-select";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import SuccessModal from "Components/SuccessModal";
import { ntrpOptions } from "Utils/utils";
import { InteractiveButton } from "Components/InteractiveButton";

let sdk = new MkdSDK();
export const EmailTemplateDrawer = ({ isOpen, onClose, members }) => {
  if (!isOpen) return null;
  // console.log("members", members);
  const [selectedMembers, setSelectedMembers] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("schedule");
  const [isSubmitLoading, setIsSubmitLoading] = useState(false);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [formData, setFormData] = useState({
    subject: "",
    message: "",
    date: "",
    time: "",
    repeat: 0,
    type: 0,
    days: [],
    end_date: "",
    condition: {
      before: 0,
      time: 30,
      condition_type: 0,
      time_type: 1,
    },
    attachments: [],
    players: [],
  });
  const [showEndDate, setShowEndDate] = useState(false);
  const [countries, setCountries] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [selectedCity, setSelectedCity] = useState(null);
  const [availableCities, setAvailableCities] = useState([]);
  const [successModalOpen, setSuccessModalOpen] = useState(false);
  const [filters, setFilters] = useState({
    gender: "",
    ntrp: "",
    country: "",
    city: "",
  });

  // Clinic subscription states
  const [recipientType, setRecipientType] = useState("members"); // "members" or "clinic_participants"
  const [clinicSubscriptions, setClinicSubscriptions] = useState([]);
  const [availableCategories, setAvailableCategories] = useState([]);
  const [availableSubcategories, setAvailableSubcategories] = useState([]);
  const [availableTags, setAvailableTags] = useState([]);
  const [clinicFilters, setClinicFilters] = useState({
    categories: [],
    subcategories: [],
    tags: [],
    sport_id: "",
    type: "",
    sub_type: "",
  });
  const [filteredClinicParticipants, setFilteredClinicParticipants] = useState(
    []
  );
  const [loadingClinicData, setLoadingClinicData] = useState(false);

  // Update the days array to include day numbers and names
  const daysOfWeek = [
    { label: "M", number: 1, value: "monday" },
    { label: "T", number: 2, value: "tuesday" },
    { label: "W", number: 3, value: "wednesday" },
    { label: "T", number: 4, value: "thursday" },
    { label: "F", number: 5, value: "friday" },
    { label: "S", number: 6, value: "saturday" },
    { label: "S", number: 0, value: "sunday" },
  ];

  // Filter members based on search term and recipient type
  const filteredMembers = (() => {
    const sourceData =
      recipientType === "members" ? members : filteredClinicParticipants;

    return sourceData.filter((member) => {
      const matchesSearch = `${member.first_name} ${member.last_name}`
        .toLowerCase()
        .includes(searchTerm.toLowerCase());

      // Use includes() for more flexible matching
      const matchesGender =
        !filters.gender ||
        (member.gender || "")
          .toLowerCase()
          .includes(filters.gender.toLowerCase());
      const matchesNTRP = !filters.ntrp || member.ntrp === filters.ntrp;
      const matchesCountry =
        !filters.country ||
        (member.country || "")
          .toLowerCase()
          .includes(filters.country.toLowerCase());
      const matchesCity =
        !filters.city ||
        (member.city || "").toLowerCase().includes(filters.city.toLowerCase());

      return (
        matchesSearch &&
        matchesGender &&
        matchesNTRP &&
        matchesCountry &&
        matchesCity
      );
    });
  })();

  // Handle day selection
  const toggleDay = (day) => {
    setFormData((prev) => ({
      ...prev,
      days: prev.days.includes(day)
        ? prev.days.filter((d) => d !== day)
        : [...prev.days, day],
    }));
  };

  // Update players when selectedMembers changes
  useEffect(() => {
    let emails = [];

    if (recipientType === "members") {
      emails = selectedMembers
        .map((id) => members.find((member) => member.id === id)?.email || "")
        .filter((email) => email);
    } else if (recipientType === "clinic_participants") {
      emails = selectedMembers
        .map(
          (id) =>
            filteredClinicParticipants.find(
              (participant) => participant.id === id
            )?.email || ""
        )
        .filter((email) => email);
    }

    setFormData((prev) => ({
      ...prev,
      players: emails,
    }));
  }, [selectedMembers, members, recipientType, filteredClinicParticipants]);

  const handleSubmit = async () => {
    setIsSubmitLoading(true);
    try {
      const finalData = {
        ...formData,
        date: formData.date,
        repeat: parseInt(formData.repeat),
        condition: {
          // ...formData.condition,
          // before: formData.condition.time,
          before: parseInt(formData.condition.before),
          time: formData.condition.time,
          condition_type: parseInt(formData.condition.condition_type),
          time_type: parseInt(formData.condition.time_type),
        },
      };

      console.log(finalData);

      await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/club/reservations/scheduling",
        finalData,
        "POST"
      );
      showToast("success", "Email template created successfully");
      setSuccessModalOpen(true);
    } catch (error) {
      showToast(globalDispatch, error.message, 3000, "error");
    } finally {
      setIsSubmitLoading(false);
    }
  };

  // Fetch countries data
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await fetch("/countries.min.json");
        const data = await response.json();

        // Transform the data into the format needed for react-select
        const countryOptions = Object.keys(data).map((country) => ({
          value: country,
          label: country,
          cities: data[country].map((city) => ({
            value: city,
            label: city,
          })),
        }));

        setCountries(countryOptions);
      } catch (error) {
        console.error("Error loading countries:", error);
      }
    };

    fetchCountries();
  }, []);

  // Update available cities when country changes
  useEffect(() => {
    if (selectedCountry) {
      const country = countries.find((c) => c.value === selectedCountry.value);
      setAvailableCities(country?.cities || []);
    } else {
      setAvailableCities([]);
    }
  }, [selectedCountry, countries]);

  // Custom styles for react-select
  const selectStyles = {
    control: (base) => ({
      ...base,
      minHeight: 36,
      borderColor: "#e5e7eb",
      borderRadius: "0.5rem",
      "&:hover": {
        borderColor: "#e5e7eb",
      },
    }),
    valueContainer: (base) => ({
      ...base,
      padding: "0 5px",
    }),
    input: (base) => ({
      ...base,
      margin: 0,
      padding: 0,
    }),
  };

  // Fetch clinic subscriptions with user and clinic data
  const fetchClinicSubscriptions = async () => {
    setLoadingClinicData(true);
    try {
      sdk.setTable("clinic_subscription");
      const result = await sdk.callRestAPI(
        {
          join: [`user|user_id`, `clinics|clinic_id`],
          // filter: [`status,eq,1`], // Only active subscriptions
        },
        "GETALL"
      );

      if (result && result.list) {
        setClinicSubscriptions(result.list);
      }
    } catch (error) {
      console.error("Error fetching clinic subscriptions:", error);
      showToast(globalDispatch, "Failed to load clinic data", 3000, "error");
    } finally {
      setLoadingClinicData(false);
    }
  };

  // Fetch categories, subcategories, and tags
  const fetchCategoriesAndTags = async () => {
    try {
      // Get club_id from members or context
      const club_id = members?.[0]?.club_id;
      if (!club_id) return;

      // Fetch categories
      sdk.setTable("clinic_categories");
      const categoriesResponse = await sdk.callRestAPI(
        {
          filter: [`club_id,eq,${club_id}`],
        },
        "GETALL"
      );

      // Fetch subcategories
      sdk.setTable("clinic_subcategories");
      const subcategoriesResponse = await sdk.callRestAPI(
        {
          filter: [`club_id,eq,${club_id}`],
        },
        "GETALL"
      );

      // Fetch tags
      sdk.setTable("clinic_tags");
      const tagsResponse = await sdk.callRestAPI(
        {
          filter: [`club_id,eq,${club_id}`],
        },
        "GETALL"
      );

      setAvailableCategories(categoriesResponse.list || []);
      setAvailableSubcategories(subcategoriesResponse.list || []);
      setAvailableTags(tagsResponse.list || []);
    } catch (error) {
      console.error("Error fetching categories and tags:", error);
      // If tables don't exist, we'll just have empty arrays
      setAvailableCategories([]);
      setAvailableSubcategories([]);
      setAvailableTags([]);
    }
  };

  // Filter clinic participants based on selected criteria
  const filterClinicParticipants = () => {
    if (!clinicSubscriptions.length) {
      setFilteredClinicParticipants([]);
      return;
    }

    let filtered = clinicSubscriptions.filter((subscription) => {
      const clinic = subscription.clinics;
      const user = subscription.user;

      if (!clinic || !user) return false;

      // Filter by categories
      if (clinicFilters.categories.length > 0) {
        const clinicCategories = clinic.categories
          ? JSON.parse(clinic.categories)
          : [];
        const hasMatchingCategory = clinicFilters.categories.some((filterCat) =>
          clinicCategories.some((clinicCat) => clinicCat.id === filterCat.id)
        );
        if (!hasMatchingCategory) return false;
      }

      // Filter by subcategories
      if (clinicFilters.subcategories.length > 0) {
        const clinicSubcategories = clinic.subcategories
          ? JSON.parse(clinic.subcategories)
          : [];
        const hasMatchingSubcategory = clinicFilters.subcategories.some(
          (filterSubcat) =>
            clinicSubcategories.some(
              (clinicSubcat) => clinicSubcat.id === filterSubcat.id
            )
        );
        if (!hasMatchingSubcategory) return false;
      }

      // Filter by tags
      if (clinicFilters.tags.length > 0) {
        const clinicTags = clinic.tags ? JSON.parse(clinic.tags) : [];
        const hasMatchingTag = clinicFilters.tags.some((filterTag) =>
          clinicTags.some((clinicTag) => clinicTag.id === filterTag.id)
        );
        if (!hasMatchingTag) return false;
      }

      // Filter by sport
      if (
        clinicFilters.sport_id &&
        clinic.sport_id !== parseInt(clinicFilters.sport_id)
      ) {
        return false;
      }

      // Filter by type
      if (clinicFilters.type && clinic.type !== clinicFilters.type) {
        return false;
      }

      // Filter by sub_type
      if (
        clinicFilters.sub_type &&
        clinic.sub_type !== clinicFilters.sub_type
      ) {
        return false;
      }

      return true;
    });

    // Remove duplicates by user_id and extract unique users
    const uniqueUsers = [];
    const seenUserIds = new Set();

    filtered.forEach((subscription) => {
      if (!seenUserIds.has(subscription.user.id)) {
        seenUserIds.add(subscription.user.id);
        uniqueUsers.push(subscription.user);
      }
    });

    setFilteredClinicParticipants(uniqueUsers);
  };

  useEffect(() => {
    if (members?.length > 0) {
      fetchClinicSubscriptions();
      fetchCategoriesAndTags();
    }
  }, [members]);

  useEffect(() => {
    filterClinicParticipants();
  }, [clinicSubscriptions, clinicFilters]);
  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div
        className="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        onClick={onClose}
      />

      <div className="fixed inset-x-0 bottom-0 z-10">
        <div className="flex  min-h-full items-end justify-center text-center sm:items-center ">
          <div className="relative h-full max-h-[97vh] w-full transform overflow-hidden rounded-t-2xl bg-white text-left shadow-xl transition-all">
            <div className="flex items-center justify-between border-b border-gray-200 px-8 py-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Create email template
              </h3>
              <button
                onClick={onClose}
                className="rounded-full p-2 hover:bg-gray-100"
              >
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path
                    d="M15 5L5 15M5 5L15 15"
                    stroke="currentColor"
                    strokeWidth="1.67"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
            </div>

            <div className="grid max-h-[calc(90vh-80px)] grid-cols-1 gap-8 overflow-y-auto bg-gray-100 p-8 md:grid-cols-2">
              {/* first column */}
              <div className="space-y-6 rounded-xl bg-white">
                <div className="border-b p-4">
                  <h4 className="font-medium">Settings</h4>
                </div>

                <div className="space-y-4 p-4">
                  <div className="flex gap-4 border-b border-gray-100 pb-4">
                    <button
                      onClick={() => setActiveTab("schedule")}
                      className={`font-medium ${
                        activeTab === "schedule"
                          ? "text-[#1D275F]"
                          : "text-gray-500"
                      }`}
                    >
                      Schedule
                    </button>
                    <button
                      onClick={() => setActiveTab("condition")}
                      className={`font-medium ${
                        activeTab === "condition"
                          ? "text-[#1D275F]"
                          : "text-gray-500 "
                      }`}
                    >
                      Condition
                    </button>
                  </div>
                  {activeTab === "schedule" && (
                    <div className="space-y-4 rounded-lg bg-gray-50 p-4">
                      <div className="space-y-2">
                        <label className="text-sm text-gray-600">
                          Date/time
                        </label>
                        <div className="flex gap-4">
                          <input
                            type="date"
                            value={formData.date}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                date: e.target.value,
                              }))
                            }
                            className="rounded-lg border border-gray-200 px-3 py-2 text-sm"
                          />
                          <input
                            type="time"
                            value={formData.time}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                time: e.target.value,
                              }))
                            }
                            className="rounded-lg border border-gray-200 px-3 py-2 text-sm"
                          />
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <label className="relative inline-flex cursor-pointer items-center">
                          <input
                            type="checkbox"
                            checked={formData.repeat === 1}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                repeat: e.target.checked ? 1 : 0,
                              }))
                            }
                            className="peer sr-only"
                          />
                          <div className="h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-[#1D275F] peer-checked:after:translate-x-full peer-checked:after:border-white"></div>
                        </label>
                        <span className="text-sm text-gray-600">Repeat</span>
                      </div>

                      <div className="flex items-center gap-3">
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.75 1.5L14.25 4.75L10.75 8M13.25 16L9.75 19.25L13.25 22.5M10.75 19.25H14C18.0041 19.25 21.25 16.0041 21.25 12C21.25 9.81504 20.2834 7.85583 18.7546 6.52661M13.25 4.75H10C5.99593 4.75 2.75 7.99594 2.75 12C2.75 14.1854 3.71696 16.145 5.24638 17.4742"
                            stroke="#525866"
                            stroke-width="1.5"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>

                        <span className="text-sm text-gray-600">
                          Repeat every
                        </span>
                        <select className="rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm">
                          <option>1</option>
                          <option>2</option>
                          <option>3</option>
                        </select>
                        <select className="rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm">
                          <option>week</option>
                          <option>month</option>
                        </select>
                      </div>

                      <div className="flex gap-2">
                        {daysOfWeek.map((day) => (
                          <button
                            key={day.value}
                            onClick={() => toggleDay(day.value)}
                            className={`h-8 w-8 rounded-full border border-gray-200 ${
                              formData.days.includes(day.value)
                                ? "bg-[#1D275F] text-white"
                                : "text-gray-500"
                            }`}
                          >
                            {day.label}
                          </button>
                        ))}
                      </div>

                      {!showEndDate ? (
                        <button
                          onClick={() => setShowEndDate(true)}
                          className="inline-flex items-center gap-2 text-sm text-[#1D275F] underline"
                        >
                          <span>+</span>
                          Add end date
                        </button>
                      ) : (
                        <div className="space-y-2">
                          <label className="text-sm text-gray-600">
                            End date
                          </label>
                          <input
                            type="date"
                            value={formData.end_date}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                end_date: e.target.value,
                              }))
                            }
                            className="block rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm"
                          />
                          <button
                            onClick={() => {
                              setShowEndDate(false);
                              setFormData((prev) => ({
                                ...prev,
                                end_date: "",
                              }));
                            }}
                            className="text-sm text-red-500"
                          >
                            Remove end date
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                  {activeTab === "condition" && (
                    <div className="space-y-4 rounded-lg bg-gray-50 p-4">
                      <div className="space-y-4">
                        <div className="flex items-center gap-2">
                          <select
                            value={formData.condition.time}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                condition: {
                                  ...prev.condition,
                                  time: parseInt(e.target.value),
                                },
                              }))
                            }
                            className="w-16 rounded-lg border border-gray-200 px-3 py-2 text-sm"
                          >
                            {[...Array(60)].map((_, index) => (
                              <option key={index} value={index + 1}>
                                {index + 1}
                              </option>
                            ))}
                          </select>
                          <select
                            value={formData.condition.time_type}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                condition: {
                                  ...prev.condition,
                                  time_type: parseInt(e.target.value),
                                },
                              }))
                            }
                            className="w-fit rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm"
                          >
                            <option value={0}>minutes</option>
                            <option value={1}>hours</option>
                            <option value={2}>days</option>
                            <option value={3}>weeks</option>
                          </select>
                          <select
                            className="w-fit rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm"
                            value={formData.condition.before}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                condition: {
                                  ...prev.condition,
                                  before: parseInt(e.target.value),
                                },
                              }))
                            }
                          >
                            <option value={0}>before</option>
                            <option value={1}>after</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm text-gray-600">
                            Reservation type
                          </label>
                          <select
                            value={formData.condition.condition_type}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                condition: {
                                  ...prev.condition,
                                  condition_type: parseInt(e.target.value),
                                },
                              }))
                            }
                            className="block w-full rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm"
                          >
                            <option value={0}>Reservation</option>
                            <option value={1}>Clinic</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4 p-4">
                  <div className="flex justify-between">
                    <p className="text-lg font-medium">Recipients</p>
                    <button
                      onClick={() => {
                        const sourceData =
                          recipientType === "members"
                            ? members
                            : filteredClinicParticipants;
                        setSelectedMembers(
                          sourceData.map((member) => member.id)
                        );
                      }}
                      className="text-sm text-blue-500"
                    >
                      Send to all
                    </button>
                  </div>

                  {/* Recipient Type Tabs */}
                  <div className="flex gap-4 border-b border-gray-100 pb-4">
                    <button
                      onClick={() => {
                        setRecipientType("members");
                        setSelectedMembers([]);
                      }}
                      className={`font-medium ${
                        recipientType === "members"
                          ? "text-[#1D275F]"
                          : "text-gray-500"
                      }`}
                    >
                      All Members
                    </button>
                    <button
                      onClick={() => {
                        setRecipientType("clinic_participants");
                        setSelectedMembers([]);
                      }}
                      className={`font-medium ${
                        recipientType === "clinic_participants"
                          ? "text-[#1D275F]"
                          : "text-gray-500"
                      }`}
                    >
                      Clinic Participants
                    </button>
                  </div>
                  <div className="flex w-full items-center gap-2 rounded-lg border border-gray-200 px-3 ">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z"
                        fill="#525866"
                      />
                    </svg>

                    <input
                      type="text"
                      placeholder="search by name"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="h-full w-full border-none bg-transparent py-2 text-sm outline-none focus:border-none focus:!outline-none"
                    />
                  </div>

                  {/* Clinic Filtering Options - Only show when clinic participants tab is selected */}
                  {recipientType === "clinic_participants" && (
                    <div className="space-y-3 rounded-lg bg-gray-50 p-4">
                      <p className="text-sm font-medium text-gray-700">
                        Filter by Clinic Criteria
                      </p>

                      {loadingClinicData && (
                        <div className="text-center text-sm text-gray-500">
                          Loading clinic data...
                        </div>
                      )}

                      {!loadingClinicData && (
                        <>
                          {/* Categories */}
                          {availableCategories.length > 0 && (
                            <div>
                              <label className="text-xs text-gray-600">
                                Categories
                              </label>
                              <Select
                                isMulti
                                value={clinicFilters.categories}
                                onChange={(selected) =>
                                  setClinicFilters((prev) => ({
                                    ...prev,
                                    categories: selected || [],
                                  }))
                                }
                                options={availableCategories.map((cat) => ({
                                  value: cat.id,
                                  label: cat.name,
                                  id: cat.id,
                                  name: cat.name,
                                }))}
                                placeholder="Select categories..."
                                styles={selectStyles}
                                className="text-sm"
                              />
                            </div>
                          )}

                          {/* Subcategories */}
                          {availableSubcategories.length > 0 && (
                            <div>
                              <label className="text-xs text-gray-600">
                                Subcategories
                              </label>
                              <Select
                                isMulti
                                value={clinicFilters.subcategories}
                                onChange={(selected) =>
                                  setClinicFilters((prev) => ({
                                    ...prev,
                                    subcategories: selected || [],
                                  }))
                                }
                                options={availableSubcategories.map(
                                  (subcat) => ({
                                    value: subcat.id,
                                    label: subcat.name,
                                    id: subcat.id,
                                    name: subcat.name,
                                    category_id: subcat.category_id,
                                  })
                                )}
                                placeholder="Select subcategories..."
                                styles={selectStyles}
                                className="text-sm"
                              />
                            </div>
                          )}

                          {/* Tags */}
                          {availableTags.length > 0 && (
                            <div>
                              <label className="text-xs text-gray-600">
                                Tags
                              </label>
                              <Select
                                isMulti
                                value={clinicFilters.tags}
                                onChange={(selected) =>
                                  setClinicFilters((prev) => ({
                                    ...prev,
                                    tags: selected || [],
                                  }))
                                }
                                options={availableTags.map((tag) => ({
                                  value: tag.id,
                                  label: tag.name,
                                  id: tag.id,
                                  name: tag.name,
                                }))}
                                placeholder="Select tags..."
                                styles={selectStyles}
                                className="text-sm"
                              />
                            </div>
                          )}

                          {/* Clear clinic filters button */}
                          {(clinicFilters.categories.length > 0 ||
                            clinicFilters.subcategories.length > 0 ||
                            clinicFilters.tags.length > 0) && (
                            <button
                              onClick={() => {
                                setClinicFilters({
                                  categories: [],
                                  subcategories: [],
                                  tags: [],
                                  sport_id: "",
                                  type: "",
                                  sub_type: "",
                                });
                              }}
                              className="text-sm text-blue-500"
                            >
                              Clear clinic filters
                            </button>
                          )}
                        </>
                      )}
                    </div>
                  )}

                  <div className="flex w-full justify-between gap-2">
                    <div className="flex-1">
                      <Select
                        value={selectedCountry}
                        onChange={(option) => {
                          setSelectedCountry(option);
                          setSelectedCity(null);
                          setFilters((prev) => ({
                            ...prev,
                            country: option?.value || "",
                            city: "",
                          }));
                        }}
                        options={countries}
                        placeholder="Country"
                        styles={selectStyles}
                        className="text-sm"
                      />
                    </div>
                    <div className="flex-1">
                      <Select
                        value={selectedCity}
                        onChange={(option) => {
                          setSelectedCity(option);
                          setFilters((prev) => ({
                            ...prev,
                            city: option?.value || "",
                          }));
                        }}
                        options={availableCities}
                        placeholder="City"
                        isDisabled={!selectedCountry}
                        styles={selectStyles}
                        className="text-sm"
                      />
                    </div>

                    <select
                      className="flex-1 rounded-lg border border-gray-200 px-3 py-2 text-sm"
                      value={filters.gender}
                      onChange={(e) =>
                        setFilters((prev) => ({
                          ...prev,
                          gender: e.target.value,
                        }))
                      }
                    >
                      <option value="">Gender</option>
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                    </select>

                    <select
                      className="flex-1 rounded-lg border border-gray-200 px-3 py-2 text-sm"
                      value={filters.ntrp}
                      onChange={(e) =>
                        setFilters((prev) => ({
                          ...prev,
                          ntrp: e.target.value,
                        }))
                      }
                    >
                      <option value="">NTRP</option>
                      {ntrpOptions.map((option) => (
                        <option key={option} value={option.toString()}>
                          {option}
                        </option>
                      ))}
                    </select>
                  </div>

                  {(filters.gender ||
                    filters.ntrp ||
                    filters.country ||
                    filters.city) && (
                    <button
                      onClick={() => {
                        setFilters({
                          gender: "",
                          ntrp: "",
                          country: "",
                          city: "",
                        });
                        setSelectedCountry(null);
                        setSelectedCity(null);
                      }}
                      className="mt-2 text-sm text-blue-500"
                    >
                      Clear filters
                    </button>
                  )}

                  <div className="max-h-[300px] space-y-3 overflow-y-auto rounded-lg bg-gray-50 p-4">
                    {filteredMembers.map((recipient, index) => (
                      <label
                        key={index}
                        className="flex items-center gap-3 rounded-lg p-2 hover:bg-gray-50"
                      >
                        <input
                          type="checkbox"
                          checked={selectedMembers.includes(recipient.id)}
                          onChange={() =>
                            setSelectedMembers((prev) =>
                              prev.includes(recipient.id)
                                ? prev.filter((id) => id !== recipient.id)
                                : [...prev, recipient.id]
                            )
                          }
                          className="h-4 w-4 rounded border-gray-300 text-[#1D275F] focus:ring-[#1D275F]"
                        />
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 overflow-hidden rounded-full bg-gray-100">
                            <img
                              src={recipient?.photo || "/default-avatar.png"}
                              alt={recipient.first_name}
                              className="h-full w-full object-cover"
                            />
                          </div>
                          <span className="text-sm text-gray-700">
                            {recipient.first_name} {recipient.last_name}
                          </span>
                        </div>
                      </label>
                    ))}
                    {filteredMembers.length === 0 && (
                      <p className="text-center text-sm text-gray-500">
                        No recipients found
                      </p>
                    )}
                  </div>
                </div>
                <div className="space-y-4 p-4">
                  <p className="text-lg font-medium">Attachment</p>
                  <div className="rounded-lg border border-dashed border-gray-200 p-4 text-center">
                    <p className="text-sm text-gray-500">
                      Choose a file or drag & drop it here.
                    </p>
                    <p className="text-xs text-gray-400">
                      JPEG, PNG, PDF, and MP4 formats, up to 50 MB.
                    </p>
                    <button className="mt-2 rounded-lg border border-gray-200 px-4 py-2 text-sm">
                      Browse File
                    </button>
                  </div>
                </div>
              </div>
              {/* second column */}
              <div className="h-fit space-y-6 rounded-xl bg-white">
                <div className="border-b p-4">
                  <h4 className="font-medium">Email</h4>
                </div>
                <div className="p-4">
                  <div className="space-y-4 p-4">
                    <input
                      type="text"
                      placeholder="Subject"
                      value={formData.subject}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          subject: e.target.value,
                        }))
                      }
                      className="w-full rounded-lg border border-gray-200 px-3 py-2 text-sm"
                    />
                    <textarea
                      placeholder="Message"
                      value={formData.message}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          message: e.target.value,
                        }))
                      }
                      className="h-[200px] w-full rounded-lg border border-gray-200 px-3 py-2 text-sm"
                    />
                  </div>
                  <InteractiveButton
                    onClick={handleSubmit}
                    loading={isSubmitLoading}
                    className="mx-4 mt-2 w-fit rounded-lg bg-[#1D275F] px-4 py-2 text-sm text-white"
                  >
                    Send
                  </InteractiveButton>
                </div>
              </div>
            </div>

            {/* <div className="flex justify-end gap-4 border-t border-gray-200 px-8 py-4">
              <button
                onClick={onClose}
                className="rounded-xl border border-gray-200 px-6 py-2 text-sm font-semibold text-gray-500 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button className="rounded-xl bg-[#1D275F] px-6 py-2 text-sm font-semibold text-white hover:bg-blue-700">
                Send
              </button>
            </div> */}
          </div>
        </div>
      </div>
      {successModalOpen && (
        <SuccessModal
          onContinue={() => {
            setSuccessModalOpen(false);
            onClose();
          }}
          description={
            "Email successfully sent to " +
            selectedMembers.length +
            " recipients"
          }
          title="Email sent!"
        />
      )}
    </div>
  );
};
