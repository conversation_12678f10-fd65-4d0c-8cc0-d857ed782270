import React from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { BiSearch } from "react-icons/bi";
import { PaginationBar } from "Components/PaginationBar";
import SkeletonLoader from "Components/Skeleton/Skeleton";
import { EmailTemplateDrawer } from "Components/EmailTemplateDrawer/EmailTemplateDrawer";
import DeleteModal from "Components/Modals/DeleteModal";
import HistoryComponent from "Components/HistoryComponent";
import DataTable from "Components/Shared/DataTable";
import EmailTemplateModal from "Components/Shared/EmailTemplateModal";

let sdk = new MkdSDK();
const columns = [
  {
    header: "Subject",
    accessor: "subject",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "",
    accessor: "actions",
  },
];

const ListEmail = ({ club }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [isEmailDrawerOpen, setIsEmailDrawerOpen] = React.useState(false);
  const [selectedEmailId, setSelectedEmailId] = React.useState(null);
  const [members, setMembers] = React.useState([]);
  const [isEmailModalOpen, setIsEmailModalOpen] = React.useState(false);
  const [editingEmail, setEditingEmail] = React.useState(null);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }
  async function getData(pageNum, limitNum, data = {}, filters = []) {
    setLoading(true);
    try {
      sdk.setTable("email");
      const result = await sdk.callRestAPI(
        {
          payload: {
            ...data,
          },
          page: pageNum,
          limit: limitNum,
          filter: [...filters, `club_id,eq,${club?.id}`],
        },
        "PAGINATE"
      );

      if (result) {
        setLoading(false);
      }
      const { list, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const fetchMembers = async () => {
    // setUsersLoading(true);
    try {
      sdk.setTable("user");
      const memberResponse = await sdk.callRestAPI(
        {
          filter: ["role,cs,user", `club_id,cs,${club?.id}`],
        },
        "GETALL"
      );
      sdk.setTable("profile");
      const profileResponse = await sdk.callRestAPI({}, "GETALL");

      const mergedMembers = memberResponse.list.map((member) => {
        const profile = profileResponse.list.find(
          (p) => p.user_id === member.id
        );
        return {
          ...member,
          ...profile,
        };
      });
      // console.log("mergedMembers", mergedMembers);
      setMembers(mergedMembers);
    } catch (error) {
      console.error("Error fetching users:", error);
      return [];
    } finally {
      // setUsersLoading(false);
    }
  };

  const handleEmailSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (editingEmail?.id) {
        // Update existing template
        sdk.setTable("email");
        const result = await sdk.callRestAPI(
          {
            subject: editingEmail.subject,
            html: editingEmail.html,
            id: editingEmail.id,
          },
          "PUT"
        );

        if (!result.error) {
          showToast(
            globalDispatch,
            "Email template updated successfully!",
            3000,
            "success"
          );

          setCurrentTableData(
            data.map((item) =>
              item.id === editingEmail.id ? { ...item, ...editingEmail } : item
            )
          );
        }
      } else {
        // Create new template
        sdk.setTable("email");
        const result = await sdk.callRestAPI(
          {
            subject: editingEmail.subject,
            html: editingEmail.html,
            club_id: club.id,
          },
          "POST"
        );

        if (!result.error) {
          showToast(
            globalDispatch,
            "Email template created successfully!",
            3000,
            "success"
          );
          setCurrentTableData([{ ...editingEmail, id: result.data }, ...data]);
        }
      }
      setIsEmailModalOpen(false);
      setEditingEmail(null);
    } catch (error) {
      console.error(error);
      showToast(globalDispatch, "Error creating email template", 3000, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (id) => {
    setIsDeleting(true);
    try {
      sdk.setTable("email");
      const result = await sdk.callRestAPI({ id }, "DELETE");
      if (!result.error) {
        showToast(
          globalDispatch,
          "Email template deleted successfully!",
          3000,
          "success"
        );
        setCurrentTableData(data.filter((item) => item.id !== id));
      }
    } catch (error) {
      console.error(error);
      showToast(
        globalDispatch,
        "An error occurred while deleting the template.",
        3000,
        "error"
      );
    } finally {
      setIsDeleting(false);
      setShowDeleteModal(false);
      setSelectedEmailId(null);
      setIsDeleteModalOpen(false);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "email",
      },
    });
    fetchMembers();
    const delay = 700;
    const timeoutId = setTimeout(async () => {
      await getData(1, pageSize);
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  return (
    <div className="h-screen px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col justify-between gap-4 py-3 sm:gap-6 md:flex-row md:items-center">
        <div className="relative flex w-full max-w-none flex-1 items-center justify-between sm:max-w-[300px] md:max-w-[350px] lg:max-w-[400px]">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <BiSearch className="h-4 w-4 text-gray-500 sm:h-5 sm:w-5" />
          </div>
          <input
            type="text"
            className="block w-full rounded-xl border border-gray-200 py-2 pl-10 pr-4 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:py-2.5 sm:pl-11"
            placeholder="Search email subject"
            onChange={(e) => {
              if (e.target.value) {
                getData(0, pageSize, {}, [`subject,cs,${e.target.value}`]);
              } else {
                getData(0, pageSize);
              }
            }}
          />
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4">
          <button
            onClick={() => setIsEmailDrawerOpen(true)}
            className="inline-flex items-center justify-center gap-2 rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 sm:px-4"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
            >
              <path
                d="M4.99919 10.0017H7.70752M4.99919 10.0017L2.81729 3.45596C2.6999 3.1038 3.06689 2.78551 3.39891 2.95152L16.7538 9.62898C17.0609 9.78253 17.0609 10.2208 16.7538 10.3743L3.39891 17.0518C3.06689 17.2178 2.6999 16.8995 2.81729 16.5474L4.99919 10.0017Z"
                stroke="#868C98"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span className="hidden sm:inline">Send custom email</span>
            <span className="sm:hidden">Send email</span>
          </button>

          <div className="hidden sm:block">
            <HistoryComponent
              title="Email History"
              emptyMessage="No email history found"
            />
          </div>

          <button
            onClick={() => {
              setEditingEmail(null);
              setIsEmailModalOpen(true);
            }}
            className="inline-flex items-center justify-center gap-2 rounded-xl bg-[#1D275F] px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 sm:px-4"
          >
            <span className="text-sm">+</span>
            <span className="hidden sm:inline">Add new</span>
            <span className="sm:hidden">Add</span>
          </button>

          {/* Mobile History Button */}
          <div className="sm:hidden">
            <HistoryComponent
              title="Email History"
              emptyMessage="No email history found"
            />
          </div>
        </div>
      </div>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <DataTable
          columns={columns}
          data={data}
          loading={loading}
          rowClassName="hover:bg-gray-50 bg-gray-100 px-4 py-3 cursor-pointer"
          onClick={(row) => {
            setEditingEmail(row);
            setIsEmailModalOpen(true);
          }}
          renderCustomCell={{
            actions: (row) => (
              <div className="flex justify-end">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedEmailId(row.id);
                    setIsDeleteModalOpen(true);
                  }}
                  className="rounded-lg p-2 text-red-600 transition-colors duration-150 hover:bg-red-50 hover:text-red-800"
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 sm:h-5 sm:w-5"
                  >
                    <path
                      d="M6.79167 6.79297V18.8763C6.79167 19.3365 7.16476 19.7096 7.625 19.7096H16.375C16.8352 19.7096 17.2083 19.3365 17.2083 18.8763V6.79297M6.79167 6.79297H17.2083M6.79167 6.79297H5.125M17.2083 6.79297H18.875M13.6667 10.9596V15.543M10.3333 10.9596V15.543M9.5 6.79297C9.5 5.41226 10.6193 4.29297 12 4.29297C13.3807 4.29297 14.5 5.41226 14.5 6.79297"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </div>
            ),
          }}
          emptyMessage="No email templates available"
          loadingMessage="Loading email templates..."
        />
      )}
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={(newPageSize) => {
          setPageSize(newPageSize);
          getData(1, newPageSize);
        }}
        previousPage={previousPage}
        nextPage={nextPage}
        gotoPage={(pageNum) => getData(pageNum, pageSize)}
      />
      {/* <ModalSidebar
            isModalActive={showAddSidebar}
            closeModalFn={() => setShowAddSidebar(false)}
          >
            <AddAdminEmailPage setSidebar={setShowAddSidebar} getData={getData} />
          </ModalSidebar> */}
      {/* {showEditSidebar && (
            <ModalSidebar
              isModalActive={showEditSidebar}
              closeModalFn={() => setShowEditSidebar(false)}
            >
              <EditAdminEmailPage
                activeId={activeEditId}
                setSidebar={setShowEditSidebar}
              />
            </ModalSidebar>
          )} */}
      <EmailTemplateDrawer
        isOpen={isEmailDrawerOpen}
        onClose={() => setIsEmailDrawerOpen(false)}
        members={members}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedEmailId(null);
        }}
        onDelete={() => handleDelete(selectedEmailId)}
        loading={isDeleting}
      />
      <EmailTemplateModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        editingEmail={editingEmail}
        setEditingEmail={setEditingEmail}
        onSubmit={handleEmailSubmit}
        isSubmitting={isSubmitting}
      />
    </div>
  );
};

export default ListEmail;
