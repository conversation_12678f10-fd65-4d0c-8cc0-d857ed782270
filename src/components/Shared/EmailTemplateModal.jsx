import React, { useState } from "react";
import RightSideModal from "Components/RightSideModal";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

const EmailTemplateModal = ({
  isOpen,
  onClose,
  editingEmail,
  setEditingEmail,
  onSubmit,
  isSubmitting,
}) => {
  const [activeTab, setActiveTab] = useState("edit");

  // ReactQuill modules configuration for email editing
  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ color: [] }, { background: [] }],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ align: [] }],
      ["link"],
      ["clean"],
    ],
  };

  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "color",
    "background",
    "list",
    "bullet",
    "align",
    "link",
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(e);
  };

  return (
    <RightSideModal
      isOpen={isOpen}
      onClose={onClose}
      title={editingEmail ? "Edit Email Template" : "Add New Email Template"}
      primaryButtonText={editingEmail ? "Update Template" : "Create Template"}
      onPrimaryAction={handleSubmit}
      submitting={isSubmitting}
    >
      <div className="flex h-full flex-col">
        {/* Tab Navigation */}
        <div className="mb-4 flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab("edit")}
            className={`border-b-2 px-4 py-2 text-sm font-medium transition-colors ${
              activeTab === "edit"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            Rich Editor
          </button>
          <button
            onClick={() => setActiveTab("html")}
            className={`border-b-2 px-4 py-2 text-sm font-medium transition-colors ${
              activeTab === "html"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            HTML
          </button>
          <button
            onClick={() => setActiveTab("preview")}
            className={`border-b-2 px-4 py-2 text-sm font-medium transition-colors ${
              activeTab === "preview"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            Preview
          </button>
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-hidden">
          {activeTab === "edit" ? (
            <form
              onSubmit={handleSubmit}
              className="flex h-full flex-col space-y-4 py-4 sm:space-y-5"
            >
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Subject
                </label>
                <input
                  type="text"
                  name="subject"
                  value={editingEmail?.subject || ""}
                  onChange={(e) =>
                    setEditingEmail({
                      ...editingEmail,
                      subject: e.target.value,
                    })
                  }
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  required
                />
              </div>
              <div className="flex flex-1 flex-col">
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Message
                </label>
                <div className="flex flex-1 flex-col">
                  <ReactQuill
                    theme="snow"
                    value={editingEmail?.html || ""}
                    onChange={(content) =>
                      setEditingEmail({ ...editingEmail, html: content })
                    }
                    modules={modules}
                    formats={formats}
                    placeholder="Enter your email content here. Use the toolbar above for formatting."
                    style={{
                      height: "300px",
                      display: "flex",
                      flexDirection: "column",
                      border: "1px solid #d1d5db",
                      borderRadius: "6px",
                    }}
                    className="flex-1"
                  />
                </div>
              </div>
              <div className="mt-4 text-xs text-gray-500">
                <p>
                  💡 Tip: Use the formatting toolbar above to style your email
                  content with bold, italic, colors, lists, and more!
                </p>
              </div>
            </form>
          ) : activeTab === "html" ? (
            <form
              onSubmit={handleSubmit}
              className="flex h-full flex-col space-y-4 py-4 sm:space-y-5"
            >
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Subject
                </label>
                <input
                  type="text"
                  name="subject"
                  value={editingEmail?.subject || ""}
                  onChange={(e) =>
                    setEditingEmail({
                      ...editingEmail,
                      subject: e.target.value,
                    })
                  }
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  required
                />
              </div>
              <div className="flex flex-1 flex-col">
                <div className="mb-2 flex items-center justify-between">
                  <label className="block text-sm font-medium text-gray-700">
                    HTML Content
                  </label>
                  <div className="flex items-center space-x-2 text-xs text-gray-500">
                    <span>💻 Code Editor</span>
                  </div>
                </div>
                <div className="relative flex-1">
                  <textarea
                    name="html"
                    value={editingEmail?.html || ""}
                    onChange={(e) =>
                      setEditingEmail({ ...editingEmail, html: e.target.value })
                    }
                    className="block h-full w-full resize-none rounded-md border border-gray-300 bg-gray-50 px-3 py-2 font-mono text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder={`Enter your HTML content here...

Example:
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h1 style="color: #333; text-align: center;">Welcome!</h1>
  <p style="color: #666; line-height: 1.6;">Your message content here...</p>
  <div style="text-align: center; margin: 20px 0;">
    <a href="#" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Call to Action</a>
  </div>
</div>`}
                    required
                    style={{
                      minHeight: "300px",
                      lineHeight: "1.5",
                      tabSize: "2",
                    }}
                  />
                </div>
              </div>
              <div className="mt-4 text-xs text-gray-500">
                <p>
                  💡 Tip: You can write custom HTML and CSS here for complete
                  control over styling. Use inline styles for best email client
                  compatibility.
                </p>
                <p className="mt-1">
                  Example: &lt;p style="color: #333; font-size: 16px;"&gt;Your
                  content&lt;/p&gt;
                </p>
              </div>
            </form>
          ) : (
            <div className="flex h-full flex-col">
              <div className="mb-4">
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Email Preview
                </h3>
                <div className="rounded-md border bg-gray-50 p-3">
                  <div className="mb-1 text-sm text-gray-600">Subject:</div>
                  <div className="font-medium text-gray-900">
                    {editingEmail?.subject || "No subject"}
                  </div>
                </div>
              </div>
              <div className="flex flex-1 flex-col">
                <div className="mb-2 text-sm text-gray-600">Message:</div>
                <div className="flex-1 overflow-auto rounded-md border bg-white">
                  {editingEmail?.html ? (
                    <div
                      className="prose prose-sm max-w-none p-4"
                      dangerouslySetInnerHTML={{ __html: editingEmail.html }}
                    />
                  ) : (
                    <div className="p-4 italic text-gray-500">
                      No message content to preview
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </RightSideModal>
  );
};

export default EmailTemplateModal;
