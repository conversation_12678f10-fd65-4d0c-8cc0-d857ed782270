import React from 'react';
import PushNotificationManager from 'Components/PushNotificationManager';
import { FaCog } from 'react-icons/fa';

const NotificationSettings = ({ className = '' }) => {
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center">
        <FaCog className="mr-3 h-6 w-6 text-gray-500" />
        <h2 className="text-2xl font-bold text-gray-900">Notification Settings</h2>
      </div>
      
      <div className="space-y-4">
        <p className="text-gray-600">
          Manage your notification preferences to stay updated with important messages and updates.
        </p>
        
        <PushNotificationManager />
        
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Notification Types
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">New Messages</h4>
                <p className="text-sm text-gray-500">Get notified when you receive new chat messages</p>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  defaultChecked
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">System Updates</h4>
                <p className="text-sm text-gray-500">Get notified about system maintenance and updates</p>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  defaultChecked
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Booking Reminders</h4>
                <p className="text-sm text-gray-500">Get reminded about upcoming bookings and reservations</p>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  defaultChecked
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
            </div>
          </div>
        </div>
        
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                About Push Notifications
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  Push notifications work even when the app is closed or minimized. 
                  You can control which notifications you receive and disable them at any time.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;
