export default function FindBuddyPerspective() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="rounded-lg bg-orange-50 p-4">
        <h3 className="mb-2 text-lg font-semibold text-orange-900">
          User Find a Buddy Experience
        </h3>
        <p className="text-orange-800">
          This shows how users experience the find-a-buddy feature to connect
          with playing partners.
        </p>
      </div>

      {/* Main Interface */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h4 className="mb-4 text-xl font-semibold sm:mb-0">Find a buddy</h4>
          <div className="flex flex-wrap items-center gap-2">
            <button className="rounded-xl border bg-green-900 px-4 py-2 text-sm text-white">
              Subscriptions
            </button>
            <button className="flex items-center rounded-xl border bg-blue-600 px-4 py-2 text-sm text-white">
              <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
                className="mr-2"
              >
                <path
                  d="M5.25 5.25V0.75H6.75V5.25H11.25V6.75H6.75V11.25H5.25V6.75H0.75V5.25H5.25Z"
                  fill="white"
                />
              </svg>
              Create a request
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          {/* Request Type Tabs */}
          <div className="mb-3 flex sm:mb-0">
            <button className="flex items-center gap-2 whitespace-nowrap border-b-2 border-blue-600 bg-transparent px-3 py-3">
              <span>All requests</span>
            </button>
            <button className="flex items-center gap-2 whitespace-nowrap bg-transparent px-3 py-3 text-gray-600">
              <span>My requests</span>
            </button>
          </div>

          {/* View Tabs */}
          <div className="flex max-w-fit divide-x overflow-x-auto rounded-xl border text-sm">
            <button className="whitespace-nowrap bg-white px-3 py-2 font-medium">
              Table
            </button>
            <button className="whitespace-nowrap bg-gray-100 px-3 py-2 text-gray-600">
              Calendar
            </button>
            <button className="whitespace-nowrap bg-gray-100 px-3 py-2 text-gray-600">
              Weekly
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 flex flex-wrap items-center gap-3 rounded-lg bg-gray-50 p-4">
          <div className="flex items-center gap-2">
            <svg
              className="h-4 w-4 text-gray-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"
              />
            </svg>
            <span className="text-sm font-medium text-gray-700">Filters:</span>
          </div>

          <select className="rounded border border-gray-300 px-3 py-1 text-sm">
            <option>Players needed: All</option>
            <option>1 player</option>
            <option>2 players</option>
            <option>3 players</option>
          </select>

          <select className="rounded border border-gray-300 px-3 py-1 text-sm">
            <option>NTRP: All</option>
            <option>2.5</option>
            <option>3.0</option>
            <option>3.5</option>
            <option>4.0</option>
            <option>4.5</option>
          </select>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Sort:</span>
            <button className="flex items-center gap-1 rounded border border-gray-300 px-3 py-1 text-sm">
              <span>Newest first</span>
              <svg
                className="h-3 w-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Buddy Request Cards */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Available Requests
        </h4>

        <div className="space-y-4">
          {/* Active Request */}
          <div className="group w-full cursor-pointer rounded-xl bg-gray-50 p-4 transition-all duration-200 hover:scale-[1.01] hover:bg-gray-100 hover:shadow-md">
            <div className="flex gap-4">
              {/* User Photo */}
              <div className="w-24 flex-shrink-0">
                <div className="flex aspect-square w-full items-center justify-center rounded-full bg-orange-500 text-lg font-medium text-white shadow-sm">
                  JS
                </div>
              </div>

              {/* Request Details */}
              <div className="flex min-w-0 flex-1 flex-col">
                <div className="flex items-start justify-between">
                  {/* Date and time information */}
                  <div className="min-w-0">
                    <p className="mb-2 break-words text-base font-medium">
                      Today (Monday) • 6:00 PM - 7:00 PM
                    </p>

                    {/* User information */}
                    <div className="mb-2 flex flex-wrap items-center text-sm text-gray-500">
                      <span>Added 2 hours ago</span>
                      <span className="mx-1">by</span>
                      <span className="font-medium capitalize text-gray-700">
                        John Smith
                      </span>
                    </div>
                  </div>

                  {/* Arrow icon */}
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    className="flex-shrink-0 text-gray-400 transition-transform duration-200 group-hover:translate-x-1"
                  >
                    <path
                      d="M4.16669 10H15.8334M15.8334 10L10 4.16669M15.8334 10L10 15.8334"
                      stroke="currentColor"
                      strokeWidth="1.67"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>

                {/* Tags */}
                <div className="mt-auto flex flex-wrap items-center gap-2">
                  <span className="rounded-full border bg-white px-3 py-1 text-xs text-gray-600">
                    Tennis
                  </span>
                  <span className="rounded-full border bg-white px-3 py-1 text-xs text-gray-600">
                    NTRP: 3.5 - 4.0
                  </span>
                  <span className="flex items-center gap-1 rounded-full border bg-white px-3 py-1 text-xs text-gray-600">
                    <svg width="12" height="12" viewBox="0 0 16 16" fill="none">
                      <path
                        d="M7.99998 1.33301C6.34313 1.33301 4.99998 2.67615 4.99998 4.33301C4.99998 5.98986 6.34313 7.33301 7.99998 7.33301C9.65683 7.33301 11 5.98986 11 4.33301C11 2.67615 9.65683 1.33301 7.99998 1.33301Z"
                        fill="currentColor"
                      />
                      <path
                        d="M2.66669 14.6663C2.66669 12.0889 4.75597 9.99967 7.33335 9.99967H8.66669C11.2441 9.99967 13.3334 12.0889 13.3334 14.6663H2.66669Z"
                        fill="currentColor"
                      />
                    </svg>
                    Need 1 player
                  </span>
                  <span className="rounded-full border border-orange-200 bg-orange-100 px-3 py-1 text-xs font-medium text-orange-700">
                    Open
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Another Request */}
          <div className="group w-full cursor-pointer rounded-xl bg-gray-50 p-4 transition-all duration-200 hover:scale-[1.01] hover:bg-gray-100 hover:shadow-md">
            <div className="flex gap-4">
              {/* User Photo */}
              <div className="w-24 flex-shrink-0">
                <div className="flex aspect-square w-full items-center justify-center rounded-full bg-blue-500 text-lg font-medium text-white shadow-sm">
                  MB
                </div>
              </div>

              {/* Request Details */}
              <div className="flex min-w-0 flex-1 flex-col">
                <div className="flex items-start justify-between">
                  {/* Date and time information */}
                  <div className="min-w-0">
                    <p className="mb-2 break-words text-base font-medium">
                      Wednesday, Dec 18 • 10:00 AM - 11:00 AM
                    </p>

                    {/* User information */}
                    <div className="mb-2 flex flex-wrap items-center text-sm text-gray-500">
                      <span>Added 5 hours ago</span>
                      <span className="mx-1">by</span>
                      <span className="font-medium capitalize text-gray-700">
                        Maria Brown
                      </span>
                    </div>
                  </div>

                  {/* Arrow icon */}
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    className="flex-shrink-0 text-gray-400 transition-transform duration-200 group-hover:translate-x-1"
                  >
                    <path
                      d="M4.16669 10H15.8334M15.8334 10L10 4.16669M15.8334 10L10 15.8334"
                      stroke="currentColor"
                      strokeWidth="1.67"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>

                {/* Tags */}
                <div className="mt-auto flex flex-wrap items-center gap-2">
                  <span className="rounded-full border bg-white px-3 py-1 text-xs text-gray-600">
                    Pickleball
                  </span>
                  <span className="rounded-full border bg-white px-3 py-1 text-xs text-gray-600">
                    NTRP: 3.0
                  </span>
                  <span className="flex items-center gap-1 rounded-full border bg-white px-3 py-1 text-xs text-gray-600">
                    <svg width="12" height="12" viewBox="0 0 16 16" fill="none">
                      <path
                        d="M7.99998 1.33301C6.34313 1.33301 4.99998 2.67615 4.99998 4.33301C4.99998 5.98986 6.34313 7.33301 7.99998 7.33301C9.65683 7.33301 11 5.98986 11 4.33301C11 2.67615 9.65683 1.33301 7.99998 1.33301Z"
                        fill="currentColor"
                      />
                      <path
                        d="M2.66669 14.6663C2.66669 12.0889 4.75597 9.99967 7.33335 9.99967H8.66669C11.2441 9.99967 13.3334 12.0889 13.3334 14.6663H2.66669Z"
                        fill="currentColor"
                      />
                    </svg>
                    Need 2 players
                  </span>
                  <span className="rounded-full border border-orange-200 bg-orange-100 px-3 py-1 text-xs font-medium text-orange-700">
                    Open
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Filled Request */}
          <div className="group w-full cursor-pointer rounded-xl border border-green-200 bg-green-50 p-4 transition-all duration-200 hover:scale-[1.01] hover:bg-green-100 hover:shadow-md">
            <div className="flex gap-4">
              {/* User Photo */}
              <div className="w-24 flex-shrink-0">
                <div className="flex aspect-square w-full items-center justify-center rounded-full bg-green-600 text-lg font-medium text-white shadow-sm">
                  DW
                </div>
              </div>

              {/* Request Details */}
              <div className="flex min-w-0 flex-1 flex-col">
                <div className="flex items-start justify-between">
                  {/* Date and time information */}
                  <div className="min-w-0">
                    <p className="mb-2 break-words text-base font-medium text-green-900">
                      Friday, Dec 20 • 2:00 PM - 3:00 PM
                    </p>

                    {/* User information */}
                    <div className="mb-2 flex flex-wrap items-center text-sm text-green-600">
                      <span>Added 1 day ago</span>
                      <span className="mx-1">by</span>
                      <span className="font-medium capitalize text-green-700">
                        David Wilson
                      </span>
                    </div>
                  </div>

                  {/* Arrow icon */}
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    className="flex-shrink-0 text-green-600 transition-transform duration-200 group-hover:translate-x-1"
                  >
                    <path
                      d="M4.16669 10H15.8334M15.8334 10L10 4.16669M15.8334 10L10 15.8334"
                      stroke="currentColor"
                      strokeWidth="1.67"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>

                {/* Tags */}
                <div className="mt-auto flex flex-wrap items-center gap-2">
                  <span className="rounded-full border border-green-200 bg-white px-3 py-1 text-xs text-green-700">
                    Tennis
                  </span>
                  <span className="rounded-full border border-green-200 bg-white px-3 py-1 text-xs text-green-700">
                    NTRP: 4.0 - 4.5
                  </span>
                  <span className="flex items-center gap-1 rounded-full border border-green-200 bg-white px-3 py-1 text-xs text-green-700">
                    <svg width="12" height="12" viewBox="0 0 16 16" fill="none">
                      <path
                        d="M7.99998 1.33301C6.34313 1.33301 4.99998 2.67615 4.99998 4.33301C4.99998 5.98986 6.34313 7.33301 7.99998 7.33301C9.65683 7.33301 11 5.98986 11 4.33301C11 2.67615 9.65683 1.33301 7.99998 1.33301Z"
                        fill="currentColor"
                      />
                      <path
                        d="M2.66669 14.6663C2.66669 12.0889 4.75597 9.99967 7.33335 9.99967H8.66669C11.2441 9.99967 13.3334 12.0889 13.3334 14.6663H2.66669Z"
                        fill="currentColor"
                      />
                    </svg>
                    Full (3/3 players)
                  </span>
                  <span className="rounded-full border border-green-300 bg-green-200 px-3 py-1 text-xs font-medium text-green-800">
                    Complete
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Request Details Modal Preview */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Request Details View
        </h4>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Request Information */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="mb-4 flex items-center space-x-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-orange-500 text-lg font-medium text-white">
                JS
              </div>
              <div>
                <div className="font-medium text-gray-900">John Smith</div>
                <div className="text-sm text-gray-600">
                  Tennis Player • NTRP 3.5
                </div>
              </div>
            </div>

            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Sport:</span>
                <span className="font-medium">Tennis - Singles</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date & Time:</span>
                <span className="font-medium">Today • 6:00 PM - 7:00 PM</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Duration:</span>
                <span className="font-medium">1 hour</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">NTRP Range:</span>
                <span className="font-medium">3.5 - 4.0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Players Needed:</span>
                <span className="font-medium">1 more player</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Court:</span>
                <span className="font-medium">Auto-assigned</span>
              </div>
            </div>

            <div className="mt-4 border-t border-gray-200 pt-4">
              <div className="mb-2 text-sm font-medium text-gray-700">
                Notes:
              </div>
              <div className="text-sm text-gray-600">
                Looking for a consistent hitting partner for regular practice
                sessions. Prefer someone who can play at least once a week.
              </div>
            </div>
          </div>

          {/* Join Request Actions */}
          <div className="rounded-lg border border-gray-200 p-4">
            <h5 className="mb-3 font-medium text-gray-900">
              Join This Request
            </h5>

            <div className="space-y-4">
              {/* Player Selection */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Select Players
                </label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-3 rounded border border-blue-200 bg-blue-50 p-2">
                    <input
                      type="checkbox"
                      checked
                      className="h-4 w-4 text-blue-600"
                    />
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white">
                      YU
                    </div>
                    <div>
                      <div className="text-sm font-medium">You</div>
                      <div className="text-xs text-gray-600">NTRP: 3.5</div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 rounded border border-gray-200 p-2 hover:bg-gray-50">
                    <input type="checkbox" className="h-4 w-4 text-blue-600" />
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-400 text-sm font-medium text-white">
                      FM
                    </div>
                    <div>
                      <div className="text-sm font-medium">Family Member</div>
                      <div className="text-xs text-gray-600">NTRP: 3.0</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Message */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Message (Optional)
                </label>
                <textarea
                  className="w-full rounded border border-gray-300 px-3 py-2 text-sm"
                  rows="3"
                  placeholder="Hi! I'd love to join your tennis session..."
                ></textarea>
              </div>

              {/* Pricing Info */}
              <div className="rounded bg-gray-50 p-3">
                <div className="mb-2 text-sm font-medium text-gray-900">
                  Estimated Cost
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Court Fee (split):</span>
                    <span>$12.50</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Service Fee:</span>
                    <span>$1.25</span>
                  </div>
                  <div className="flex justify-between border-t border-gray-300 pt-1 font-medium">
                    <span>Your Total:</span>
                    <span>$13.75</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button className="flex-1 rounded bg-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-400">
                  Cancel
                </button>
                <button className="flex-1 rounded bg-orange-600 px-4 py-2 text-sm font-medium text-white hover:bg-orange-700">
                  Join Request
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Calendar and Weekly Views Preview */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Calendar View */}
        <div className="rounded-lg bg-white p-6 shadow-sm">
          <h4 className="mb-4 text-lg font-medium text-gray-900">
            Calendar View
          </h4>

          <div className="rounded-lg border p-4">
            <div className="mb-4 flex items-center justify-between">
              <button className="rounded p-1 hover:bg-gray-100">
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <span className="font-medium">December 2024</span>
              <button className="rounded p-1 hover:bg-gray-100">
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1 text-sm">
              <div className="py-2 text-center font-medium text-gray-500">
                S
              </div>
              <div className="py-2 text-center font-medium text-gray-500">
                M
              </div>
              <div className="py-2 text-center font-medium text-gray-500">
                T
              </div>
              <div className="py-2 text-center font-medium text-gray-500">
                W
              </div>
              <div className="py-2 text-center font-medium text-gray-500">
                T
              </div>
              <div className="py-2 text-center font-medium text-gray-500">
                F
              </div>
              <div className="py-2 text-center font-medium text-gray-500">
                S
              </div>

              {/* Sample dates with buddy requests */}
              <div className="py-2 text-center text-gray-400">1</div>
              <div className="py-2 text-center text-gray-400">2</div>
              <div className="py-2 text-center text-gray-400">3</div>
              <div className="py-2 text-center text-gray-400">4</div>
              <div className="py-2 text-center text-gray-400">5</div>
              <div className="py-2 text-center text-gray-400">6</div>
              <div className="py-2 text-center text-gray-400">7</div>
              <div className="py-2 text-center text-gray-400">8</div>
              <div className="py-2 text-center text-gray-400">9</div>
              <div className="py-2 text-center text-gray-400">10</div>
              <div className="py-2 text-center text-gray-400">11</div>
              <div className="py-2 text-center text-gray-400">12</div>
              <div className="py-2 text-center text-gray-400">13</div>
              <div className="py-2 text-center text-gray-400">14</div>
              <div className="relative rounded bg-orange-100 py-2 text-center text-orange-800">
                15
                <div className="absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 transform rounded-full bg-orange-600"></div>
              </div>
              <div className="cursor-pointer py-2 text-center hover:bg-gray-100">
                16
              </div>
              <div className="cursor-pointer py-2 text-center hover:bg-gray-100">
                17
              </div>
              <div className="relative rounded bg-orange-100 py-2 text-center text-orange-800">
                18
                <div className="absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 transform rounded-full bg-orange-600"></div>
              </div>
              <div className="cursor-pointer py-2 text-center hover:bg-gray-100">
                19
              </div>
              <div className="relative rounded bg-green-100 py-2 text-center text-green-800">
                20
                <div className="absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 transform rounded-full bg-green-600"></div>
              </div>
              <div className="cursor-pointer py-2 text-center hover:bg-gray-100">
                21
              </div>
            </div>

            <div className="mt-4 flex items-center gap-4 text-xs">
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 rounded-full bg-orange-600"></div>
                <span>Open requests</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 rounded-full bg-green-600"></div>
                <span>Complete</span>
              </div>
            </div>
          </div>
        </div>

        {/* Weekly View */}
        <div className="rounded-lg bg-white p-6 shadow-sm">
          <h4 className="mb-4 text-lg font-medium text-gray-900">
            Weekly View
          </h4>

          <div className="rounded-lg border p-4">
            <div className="mb-4 flex items-center justify-between">
              <button className="rounded p-1 hover:bg-gray-100">
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <span className="font-medium">Dec 15 - Dec 21, 2024</span>
              <button className="rounded p-1 hover:bg-gray-100">
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>

            {/* Weekly Grid */}
            <div className="space-y-2">
              {/* Monday */}
              <div className="flex items-center gap-3 rounded p-2 hover:bg-gray-50">
                <div className="w-16 text-sm font-medium text-gray-700">
                  Mon 16
                </div>
                <div className="flex-1">
                  <div className="flex h-6 items-center rounded bg-orange-100 px-2 text-xs text-orange-800">
                    6:00 PM - Tennis (John S.)
                  </div>
                </div>
              </div>

              {/* Tuesday */}
              <div className="flex items-center gap-3 rounded p-2 hover:bg-gray-50">
                <div className="w-16 text-sm font-medium text-gray-700">
                  Tue 17
                </div>
                <div className="flex-1 text-xs text-gray-500">No requests</div>
              </div>

              {/* Wednesday */}
              <div className="flex items-center gap-3 rounded p-2 hover:bg-gray-50">
                <div className="w-16 text-sm font-medium text-gray-700">
                  Wed 18
                </div>
                <div className="flex-1">
                  <div className="flex h-6 items-center rounded bg-orange-100 px-2 text-xs text-orange-800">
                    10:00 AM - Pickleball (Maria B.)
                  </div>
                </div>
              </div>

              {/* Thursday */}
              <div className="flex items-center gap-3 rounded p-2 hover:bg-gray-50">
                <div className="w-16 text-sm font-medium text-gray-700">
                  Thu 19
                </div>
                <div className="flex-1 text-xs text-gray-500">No requests</div>
              </div>

              {/* Friday */}
              <div className="flex items-center gap-3 rounded p-2 hover:bg-gray-50">
                <div className="w-16 text-sm font-medium text-gray-700">
                  Fri 20
                </div>
                <div className="flex-1">
                  <div className="flex h-6 items-center rounded bg-green-100 px-2 text-xs text-green-800">
                    2:00 PM - Tennis (David W.) - Complete
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Feature Summary */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Find a Buddy Features
        </h4>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div className="rounded-lg border border-orange-200 bg-orange-50 p-4">
            <div className="mb-3 flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange-600 text-sm font-medium text-white">
                1
              </div>
              <h5 className="font-medium text-orange-900">Create Requests</h5>
            </div>
            <div className="space-y-2 text-sm text-orange-800">
              <p>• Select sport, date, and time preferences</p>
              <p>• Set NTRP skill level range for matching</p>
              <p>• Specify number of players needed</p>
              <p>• Add custom notes and requirements</p>
            </div>
          </div>

          <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
            <div className="mb-3 flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white">
                2
              </div>
              <h5 className="font-medium text-blue-900">Browse & Join</h5>
            </div>
            <div className="space-y-2 text-sm text-blue-800">
              <p>• View all open requests from other members</p>
              <p>• Filter by sport, skill level, and availability</p>
              <p>• Join requests that match your preferences</p>
              <p>• Multiple view options: Table, Calendar, Weekly</p>
            </div>
          </div>

          <div className="rounded-lg border border-green-200 bg-green-50 p-4">
            <div className="mb-3 flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white">
                3
              </div>
              <h5 className="font-medium text-green-900">Play Together</h5>
            </div>
            <div className="space-y-2 text-sm text-green-800">
              <p>• Automatic court booking when request fills</p>
              <p>• Split court costs among all participants</p>
              <p>• Email notifications and confirmations</p>
              <p>• Subscription options for regular matches</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
