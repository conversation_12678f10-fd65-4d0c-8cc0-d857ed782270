export default function PricesPerspective({ pricing, sports }) {
  return (
    <div className="space-y-6">
      <div className="rounded-lg bg-green-50 p-4">
        <h3 className="mb-2 text-lg font-semibold text-green-900">
          Pricing Overview
        </h3>
        <p className="text-green-800">
          View the pricing structure for courts, lessons, and other services
          offered by the club. Prices may vary based on membership type,
          time of day, and sport selection.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">
            Court Reservations
          </h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Hourly rates based on sport and time</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Member vs non-member pricing</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Peak and off-peak rates</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Service fees and taxes included</span>
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">
            Lessons & Coaching
          </h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>Coach-specific hourly rates</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>Group vs private lesson pricing</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>Package deals and discounts</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>Skill level considerations</span>
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-lg bg-white p-4 shadow-sm">
        <h4 className="mb-3 font-medium text-gray-900">
          Pricing Features
        </h4>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Dynamic pricing based on demand</p>
            <p>• Membership tier discounts</p>
            <p>• Seasonal rate adjustments</p>
            <p>• Multi-hour booking discounts</p>
          </div>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Transparent fee breakdown</p>
            <p>• Advance booking incentives</p>
            <p>• Family and group rates</p>
            <p>• Cancellation fee policies</p>
          </div>
        </div>
      </div>

      {pricing && pricing.length > 0 ? (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-3 font-medium text-gray-900">
            Current Pricing Structure
          </h4>
          <div className="space-y-2">
            {pricing.map((price, index) => (
              <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                <span className="text-sm font-medium text-gray-700">
                  {price.sport || 'General'} - {price.type || 'Standard'}
                </span>
                <span className="text-sm text-gray-600">
                  ${price.price || 'Contact for pricing'}
                </span>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="rounded-lg bg-gray-50 p-4 text-center">
          <p className="text-gray-600">
            Pricing information will be displayed here when configured by the club.
          </p>
        </div>
      )}

      <div className="rounded-lg bg-blue-50 p-4">
        <h4 className="mb-2 font-medium text-blue-900">Pricing Notes</h4>
        <div className="space-y-2 text-sm text-blue-800">
          <p>• All prices are subject to applicable taxes and fees</p>
          <p>• Membership discounts are automatically applied at checkout</p>
          <p>• Prices may vary during special events or tournaments</p>
          <p>• Contact the club directly for custom pricing arrangements</p>
        </div>
      </div>
    </div>
  );
}
