export default function CourtBookingPerspective({ club }) {
  const courtDescription = club?.court_description
    ? JSON.parse(club?.court_description)
    : {
        reservation_description: "",
        payment_description: "",
      };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="rounded-lg bg-blue-50 p-4">
        <h3 className="mb-2 text-lg font-semibold text-blue-900">
          User Court Booking Experience
        </h3>
        <p className="text-blue-800">
          This shows how users experience the court reservation process on your
          platform.
        </p>
      </div>

      {/* Step Progress Indicator */}
      <div className="flex items-center justify-center rounded-lg bg-white p-4 shadow-sm">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white">
              1
            </div>
            <span className="ml-2 text-sm font-medium text-gray-900">
              Select date and time
            </span>
          </div>
          <div className="h-px w-8 bg-gray-300"></div>
          <div className="flex items-center">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-300 text-sm font-medium text-gray-600">
              2
            </div>
            <span className="ml-2 text-sm font-medium text-gray-500">
              Reservation detail
            </span>
          </div>
          <div className="h-px w-8 bg-gray-300"></div>
          <div className="flex items-center">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-300 text-sm font-medium text-gray-600">
              3
            </div>
            <span className="ml-2 text-sm font-medium text-gray-500">
              Payment
            </span>
          </div>
        </div>
      </div>

      {/* Main Booking Interface */}
      <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
        {/* Sport Selection */}
        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">Sport Selection</h4>
          <div className="space-y-3">
            <div className="rounded-lg border-2 border-blue-200 bg-blue-50 p-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-900">
                  Tennis
                </span>
                <div className="h-4 w-4 rounded-full bg-blue-600"></div>
              </div>
            </div>
            <div className="rounded-lg border border-gray-200 p-3 opacity-60">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Pickleball</span>
                <div className="h-4 w-4 rounded-full border-2 border-gray-300"></div>
              </div>
            </div>
            <div className="rounded-lg border border-gray-200 p-3 opacity-60">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Squash</span>
                <div className="h-4 w-4 rounded-full border-2 border-gray-300"></div>
              </div>
            </div>
          </div>

          {/* Type Selection */}
          <div className="mt-4">
            <h5 className="mb-2 text-sm font-medium text-gray-700">Type</h5>
            <div className="space-y-2">
              <div className="rounded border-2 border-blue-200 bg-blue-50 px-3 py-2">
                <span className="text-sm font-medium text-blue-900">
                  Singles
                </span>
              </div>
              <div className="rounded border border-gray-200 px-3 py-2 opacity-60">
                <span className="text-sm text-gray-600">Doubles</span>
              </div>
            </div>
          </div>
        </div>

        {/* Calendar */}
        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">Select Date</h4>
          <div className="mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700">
            You can reserve a court up to 10 days in advance.
          </div>

          {/* Mini Calendar */}
          <div className="rounded-lg border p-3">
            <div className="mb-3 flex items-center justify-between">
              <button className="rounded p-1 hover:bg-gray-100">
                <svg
                  className="h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <span className="text-sm font-medium">December 2024</span>
              <button className="rounded p-1 hover:bg-gray-100">
                <svg
                  className="h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1 text-xs">
              <div className="py-1 text-center text-gray-500">S</div>
              <div className="py-1 text-center text-gray-500">M</div>
              <div className="py-1 text-center text-gray-500">T</div>
              <div className="py-1 text-center text-gray-500">W</div>
              <div className="py-1 text-center text-gray-500">T</div>
              <div className="py-1 text-center text-gray-500">F</div>
              <div className="py-1 text-center text-gray-500">S</div>

              {/* Sample dates */}
              <div className="py-1 text-center text-gray-400">1</div>
              <div className="py-1 text-center text-gray-400">2</div>
              <div className="py-1 text-center text-gray-400">3</div>
              <div className="py-1 text-center text-gray-400">4</div>
              <div className="py-1 text-center text-gray-400">5</div>
              <div className="py-1 text-center text-gray-400">6</div>
              <div className="py-1 text-center text-gray-400">7</div>
              <div className="py-1 text-center text-gray-400">8</div>
              <div className="py-1 text-center text-gray-400">9</div>
              <div className="py-1 text-center text-gray-400">10</div>
              <div className="py-1 text-center text-gray-400">11</div>
              <div className="py-1 text-center text-gray-400">12</div>
              <div className="py-1 text-center text-gray-400">13</div>
              <div className="py-1 text-center text-gray-400">14</div>
              <div className="rounded bg-blue-600 py-1 text-center text-white">
                15
              </div>
              <div className="cursor-pointer py-1 text-center hover:bg-gray-100">
                16
              </div>
              <div className="cursor-pointer py-1 text-center hover:bg-gray-100">
                17
              </div>
              <div className="cursor-pointer py-1 text-center hover:bg-gray-100">
                18
              </div>
              <div className="cursor-pointer py-1 text-center hover:bg-gray-100">
                19
              </div>
              <div className="cursor-pointer py-1 text-center hover:bg-gray-100">
                20
              </div>
              <div className="cursor-pointer py-1 text-center hover:bg-gray-100">
                21
              </div>
            </div>
          </div>
        </div>

        {/* Time Slots */}
        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">Available Times</h4>
          <div className="mb-3 rounded-lg bg-blue-50 p-3">
            <p className="text-sm text-blue-800">
              <span className="font-medium">Minimum booking time:</span>{" "}
              <span className="font-semibold text-blue-900">1 hour</span>
            </p>
            <p className="mt-1 text-xs text-blue-600">
              Based on available courts for your selected sport
            </p>
          </div>

          <div className="max-h-64 space-y-2 overflow-y-auto">
            <button className="w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50">
              8:00 AM
            </button>
            <button className="w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50">
              8:30 AM
            </button>
            <button className="w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50">
              9:00 AM
            </button>
            <button className="w-full rounded-lg border-2 border-blue-600 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-600">
              10:00 AM - 11:00 AM
            </button>
            <button className="w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50">
              11:00 AM
            </button>
            <button className="w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50">
              11:30 AM
            </button>
            <button className="w-full cursor-not-allowed rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-400 opacity-50">
              12:00 PM (Unavailable)
            </button>
          </div>

          <button className="mt-4 w-full rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700">
            Next: Players
          </button>
        </div>
      </div>

      {/* Player Selection Preview */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Step 2: Player Selection
        </h4>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Reservation Summary */}
          <div className="rounded-lg border border-gray-200 p-4">
            <h5 className="mb-3 font-medium text-gray-900">
              Reservation Summary
            </h5>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Sport:</span>
                <span className="font-medium">Tennis - Singles</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date:</span>
                <span className="font-medium">Dec 15, 2024</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Time:</span>
                <span className="font-medium">10:00 AM - 11:00 AM</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Duration:</span>
                <span className="font-medium">1 hour</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Court:</span>
                <span className="font-medium">Auto-assigned</span>
              </div>
            </div>
          </div>

          {/* Player Selection */}
          <div className="rounded-lg border border-gray-200 p-4">
            <h5 className="mb-3 font-medium text-gray-900">Select Players</h5>

            {/* Primary Player */}
            <div className="mb-4">
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Primary Player
              </label>
              <div className="flex items-center space-x-3 rounded-lg border-2 border-blue-200 bg-blue-50 p-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white">
                  JD
                </div>
                <span className="text-sm font-medium text-blue-900">
                  John Doe (You)
                </span>
              </div>
            </div>

            {/* Additional Players */}
            <div className="mb-4">
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Additional Players
              </label>
              <div className="max-h-32 space-y-2 overflow-y-auto">
                <div className="flex cursor-pointer items-center space-x-3 rounded-lg border border-gray-200 p-2 hover:bg-gray-50">
                  <input
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-blue-600"
                  />
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-300 text-xs">
                    JS
                  </div>
                  <span className="text-sm">Jane Smith</span>
                </div>
                <div className="flex cursor-pointer items-center space-x-3 rounded-lg border border-gray-200 p-2 hover:bg-gray-50">
                  <input
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-blue-600"
                    checked
                  />
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-500 text-xs text-white">
                    MB
                  </div>
                  <span className="text-sm">Mike Brown</span>
                </div>
                <div className="flex cursor-pointer items-center space-x-3 rounded-lg border border-gray-200 p-2 hover:bg-gray-50">
                  <input
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-blue-600"
                  />
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-300 text-xs">
                    LW
                  </div>
                  <span className="text-sm">Lisa Wilson</span>
                </div>
              </div>
            </div>

            {/* Find a Buddy Option */}
            <div className="rounded-lg border border-orange-200 bg-orange-50 p-3">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-orange-600"
                />
                <span className="text-sm font-medium text-orange-900">
                  Find a Buddy
                </span>
              </div>
              <p className="mt-1 text-xs text-orange-700">
                Let other members join your reservation
              </p>
            </div>
          </div>
        </div>

        {/* Pricing Summary */}
        <div className="mt-6 rounded-lg bg-gray-50 p-4">
          <h5 className="mb-3 font-medium text-gray-900">Pricing</h5>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Court Fee (1 hour):</span>
              <span>$25.00</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Service Fee:</span>
              <span>$2.50</span>
            </div>
            <div className="flex justify-between border-t border-gray-300 pt-2 font-medium">
              <span>Total:</span>
              <span>$27.50</span>
            </div>
          </div>
        </div>

        <div className="mt-4 flex space-x-3">
          <button className="flex-1 rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
            Back
          </button>
          <button className="flex-1 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700">
            Continue to Payment
          </button>
        </div>
      </div>

      {/* Payment Step Preview */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Step 3: Payment
        </h4>

        {/* Payment Warning */}
        <div className="mb-4 rounded-xl bg-orange-500 px-4 py-3 text-white">
          <div className="flex items-center gap-2">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span className="text-sm">
              Your session is reserved. You have 15 minutes to complete payment.
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Final Summary */}
          <div className="rounded-lg border border-gray-200 p-4">
            <h5 className="mb-3 font-medium text-gray-900">Final Summary</h5>
            <div className="space-y-3 text-sm">
              <div>
                <div className="font-medium text-gray-900">
                  Tennis - Singles
                </div>
                <div className="text-gray-600">
                  Dec 15, 2024 • 10:00 AM - 11:00 AM
                </div>
                <div className="text-gray-600">Court 1 • 1 hour</div>
              </div>

              <div>
                <div className="font-medium text-gray-900">Players (2)</div>
                <div className="text-gray-600">John Doe, Mike Brown</div>
              </div>

              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Court Fee:</span>
                  <span>$25.00</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Service Fee:</span>
                  <span>$2.50</span>
                </div>
                <div className="flex justify-between text-lg font-medium">
                  <span>Total:</span>
                  <span>$27.50</span>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Form */}
          <div className="rounded-lg border border-gray-200 p-4">
            <h5 className="mb-3 font-medium text-gray-900">Payment Details</h5>
            <div className="space-y-4">
              <div className="rounded-lg border border-gray-300 bg-gray-50 p-3">
                <div className="text-sm text-gray-600">Card Number</div>
                <div className="font-mono text-sm">•••• •••• •••• 4242</div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div className="rounded-lg border border-gray-300 bg-gray-50 p-3">
                  <div className="text-sm text-gray-600">Expiry</div>
                  <div className="font-mono text-sm">12/25</div>
                </div>
                <div className="rounded-lg border border-gray-300 bg-gray-50 p-3">
                  <div className="text-sm text-gray-600">CVC</div>
                  <div className="font-mono text-sm">•••</div>
                </div>
              </div>

              <button className="w-full rounded-lg bg-green-600 px-4 py-3 text-sm font-medium text-white hover:bg-green-700">
                Pay $27.50
              </button>
            </div>
          </div>
        </div>

        <div className="mt-4 space-y-4 text-sm text-gray-500">
          <p>
            By clicking "Pay now" you agree to our{" "}
            <span className="font-medium underline">Terms and Conditions</span>{" "}
            and <span className="font-medium underline">Privacy Policy</span>.
            All sales are final unless stated otherwise.
          </p>
        </div>
      </div>

      {/* Custom Messages */}
      {courtDescription.reservation_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Reservation Message
          </h4>
          <p className="text-sm text-gray-700">
            {courtDescription.reservation_description}
          </p>
        </div>
      )}

      {courtDescription.payment_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Payment Message
          </h4>
          <p className="text-sm text-gray-700">
            {courtDescription.payment_description}
          </p>
        </div>
      )}
    </div>
  );
}
