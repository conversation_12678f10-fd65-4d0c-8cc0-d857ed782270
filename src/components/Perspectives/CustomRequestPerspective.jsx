export default function CustomRequestPerspective({ club }) {
  const customRequestDescription = club?.custom_request_description
    ? JSON.parse(club?.custom_request_description)
    : {
        reservation_description: "",
        payment_description: "",
      };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="rounded-lg bg-orange-50 p-4">
        <h3 className="mb-2 text-lg font-semibold text-orange-900">
          User Custom Request Experience
        </h3>
        <p className="text-orange-800">
          This shows how users experience creating custom requests for group
          activities and special arrangements.
        </p>
      </div>

      {/* Exact UI Copy - Header */}
      <div className="flex items-center justify-center bg-white p-4 shadow-sm">
        <p>Custom Request</p>
      </div>

      {/* Exact UI Copy - Main Content */}
      <div className="p-4">
        <div className="p-4">
          <div className="space-y-6">
            <div className="mx-auto max-w-7xl p-4">
              <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
                {/* Summary Panel - Exact Copy */}
                <div className="h-fit rounded-lg bg-white shadow-5">
                  <div className="flex items-center justify-center rounded-lg bg-gray-50 p-3 text-center">
                    Summary
                  </div>
                  <div className="space-y-4 p-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-700">
                          Number of players
                        </p>
                        <div className="flex max-w-fit items-center gap-2 rounded-xl border border-gray-300">
                          <button className="flex h-8 w-8 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-50">
                            <svg
                              className="h-4 w-4"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </button>
                          <input
                            type="number"
                            className="w-8 rounded-lg border-none p-0 text-center text-gray-700"
                            value="5"
                            min={1}
                            readOnly
                          />
                          <button className="flex h-8 w-8 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-50">
                            <svg
                              className="h-4 w-4"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <span>
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z"
                              fill="#868C98"
                            />
                          </svg>
                        </span>
                        <p className="text-sm text-gray-600">
                          Custom requests are meant for parties of 5 or move
                          players. The requests made will be checked by the club
                          and the club will respond to you over the registered
                          email address.
                        </p>
                      </div>

                      <div>
                        <label className="mb-2 block text-gray-900">
                          Custom request{" "}
                          <span className="text-gray-500">(optional)</span>
                        </label>
                        <textarea
                          className="w-full rounded-xl border border-gray-300 p-2 text-sm"
                          placeholder="Add a note"
                          rows={4}
                          value="Looking to organize a tennis tournament for our company team building event. We need courts for approximately 16 players and would prefer morning slots."
                          maxLength={200}
                          readOnly
                        />
                      </div>
                    </div>

                    {/* Sport Type Selection - Simplified */}
                    <div className="space-y-3">
                      <div>
                        <label className="mb-2 block text-sm font-medium text-gray-700">
                          Sport
                        </label>
                        <select
                          className="w-full rounded-lg border border-gray-300 p-2 text-sm"
                          value="tennis"
                        >
                          <option value="">Select sport</option>
                          <option value="tennis">Tennis</option>
                          <option value="pickleball">Pickleball</option>
                        </select>
                      </div>

                      <div>
                        <label className="mb-2 block text-sm font-medium text-gray-700">
                          Type
                        </label>
                        <select
                          className="w-full rounded-lg border border-gray-300 p-2 text-sm"
                          value="singles"
                        >
                          <option value="">Select type</option>
                          <option value="singles">Singles</option>
                          <option value="doubles">Doubles</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Calendar - Exact Copy */}
                <div className="h-fit rounded-lg bg-white p-4 shadow-5">
                  <div className="mb-4 flex items-center justify-between">
                    <button className="rounded p-1 hover:bg-gray-100">
                      <svg
                        className="h-5 w-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 19l-7-7 7-7"
                        />
                      </svg>
                    </button>
                    <span className="font-medium">December 2024</span>
                    <button className="rounded p-1 hover:bg-gray-100">
                      <svg
                        className="h-5 w-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </button>
                  </div>

                  <div className="grid grid-cols-7 gap-1 text-sm">
                    <div className="py-2 text-center font-medium text-gray-500">
                      S
                    </div>
                    <div className="py-2 text-center font-medium text-gray-500">
                      M
                    </div>
                    <div className="py-2 text-center font-medium text-gray-500">
                      T
                    </div>
                    <div className="py-2 text-center font-medium text-gray-500">
                      W
                    </div>
                    <div className="py-2 text-center font-medium text-gray-500">
                      T
                    </div>
                    <div className="py-2 text-center font-medium text-gray-500">
                      F
                    </div>
                    <div className="py-2 text-center font-medium text-gray-500">
                      S
                    </div>

                    <div className="py-2 text-center text-gray-400">1</div>
                    <div className="py-2 text-center text-gray-400">2</div>
                    <div className="py-2 text-center text-gray-400">3</div>
                    <div className="py-2 text-center text-gray-400">4</div>
                    <div className="py-2 text-center text-gray-400">5</div>
                    <div className="py-2 text-center text-gray-400">6</div>
                    <div className="py-2 text-center text-gray-400">7</div>
                    <div className="py-2 text-center text-gray-400">8</div>
                    <div className="py-2 text-center text-gray-400">9</div>
                    <div className="py-2 text-center text-gray-400">10</div>
                    <div className="py-2 text-center text-gray-400">11</div>
                    <div className="py-2 text-center text-gray-400">12</div>
                    <div className="py-2 text-center text-gray-400">13</div>
                    <div className="py-2 text-center text-gray-400">14</div>
                    <div className="py-2 text-center text-gray-400">15</div>
                    <div className="py-2 text-center text-gray-400">16</div>
                    <div className="py-2 text-center text-gray-400">17</div>
                    <div className="rounded bg-blue-100 py-2 text-center text-blue-800">
                      18
                    </div>
                    <div className="cursor-pointer py-2 text-center hover:bg-gray-100">
                      19
                    </div>
                    <div className="cursor-pointer py-2 text-center hover:bg-gray-100">
                      20
                    </div>
                    <div className="cursor-pointer py-2 text-center hover:bg-gray-100">
                      21
                    </div>
                  </div>
                </div>

                {/* Time Slots - Exact Copy */}
                <div className="h-fit rounded-lg bg-white p-4 shadow-5">
                  <h5 className="mb-4 font-medium text-gray-900">
                    Select Time
                  </h5>
                  <div className="space-y-2">
                    <div className="mb-3 text-sm text-gray-600">
                      December 18, 2024
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <button className="rounded border border-gray-300 p-2 text-sm hover:bg-gray-50">
                        8:00 AM
                      </button>
                      <button className="rounded border border-gray-300 p-2 text-sm hover:bg-gray-50">
                        8:30 AM
                      </button>
                      <button className="rounded border border-gray-300 p-2 text-sm hover:bg-gray-50">
                        9:00 AM
                      </button>
                      <button className="rounded bg-blue-600 p-2 text-sm text-white">
                        9:30 AM
                      </button>
                      <button className="rounded border border-gray-300 p-2 text-sm hover:bg-gray-50">
                        10:00 AM
                      </button>
                      <button className="rounded border border-gray-300 p-2 text-sm hover:bg-gray-50">
                        10:30 AM
                      </button>
                    </div>

                    <div className="mt-4 border-t border-gray-200 pt-4">
                      <button className="w-full rounded bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700">
                        Next: Select coach
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Coach Selection Modal Preview */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Coach Selection View
        </h4>

        <div className="space-y-4">
          {/* Search and Sort */}
          <div className="flex items-center justify-between">
            <div className="relative max-w-md flex-1">
              <input
                type="text"
                placeholder="Search coaches..."
                className="w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 text-sm"
                value="John"
              />
              <svg
                className="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <button className="flex items-center gap-2 rounded-lg border border-gray-300 px-3 py-2 text-sm">
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
                />
              </svg>
              Sort by price
            </button>
          </div>

          {/* Coach Cards */}
          <div className="space-y-3">
            {/* Available Coach */}
            <div className="cursor-pointer rounded-lg border border-gray-200 p-4 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-600 font-medium text-white">
                    JS
                  </div>
                  <div>
                    <h5 className="font-medium text-gray-900">John Smith</h5>
                    <p className="text-sm text-gray-600">
                      Tennis Pro • 8 years experience
                    </p>
                    <div className="mt-1 flex items-center gap-1">
                      <div className="flex text-yellow-400">
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg
                          className="h-4 w-4 fill-current text-gray-300"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </div>
                      <span className="text-sm text-gray-600">
                        4.8 (24 reviews)
                      </span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-900">
                    $85/hour
                  </div>
                  <div className="text-sm text-green-600">Available</div>
                </div>
              </div>
            </div>

            {/* Selected Coach */}
            <div className="rounded-lg border-2 border-blue-600 bg-blue-50 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-600 font-medium text-white">
                    MB
                  </div>
                  <div>
                    <h5 className="font-medium text-blue-900">Maria Brown</h5>
                    <p className="text-sm text-blue-700">
                      Tennis Pro • 12 years experience
                    </p>
                    <div className="mt-1 flex items-center gap-1">
                      <div className="flex text-yellow-400">
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </div>
                      <span className="text-sm text-blue-700">
                        5.0 (18 reviews)
                      </span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-blue-900">
                    $95/hour
                  </div>
                  <div className="text-sm text-blue-600">Selected</div>
                </div>
              </div>
            </div>

            {/* Unavailable Coach */}
            <div className="rounded-lg border border-gray-200 p-4 opacity-60">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-400 font-medium text-white">
                    DW
                  </div>
                  <div>
                    <h5 className="font-medium text-gray-700">David Wilson</h5>
                    <p className="text-sm text-gray-500">
                      Tennis Pro • 6 years experience
                    </p>
                    <div className="mt-1 flex items-center gap-1">
                      <div className="flex text-yellow-400">
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg
                          className="h-4 w-4 fill-current"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg
                          className="h-4 w-4 fill-current text-gray-300"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </div>
                      <span className="text-sm text-gray-500">
                        4.6 (31 reviews)
                      </span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-700">
                    $75/hour
                  </div>
                  <div className="text-sm text-red-600">Unavailable</div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex gap-3 border-t border-gray-200 pt-4">
            <button className="flex-1 rounded bg-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-400">
              Back
            </button>
            <button className="flex-1 rounded bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700">
              Continue to Details
            </button>
          </div>
        </div>
      </div>

      {/* Feature Summary */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Custom Request Features
        </h4>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div className="rounded-lg border border-orange-200 bg-orange-50 p-4">
            <div className="mb-3 flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange-600 text-sm font-medium text-white">
                1
              </div>
              <h5 className="font-medium text-orange-900">Request Details</h5>
            </div>
            <div className="space-y-2 text-sm text-orange-800">
              <p>• Specify group size and requirements</p>
              <p>• Select preferred sports and formats</p>
              <p>• Choose date and time preferences</p>
              <p>• Add custom notes and special requests</p>
            </div>
          </div>

          <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
            <div className="mb-3 flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white">
                2
              </div>
              <h5 className="font-medium text-blue-900">Coach Selection</h5>
            </div>
            <div className="space-y-2 text-sm text-blue-800">
              <p>• Browse available coaches for your time slot</p>
              <p>• View coach profiles and ratings</p>
              <p>• Compare pricing and experience levels</p>
              <p>• Select preferred coach for your group</p>
            </div>
          </div>

          <div className="rounded-lg border border-green-200 bg-green-50 p-4">
            <div className="mb-3 flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white">
                3
              </div>
              <h5 className="font-medium text-green-900">Club Review</h5>
            </div>
            <div className="space-y-2 text-sm text-green-800">
              <p>• Club reviews your custom request</p>
              <p>• Confirmation of availability and pricing</p>
              <p>• Email notification with details</p>
              <p>• Flexible arrangements for large groups</p>
            </div>
          </div>
        </div>
      </div>

      {/* Custom Messages */}
      {customRequestDescription.reservation_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Reservation Message
          </h4>
          <p className="text-sm text-gray-700">
            {customRequestDescription.reservation_description}
          </p>
        </div>
      )}

      {customRequestDescription.payment_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Payment Message
          </h4>
          <p className="text-sm text-gray-700">
            {customRequestDescription.payment_description}
          </p>
        </div>
      )}
    </div>
  );
}
