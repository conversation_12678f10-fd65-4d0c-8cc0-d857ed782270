export default function LessonBookingPerspective({ club }) {
  const lessonDescription = club?.lesson_description
    ? JSON.parse(club?.lesson_description)
    : {
        reservation_description: "",
        payment_description: "",
      };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="rounded-lg bg-purple-50 p-4">
        <h3 className="mb-2 text-lg font-semibold text-purple-900">
          User Lesson Booking Experience
        </h3>
        <p className="text-purple-800">
          This shows how users experience the lesson booking process with three
          different search methods.
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="rounded-lg bg-white p-4 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Lesson Booking Options
        </h4>
        <div className="flex max-w-fit divide-x overflow-x-auto rounded-xl border text-sm">
          <button className="whitespace-nowrap bg-white px-3 py-2 font-medium">
            Find by coach
          </button>
          <button className="whitespace-nowrap bg-gray-100 px-3 py-2 text-gray-600">
            Find by time
          </button>
          <button className="whitespace-nowrap bg-gray-100 px-3 py-2 text-gray-600">
            Custom request
          </button>
        </div>
      </div>

      {/* Find by Coach Tab */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Find by Coach
        </h4>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Sport Selection */}
          <div className="rounded-lg border border-gray-200 p-4">
            <h5 className="mb-3 font-medium text-gray-900">Sport Selection</h5>
            <div className="space-y-3">
              <div className="rounded-lg border-2 border-purple-200 bg-purple-50 p-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-purple-900">
                    Tennis
                  </span>
                  <div className="h-4 w-4 rounded-full bg-purple-600"></div>
                </div>
              </div>
              <div className="rounded-lg border border-gray-200 p-3 opacity-60">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Pickleball</span>
                  <div className="h-4 w-4 rounded-full border-2 border-gray-300"></div>
                </div>
              </div>
            </div>

            {/* Type Selection */}
            <div className="mt-4">
              <h6 className="mb-2 text-sm font-medium text-gray-700">Type</h6>
              <div className="space-y-2">
                <div className="rounded border-2 border-purple-200 bg-purple-50 px-3 py-2">
                  <span className="text-sm font-medium text-purple-900">
                    Private
                  </span>
                </div>
                <div className="rounded border border-gray-200 px-3 py-2 opacity-60">
                  <span className="text-sm text-gray-600">Group</span>
                </div>
              </div>
            </div>
          </div>

          {/* Coach List */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="mb-3 flex items-center justify-between">
              <h5 className="font-medium text-gray-900">Available Coaches</h5>
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search coaches..."
                    className="w-32 rounded-lg border border-gray-300 px-3 py-1 text-sm"
                  />
                  <svg
                    className="absolute right-2 top-1.5 h-4 w-4 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <button className="rounded p-1 hover:bg-gray-100">
                  <svg
                    className="h-4 w-4 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <div className="max-h-64 space-y-3 overflow-y-auto">
              {/* Selected Coach */}
              <div className="rounded-lg border-2 border-purple-200 bg-purple-50 p-3">
                <div className="flex items-center space-x-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-purple-600 font-medium text-white">
                    JS
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-purple-900">
                      John Smith
                    </div>
                    <div className="text-sm text-purple-700">
                      Tennis Pro • 5 years exp
                    </div>
                    <div className="text-sm font-medium text-purple-900">
                      $75/hour
                    </div>
                  </div>
                </div>
              </div>

              {/* Other Coaches */}
              <div className="cursor-pointer rounded-lg border border-gray-200 p-3 hover:bg-gray-50">
                <div className="flex items-center space-x-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-400 font-medium text-white">
                    MB
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">Maria Brown</div>
                    <div className="text-sm text-gray-600">
                      Tennis Coach • 3 years exp
                    </div>
                    <div className="text-sm font-medium text-gray-900">
                      $65/hour
                    </div>
                  </div>
                </div>
              </div>

              <div className="cursor-pointer rounded-lg border border-gray-200 p-3 hover:bg-gray-50">
                <div className="flex items-center space-x-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-400 font-medium text-white">
                    DW
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">
                      David Wilson
                    </div>
                    <div className="text-sm text-gray-600">
                      Tennis Instructor • 2 years exp
                    </div>
                    <div className="text-sm font-medium text-gray-900">
                      $55/hour
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Coach Profile */}
          <div className="rounded-lg border border-gray-200 p-4">
            <h5 className="mb-3 font-medium text-gray-900">Coach Profile</h5>

            <div className="mb-4 text-center">
              <div className="mx-auto mb-2 flex h-16 w-16 items-center justify-center rounded-full bg-purple-600 text-xl font-medium text-white">
                JS
              </div>
              <div className="font-medium text-gray-900">John Smith</div>
              <div className="text-sm text-gray-600">Tennis Professional</div>
            </div>

            <div className="space-y-3 text-sm">
              <div>
                <div className="font-medium text-gray-900">Experience</div>
                <div className="text-gray-600">5 years teaching experience</div>
              </div>

              <div>
                <div className="font-medium text-gray-900">Specialties</div>
                <div className="text-gray-600">
                  Beginner to Advanced, Tournament Prep
                </div>
              </div>

              <div>
                <div className="font-medium text-gray-900">Rate</div>
                <div className="text-gray-600">$75/hour</div>
              </div>

              <div>
                <div className="font-medium text-gray-900">Bio</div>
                <div className="text-xs text-gray-600">
                  Former college player with extensive coaching experience.
                  Specializes in technique improvement and match strategy.
                </div>
              </div>
            </div>

            <button className="mt-4 w-full rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700">
              Check Availability
            </button>
          </div>
        </div>
      </div>

      {/* Find by Time Tab */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">Find by Time</h4>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Sport & Date Selection */}
          <div className="space-y-4">
            {/* Sport Selection */}
            <div className="rounded-lg border border-gray-200 p-4">
              <h5 className="mb-3 font-medium text-gray-900">
                Sport Selection
              </h5>
              <div className="space-y-2">
                <div className="rounded-lg border-2 border-blue-200 bg-blue-50 p-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-blue-900">
                      Tennis - Private
                    </span>
                    <div className="h-4 w-4 rounded-full bg-blue-600"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Date Selection */}
            <div className="rounded-lg border border-gray-200 p-4">
              <h5 className="mb-3 font-medium text-gray-900">Select Date</h5>
              <div className="mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700">
                You can book lessons up to 10 days in advance.
              </div>

              {/* Mini Calendar */}
              <div className="rounded-lg border p-3">
                <div className="mb-3 flex items-center justify-between">
                  <button className="rounded p-1 hover:bg-gray-100">
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 19l-7-7 7-7"
                      />
                    </svg>
                  </button>
                  <span className="text-sm font-medium">December 2024</span>
                  <button className="rounded p-1 hover:bg-gray-100">
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </button>
                </div>

                {/* Calendar Grid */}
                <div className="grid grid-cols-7 gap-1 text-xs">
                  <div className="py-1 text-center text-gray-500">S</div>
                  <div className="py-1 text-center text-gray-500">M</div>
                  <div className="py-1 text-center text-gray-500">T</div>
                  <div className="py-1 text-center text-gray-500">W</div>
                  <div className="py-1 text-center text-gray-500">T</div>
                  <div className="py-1 text-center text-gray-500">F</div>
                  <div className="py-1 text-center text-gray-500">S</div>

                  {/* Sample dates */}
                  <div className="py-1 text-center text-gray-400">1</div>
                  <div className="py-1 text-center text-gray-400">2</div>
                  <div className="py-1 text-center text-gray-400">3</div>
                  <div className="py-1 text-center text-gray-400">4</div>
                  <div className="py-1 text-center text-gray-400">5</div>
                  <div className="py-1 text-center text-gray-400">6</div>
                  <div className="py-1 text-center text-gray-400">7</div>
                  <div className="py-1 text-center text-gray-400">8</div>
                  <div className="py-1 text-center text-gray-400">9</div>
                  <div className="py-1 text-center text-gray-400">10</div>
                  <div className="py-1 text-center text-gray-400">11</div>
                  <div className="py-1 text-center text-gray-400">12</div>
                  <div className="py-1 text-center text-gray-400">13</div>
                  <div className="py-1 text-center text-gray-400">14</div>
                  <div className="rounded bg-blue-600 py-1 text-center text-white">
                    16
                  </div>
                  <div className="cursor-pointer py-1 text-center hover:bg-gray-100">
                    17
                  </div>
                  <div className="cursor-pointer py-1 text-center hover:bg-gray-100">
                    18
                  </div>
                  <div className="cursor-pointer py-1 text-center hover:bg-gray-100">
                    19
                  </div>
                  <div className="cursor-pointer py-1 text-center hover:bg-gray-100">
                    20
                  </div>
                  <div className="cursor-pointer py-1 text-center hover:bg-gray-100">
                    21
                  </div>
                  <div className="cursor-pointer py-1 text-center hover:bg-gray-100">
                    22
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Time Slots */}
          <div className="rounded-lg border border-gray-200 p-4">
            <h5 className="mb-3 font-medium text-gray-900">Available Times</h5>

            <div className="max-h-64 space-y-2 overflow-y-auto">
              <button className="w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50">
                9:00 AM - 10:00 AM
              </button>
              <button className="w-full rounded-lg border-2 border-blue-600 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-600">
                10:00 AM - 11:00 AM
              </button>
              <button className="w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50">
                11:00 AM - 12:00 PM
              </button>
              <button className="w-full cursor-not-allowed rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-400 opacity-50">
                2:00 PM - 3:00 PM (Unavailable)
              </button>
              <button className="w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50">
                3:00 PM - 4:00 PM
              </button>
              <button className="w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50">
                4:00 PM - 5:00 PM
              </button>
            </div>

            <button className="mt-4 w-full rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700">
              Find Coaches
            </button>
          </div>

          {/* Available Coaches for Selected Time */}
          <div className="rounded-lg border border-gray-200 p-4">
            <h5 className="mb-3 font-medium text-gray-900">
              Available Coaches
            </h5>
            <div className="mb-2 text-xs text-gray-600">
              Dec 16, 2024 • 10:00 AM - 11:00 AM
            </div>

            <div className="max-h-64 space-y-3 overflow-y-auto">
              <div className="rounded-lg border-2 border-green-200 bg-green-50 p-3">
                <div className="flex items-center space-x-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white">
                    JS
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-green-900">John Smith</div>
                    <div className="text-sm text-green-700">$75/hour</div>
                  </div>
                  <button className="rounded bg-green-600 px-2 py-1 text-xs text-white hover:bg-green-700">
                    Book
                  </button>
                </div>
              </div>

              <div className="rounded-lg border border-gray-200 p-3">
                <div className="flex items-center space-x-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-400 text-sm font-medium text-white">
                    MB
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">Maria Brown</div>
                    <div className="text-sm text-gray-600">$65/hour</div>
                  </div>
                  <button className="rounded bg-blue-600 px-2 py-1 text-xs text-white hover:bg-blue-700">
                    Book
                  </button>
                </div>
              </div>

              <div className="rounded-lg border border-gray-200 p-3">
                <div className="flex items-center space-x-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-400 text-sm font-medium text-white">
                    DW
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">
                      David Wilson
                    </div>
                    <div className="text-sm text-gray-600">$55/hour</div>
                  </div>
                  <button className="rounded bg-blue-600 px-2 py-1 text-xs text-white hover:bg-blue-700">
                    Book
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Custom Request Tab */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <div className="mb-4 flex items-center justify-between">
          <h4 className="text-lg font-medium text-gray-900">Custom Request</h4>
          <button className="rounded-lg bg-blue-600 px-4 py-2 text-sm text-white hover:bg-blue-700">
            Create request
          </button>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Request List */}
          <div className="rounded-lg border border-gray-200 p-4">
            <h5 className="mb-3 font-medium text-gray-900">Your Requests</h5>

            <div className="max-h-64 space-y-3 overflow-y-auto">
              {/* Active Request */}
              <div className="rounded-lg border-2 border-green-200 bg-green-50 p-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="mb-1 flex items-center space-x-2">
                      <span className="text-sm font-medium text-green-900">
                        Tennis • Private • Beginner
                      </span>
                      <span className="rounded-full bg-green-200 px-2 py-0.5 text-xs text-green-800">
                        Approved
                      </span>
                    </div>
                    <div className="text-xs text-green-700">
                      Dec 18, 2024 • 2:00 PM - 3:00 PM
                    </div>
                    <div className="mt-1 text-xs text-green-600">
                      Players: John Doe
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-green-900">
                      $65
                    </div>
                    <div className="text-xs text-green-700">Maria Brown</div>
                  </div>
                </div>
              </div>

              {/* Pending Request */}
              <div className="rounded-lg border border-gray-200 p-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="mb-1 flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900">
                        Tennis • Group • Intermediate
                      </span>
                      <span className="rounded-full bg-gray-200 px-2 py-0.5 text-xs text-gray-600">
                        Pending
                      </span>
                    </div>
                    <div className="text-xs text-gray-600">
                      Dec 20, 2024 • 4:00 PM - 5:00 PM
                    </div>
                    <div className="mt-1 text-xs text-gray-500">
                      Players: John Doe, Jane Smith
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">$80</div>
                    <div className="text-xs text-gray-500">Waiting...</div>
                  </div>
                </div>
              </div>

              {/* Declined Request */}
              <div className="rounded-lg border border-red-200 bg-red-50 p-3 opacity-75">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="mb-1 flex items-center space-x-2">
                      <span className="text-sm font-medium text-red-900">
                        Tennis • Private • Advanced
                      </span>
                      <span className="rounded-full bg-red-200 px-2 py-0.5 text-xs text-red-800">
                        Declined
                      </span>
                    </div>
                    <div className="text-xs text-red-700">
                      Dec 15, 2024 • 6:00 PM - 7:00 PM
                    </div>
                    <div className="mt-1 text-xs text-red-600">
                      Players: John Doe
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-red-900">$75</div>
                    <div className="text-xs text-red-700">No responses</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Request Details */}
          <div className="rounded-lg border border-gray-200 p-4">
            <h5 className="mb-3 font-medium text-gray-900">Request Details</h5>

            <div className="space-y-4">
              {/* Request Info */}
              <div className="rounded-lg border border-green-200 bg-green-50 p-3">
                <div className="mb-2 flex items-center space-x-2">
                  <span className="text-sm font-medium text-green-900">
                    Tennis • Private • Beginner
                  </span>
                  <span className="rounded-full bg-green-200 px-2 py-0.5 text-xs text-green-800">
                    Approved
                  </span>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-green-700">Date & Time:</span>
                    <span className="font-medium text-green-900">
                      Dec 18, 2024 • 2:00 PM - 3:00 PM
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-green-700">Duration:</span>
                    <span className="font-medium text-green-900">1 hour</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-green-700">Players:</span>
                    <span className="font-medium text-green-900">John Doe</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-green-700">Budget:</span>
                    <span className="font-medium text-green-900">
                      $60-70/hour
                    </span>
                  </div>
                </div>

                <div className="mt-3 border-t border-green-200 pt-3">
                  <div className="mb-1 text-xs text-green-700">
                    Special Requirements:
                  </div>
                  <div className="text-xs text-green-600">
                    Looking for a patient coach to help with basic strokes and
                    footwork. Beginner level player.
                  </div>
                </div>
              </div>

              {/* Coach Response */}
              <div className="rounded-lg border border-gray-200 bg-gray-50 p-3">
                <div className="mb-2 flex items-center space-x-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white">
                    MB
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">Maria Brown</div>
                    <div className="text-xs text-gray-600">
                      Responded 2 hours ago
                    </div>
                  </div>
                </div>

                <div className="mb-2 text-sm text-gray-700">
                  "I'd be happy to help you with your tennis fundamentals. I
                  specialize in working with beginners and focus on proper
                  technique development."
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-sm">
                    <span className="text-gray-600">Rate:</span>
                    <span className="ml-1 font-medium text-gray-900">
                      $65/hour
                    </span>
                  </div>
                  <div className="flex space-x-2">
                    <button className="rounded bg-gray-300 px-3 py-1 text-xs text-gray-700 hover:bg-gray-400">
                      Decline
                    </button>
                    <button className="rounded bg-green-600 px-3 py-1 text-xs text-white hover:bg-green-700">
                      Accept
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Lesson Booking Summary */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Lesson Booking Process
        </h4>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div className="rounded-lg border border-purple-200 bg-purple-50 p-4">
            <div className="mb-3 flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-600 text-sm font-medium text-white">
                1
              </div>
              <h5 className="font-medium text-purple-900">Find by Coach</h5>
            </div>
            <div className="space-y-2 text-sm text-purple-800">
              <p>• Browse available coaches by sport</p>
              <p>• View detailed coach profiles and rates</p>
              <p>• Check real-time availability</p>
              <p>• Book directly with preferred coach</p>
            </div>
          </div>

          <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
            <div className="mb-3 flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white">
                2
              </div>
              <h5 className="font-medium text-blue-900">Find by Time</h5>
            </div>
            <div className="space-y-2 text-sm text-blue-800">
              <p>• Select preferred date and time first</p>
              <p>• System shows available coaches</p>
              <p>• Compare rates and experience</p>
              <p>• Book with best match</p>
            </div>
          </div>

          <div className="rounded-lg border border-green-200 bg-green-50 p-4">
            <div className="mb-3 flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white">
                3
              </div>
              <h5 className="font-medium text-green-900">Custom Request</h5>
            </div>
            <div className="space-y-2 text-sm text-green-800">
              <p>• Submit specific requirements</p>
              <p>• Set budget and preferences</p>
              <p>• Coaches respond with proposals</p>
              <p>• Choose from multiple options</p>
            </div>
          </div>
        </div>
      </div>

      {/* Custom Messages */}
      {lessonDescription.reservation_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Reservation Message
          </h4>
          <p className="text-sm text-gray-700">
            {lessonDescription.reservation_description}
          </p>
        </div>
      )}

      {lessonDescription.payment_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Payment Message
          </h4>
          <p className="text-sm text-gray-700">
            {lessonDescription.payment_description}
          </p>
        </div>
      )}
    </div>
  );
}
