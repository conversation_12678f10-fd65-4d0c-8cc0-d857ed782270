export default function HomeLoggedInPerspective({ club }) {
  return (
    <div className="mx-auto max-w-7xl space-y-6 p-3 md:p-6">
      {/* Enhanced Club Header Section */}
      {club?.home_image ? (
        <div className="">
          <div className="flex flex-col gap-4 md:flex-row md:gap-10">
            <div className="relative mb-4 flex-1 overflow-hidden rounded-xl shadow-md transition-all duration-300 hover:shadow-lg md:mb-6">
              <div className="relative h-[200px] w-full overflow-hidden md:h-[300px]">
                <img
                  src={club?.home_image}
                  alt="Club Home Image"
                  className="h-full w-full object-cover transition-transform duration-700 hover:scale-105"
                  loading="lazy"
                />
              </div>
            </div>

            <div className="flex-1">
              {club?.title && (
                <h1 className="mb-2 text-2xl font-bold tracking-tight md:text-3xl">
                  {club?.title}
                </h1>
              )}
              {club?.description && (
                <p className="max-w-2xl text-base md:text-lg">
                  {club?.description}
                </p>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="to-navy-900 mb-6 rounded-xl p-4 md:p-6">
          {club?.title && (
            <h1 className="mb-2 text-2xl font-bold tracking-tight text-white md:text-3xl">
              {club?.title}
            </h1>
          )}
          {club?.description && (
            <p className="max-w-2xl text-base text-gray-200 md:text-lg">
              {club?.description}
            </p>
          )}
        </div>
      )}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Upcoming Reservations Section */}
        <div className="h-fit rounded-2xl bg-white p-6 shadow-sm">
          <div className="mb-6 flex items-center gap-2">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M4.75 3C3.7835 3 3 3.7835 3 4.75V19.25C3 20.2165 3.7835 21 4.75 21H19.25C20.2165 21 21 20.2165 21 19.25V4.75C21 3.7835 20.2165 3 19.25 3H4.75ZM8 11C7.44772 11 7 11.4477 7 12C7 12.5523 7.44772 13 8 13C8.55228 13 9 12.5523 9 12C9 11.4477 8.55228 11 8 11ZM8 15C7.44772 15 7 15.4477 7 16C7 16.5523 7.44772 17 8 17C8.55228 17 9 16.5523 9 16C9 15.4477 8.55228 15 8 15ZM11 16C11 15.4477 11.4477 15 12 15C12.5523 15 13 15.4477 13 16C13 16.5523 12.5523 17 12 17C11.4477 17 11 16.5523 11 16ZM12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11ZM15 12C15 11.4477 15.4477 11 16 11C16.5523 11 17 11.4477 17 12C17 12.5523 16.5523 13 16 13C15.4477 13 15 12.5523 15 12ZM4.75 4.5C4.61193 4.5 4.5 4.61193 4.5 4.75V7H19.5V4.75C19.5 4.61193 19.3881 4.5 19.25 4.5H4.75Z"
                fill="#176448"
              />
            </svg>
            <h2 className="text-lg font-medium">Upcoming reservations</h2>
          </div>
          <div className="flex flex-col items-center justify-center py-8">
            <svg
              width="60"
              height="60"
              viewBox="0 0 60 60"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                x="0.5"
                y="0.5"
                width="59"
                height="59"
                rx="29.5"
                fill="url(#paint0_linear_158_11396)"
              />
              <rect
                x="0.5"
                y="0.5"
                width="59"
                height="59"
                rx="29.5"
                stroke="url(#paint1_linear_158_11396)"
              />
              <g filter="url(#filter0_d_158_11396)">
                <rect
                  x="8"
                  y="8"
                  width="44"
                  height="44"
                  rx="22"
                  fill="white"
                />
                <rect
                  x="8.5"
                  y="8.5"
                  width="43"
                  height="43"
                  rx="21.5"
                  stroke="#E2E4E9"
                />
                <path
                  d="M20 30C20 27.2819 21.0844 24.8171 22.8443 23.0146C25.0536 24.5496 26.5 27.1059 26.5 30C26.5 32.8941 25.0536 35.4504 22.8443 36.9854C21.0844 35.1829 20 32.7181 20 30Z"
                  fill="#868C98"
                />
                <path
                  d="M28 30C28 26.7284 26.4289 23.8237 24 21.9993C25.6713 20.7439 27.7488 20 30 20C32.2512 20 34.3287 20.7439 36 21.9993C33.5711 23.8237 32 26.7284 32 30C32 33.2716 33.5711 36.1763 36 38.0007C34.3287 39.2561 32.2512 40 30 40C27.7488 40 25.6713 39.2561 24 38.0007C26.4289 36.1763 28 33.2716 28 30Z"
                  fill="#868C98"
                />
                <path
                  d="M37.1557 23.0146C38.9156 24.8171 40 27.2819 40 30C40 32.7181 38.9156 35.1829 37.1557 36.9854C34.9464 35.4504 33.5 32.8941 33.5 30C33.5 27.1059 34.9464 24.5496 37.1557 23.0146Z"
                  fill="#868C98"
                />
              </g>
              <defs>
                <filter
                  id="filter0_d_158_11396"
                  x="4"
                  y="6"
                  width="52"
                  height="52"
                  filterUnits="userSpaceOnUse"
                  colorInterpolationFilters="sRGB"
                >
                  <feFlood floodOpacity="0" result="BackgroundImageFix" />
                  <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  />
                  <feOffset dy="2" />
                  <feGaussianBlur stdDeviation="2" />
                  <feColorMatrix
                    type="matrix"
                    values="0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"
                  />
                  <feBlend
                    mode="normal"
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_158_11396"
                  />
                  <feBlend
                    mode="normal"
                    in="SourceGraphic"
                    in2="effect1_dropShadow_158_11396"
                    result="shape"
                  />
                </filter>
                <linearGradient
                  id="paint0_linear_158_11396"
                  x1="30"
                  y1="0"
                  x2="30"
                  y2="60"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#E4E5E7" stopOpacity="0.48" />
                  <stop offset="1" stopColor="#F7F8F8" stopOpacity="0" />
                  <stop offset="1" stopColor="#E4E5E7" stopOpacity="0" />
                </linearGradient>
                <linearGradient
                  id="paint1_linear_158_11396"
                  x1="30"
                  y1="0"
                  x2="30"
                  y2="60"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#E4E5E7" />
                  <stop
                    offset="0.765625"
                    stopColor="#E4E5E7"
                    stopOpacity="0"
                  />
                </linearGradient>
              </defs>
            </svg>
            <p className="mb-4 text-gray-600">
              You have no upcoming reservations
            </p>
            <span className="font-medium text-blue-600 hover:text-blue-700">
              Reserve a court
            </span>
          </div>
        </div>

        {/* Open Requests Section */}
        <div className="max-h-fit rounded-lg bg-white p-6 shadow-sm">
          <div className="mb-6 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 2C9.51472 2 7.5 4.01472 7.5 6.5C7.5 8.98528 9.51472 11 12 11C14.4853 11 16.5 8.98528 16.5 6.5C16.5 4.01472 14.4853 2 12 2Z"
                  fill="#176448"
                />
                <path
                  d="M16.5081 13.8263C16.1908 14.2141 16.0005 14.7098 16.0005 15.25V16H15.2505C14.0078 16 13.0005 17.0074 13.0005 18.25C13.0005 19.4926 14.0078 20.5 15.2505 20.5H16.0005V21H5.59881C4.60008 21 3.69057 20.1119 3.9402 19.0012C4.7686 15.3152 8.21185 12.5 12.0004 12.5C13.6638 12.5 15.2115 12.9805 16.5081 13.8263Z"
                  fill="#176448"
                />
                <path
                  d="M19 15.25C19 14.8358 18.6642 14.5 18.25 14.5C17.8358 14.5 17.5 14.8358 17.5 15.25V17.5H15.25C14.8358 17.5 14.5 17.8358 14.5 18.25C14.5 18.6642 14.8358 19 15.25 19H17.5V21.25C17.5 21.6642 17.8358 22 18.25 22C18.6642 22 19 21.6642 19 21.25V19H21.25C21.6642 19 22 18.6642 22 18.25C22 17.8358 21.6642 17.5 21.25 17.5H19V15.25Z"
                  fill="#176448"
                />
              </svg>
              <h2 className="text-lg font-medium">Open requests</h2>
            </div>
          </div>
          <div className="flex flex-col items-center justify-center py-8">
            <svg
              width="60"
              height="60"
              viewBox="0 0 60 60"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                x="0.5"
                y="0.5"
                width="59"
                height="59"
                rx="29.5"
                fill="url(#paint0_linear_187_35666)"
              />
              <rect
                x="0.5"
                y="0.5"
                width="59"
                height="59"
                rx="29.5"
                stroke="url(#paint1_linear_187_35666)"
              />
              <g filter="url(#filter0_d_187_35666)">
                <rect
                  x="8"
                  y="8"
                  width="44"
                  height="44"
                  rx="22"
                  fill="white"
                />
                <rect
                  x="8.5"
                  y="8.5"
                  width="43"
                  height="43"
                  rx="21.5"
                  stroke="#E2E4E9"
                />
                <path
                  d="M30 20C27.5147 20 25.5 22.0147 25.5 24.5C25.5 26.9853 27.5147 29 30 29C32.4853 29 34.5 26.9853 34.5 24.5C34.5 22.0147 32.4853 20 30 20Z"
                  fill="#868C98"
                />
                <path
                  d="M34.5081 31.8263C34.1908 32.2141 34.0005 32.7098 34.0005 33.25V34H33.2505C32.0078 34 31.0005 35.0074 31.0005 36.25C31.0005 37.4926 32.0078 38.5 33.2505 38.5H34.0005V39H23.5988C22.6001 39 21.6906 38.1119 21.9402 37.0012C22.7686 33.3152 26.2118 30.5 30.0004 30.5C31.6638 30.5 33.2115 30.9805 34.5081 31.8263Z"
                  fill="#868C98"
                />
                <path
                  d="M37 33.25C37 32.8358 36.6642 32.5 36.25 32.5C35.8358 32.5 35.5 32.8358 35.5 33.25V35.5H33.25C32.8358 35.5 32.5 35.8358 32.5 36.25C32.5 36.6642 32.8358 37 33.25 37H35.5V39.25C35.5 39.6642 35.8358 40 36.25 40C36.6642 40 37 39.6642 37 39.25V37H39.25C39.6642 37 40 36.6642 40 36.25C40 35.8358 39.6642 35.5 39.25 35.5H37V33.25Z"
                  fill="#868C98"
                />
              </g>
              <defs>
                <filter
                  id="filter0_d_187_35666"
                  x="4"
                  y="6"
                  width="52"
                  height="52"
                  filterUnits="userSpaceOnUse"
                  colorInterpolationFilters="sRGB"
                >
                  <feFlood floodOpacity="0" result="BackgroundImageFix" />
                  <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  />
                  <feOffset dy="2" />
                  <feGaussianBlur stdDeviation="2" />
                  <feColorMatrix
                    type="matrix"
                    values="0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"
                  />
                  <feBlend
                    mode="normal"
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_187_35666"
                  />
                  <feBlend
                    mode="normal"
                    in="SourceGraphic"
                    in2="effect1_dropShadow_187_35666"
                    result="shape"
                  />
                </filter>
                <linearGradient
                  id="paint0_linear_187_35666"
                  x1="30"
                  y1="0"
                  x2="30"
                  y2="60"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#E4E5E7" stopOpacity="0.48" />
                  <stop offset="1" stopColor="#F7F8F8" stopOpacity="0" />
                  <stop offset="1" stopColor="#E4E5E7" stopOpacity="0" />
                </linearGradient>
                <linearGradient
                  id="paint1_linear_187_35666"
                  x1="30"
                  y1="0"
                  x2="30"
                  y2="60"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#E4E5E7" />
                  <stop
                    offset="0.765625"
                    stopColor="#E4E5E7"
                    stopOpacity="0"
                  />
                </linearGradient>
              </defs>
            </svg>
            <p className="mb-4 text-gray-600">
              There are no open requests
            </p>
            <span className="font-medium text-blue-600 hover:text-blue-700">
              Make request
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
