import React, { createContext, useContext, useEffect, useRef, useState, useCallback } from 'react';
import { useAuth } from 'Context/Auth';

const PushNotificationContext = createContext();

export const PushNotificationProvider = ({ children }) => {
  const [isSupported, setIsSupported] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscription, setSubscription] = useState(null);
  const [permission, setPermission] = useState('default');
  const { state: authState } = useAuth();
  
  const swRegistration = useRef(null);

  // Check if push notifications are supported
  useEffect(() => {
    const checkSupport = () => {
      const supported = 'serviceWorker' in navigator && 'PushManager' in window;
      setIsSupported(supported);
      
      if (supported) {
        setPermission(Notification.permission);
      }
    };

    checkSupport();
  }, []);

  // Register service worker and check existing subscription
  useEffect(() => {
    if (!isSupported || !authState.isAuthenticated) return;

    const registerSW = async () => {
      try {
        // Register service worker
        const registration = await navigator.serviceWorker.register('/sw.js');
        swRegistration.current = registration;
        
        console.log('🔧 Service Worker registered:', registration);

        // Check for existing subscription
        const existingSubscription = await registration.pushManager.getSubscription();
        if (existingSubscription) {
          setSubscription(existingSubscription);
          setIsSubscribed(true);
          console.log('📱 Existing push subscription found');
        }
      } catch (error) {
        console.error('❌ Service Worker registration failed:', error);
      }
    };

    registerSW();
  }, [isSupported, authState.isAuthenticated]);

  // Request notification permission
  const requestPermission = useCallback(async () => {
    if (!isSupported) {
      throw new Error('Push notifications are not supported');
    }

    try {
      const permission = await Notification.requestPermission();
      setPermission(permission);
      
      if (permission === 'granted') {
        console.log('✅ Notification permission granted');
        return true;
      } else {
        console.log('❌ Notification permission denied');
        return false;
      }
    } catch (error) {
      console.error('❌ Error requesting notification permission:', error);
      throw error;
    }
  }, [isSupported]);

  // Subscribe to push notifications
  const subscribe = useCallback(async () => {
    if (!swRegistration.current || !authState.user) {
      throw new Error('Service worker not registered or user not authenticated');
    }

    try {
      // Request permission if not granted
      if (permission !== 'granted') {
        const granted = await requestPermission();
        if (!granted) {
          throw new Error('Notification permission not granted');
        }
      }

      // Create subscription
      const sub = await swRegistration.current.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(
          // You'll need to replace this with your actual VAPID public key
          'BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9f8HnKJuOmqmkFWJ6swkuHQQU-YS0Q3_NdGrsp-HjGAJBWqz9SWM'
        )
      });

      setSubscription(sub);
      setIsSubscribed(true);

      // Send subscription to server
      await sendSubscriptionToServer(sub);
      
      console.log('✅ Push notification subscription successful');
      return sub;
    } catch (error) {
      console.error('❌ Push subscription failed:', error);
      throw error;
    }
  }, [permission, requestPermission, authState.user]);

  // Unsubscribe from push notifications
  const unsubscribe = useCallback(async () => {
    if (!subscription) {
      throw new Error('No active subscription');
    }

    try {
      await subscription.unsubscribe();
      await removeSubscriptionFromServer();
      
      setSubscription(null);
      setIsSubscribed(false);
      
      console.log('✅ Push notification unsubscription successful');
    } catch (error) {
      console.error('❌ Push unsubscription failed:', error);
      throw error;
    }
  }, [subscription]);

  // Send subscription to server
  const sendSubscriptionToServer = async (subscription) => {
    try {
      // You'll need to implement this API endpoint
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          subscription,
          user_id: authState.user
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save subscription to server');
      }
    } catch (error) {
      console.error('❌ Failed to send subscription to server:', error);
      throw error;
    }
  };

  // Remove subscription from server
  const removeSubscriptionFromServer = async () => {
    try {
      const response = await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          user_id: authState.user
        })
      });

      if (!response.ok) {
        throw new Error('Failed to remove subscription from server');
      }
    } catch (error) {
      console.error('❌ Failed to remove subscription from server:', error);
      throw error;
    }
  };

  // Show local notification (for testing)
  const showNotification = useCallback((title, options = {}) => {
    if (!isSupported || permission !== 'granted') {
      console.warn('⚠️ Cannot show notification: not supported or permission not granted');
      return;
    }

    const defaultOptions = {
      icon: '/courtmatchup-logo.png',
      badge: '/courtmatchup-logo.png',
      tag: 'courtmatchup-notification',
      requireInteraction: false,
      ...options
    };

    if (swRegistration.current) {
      swRegistration.current.showNotification(title, defaultOptions);
    } else {
      new Notification(title, defaultOptions);
    }
  }, [isSupported, permission]);

  const value = {
    isSupported,
    isSubscribed,
    subscription,
    permission,
    requestPermission,
    subscribe,
    unsubscribe,
    showNotification
  };

  return (
    <PushNotificationContext.Provider value={value}>
      {children}
    </PushNotificationContext.Provider>
  );
};

// Custom hook to use push notifications
export const usePushNotifications = () => {
  const context = useContext(PushNotificationContext);
  if (!context) {
    throw new Error('usePushNotifications must be used within a PushNotificationProvider');
  }
  return context;
};

// Utility function to convert VAPID key
function urlBase64ToUint8Array(base64String) {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}
